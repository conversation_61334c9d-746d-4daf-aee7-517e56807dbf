
package com.dc.summer.model.virtual;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Virtual model object
 */
public abstract class DBVObject implements DBSObject {

    static final Log log = Log.getLog(DBVObject.class);

    private DBVTransformSettings transformSettings;
    private Map<String, Object> properties;

    @Override
    public abstract DBVContainer getParentObject();

    @Override
    public boolean isPersisted() {
        return true;
    }

    public DBVTransformSettings getTransformSettings() {
        return transformSettings;
    }

    public void setTransformSettings(DBVTransformSettings transformSettings) {
        this.transformSettings = transformSettings;
    }

    abstract public boolean hasValuableData();

    /**
     * Property value can be String, Number, Boolean, List or Map
     * @param name property name
     */
    @Nullable
    public <T> T getProperty(@NotNull String name) {
        return CommonUtils.isEmpty(properties) ? null : (T) properties.get(name);
    }

    public void setProperty(String name, @Nullable Object value) {
        if (properties == null) {
            properties = new LinkedHashMap<>();
        }
        if (value == null) {
            properties.remove(name);
        } else {
            properties.put(name, value);
        }
    }

    @NotNull
    public Map<String, Object> getProperties() {
        return properties == null ? Collections.emptyMap() : properties;
    }

    protected void copyFrom(@NotNull DBVObject src) {
        if (!CommonUtils.isEmpty(src.properties)) {
            this.properties = new LinkedHashMap<>(src.properties);
        }
    }

    protected void loadPropertiesFrom(@NotNull Map<String, Object> map, String elemName) {
        properties = JSONUtils.deserializeProperties(map, elemName);
    }


    public void persistConfiguration() {
        DBPDataSourceContainer dataSource = getDataSourceContainer();
        if (dataSource != null) {
            dataSource.persistConfiguration();
        }
    }

    @Nullable
    public DBPDataSourceContainer getDataSourceContainer() {
        DBVContainer parentObject = getParentObject();
        return parentObject == null ? null : parentObject.getDataSourceContainer();
    }

    public DBPProject getProject() {
        DBPDataSourceContainer ds = getDataSourceContainer();
        return ds == null ? null : ds.getProject();
    }
}

package com.dc.parser.ext.mssql.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.model.statement.SQLStatement;

/**
 * SQLServer statement.
 */
public interface SQLServerStatement extends SQLStatement {
    
    @Override
    default DatabaseType getDatabaseType() {
        return TypedSPILoader.getService(DatabaseType.class, DatabaseType.Constant.SQL_SERVER);
    }
}

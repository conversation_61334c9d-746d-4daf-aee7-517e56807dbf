package com.dc.parser.ext.mssql.statement.dml;

import com.dc.parser.ext.mssql.statement.SQLServerStatement;
import com.dc.parser.model.segment.dml.hint.WithTableHintSegment;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * SQLServer select statement.
 */
@Setter
public final class SQLServerSelectStatement extends SelectStatement implements SQLServerStatement {
    
    private LimitSegment limit;

    private TableSegment intoSegment;
    
    private WithTableHintSegment withTableHintSegment;
    
    /**
     * Get order by segment.
     *
     * @return order by segment
     */
    public Optional<LimitSegment> getLimit() {
        return Optional.ofNullable(limit);
    }
    
    /**
     * Get into segment.
     *
     * @return into segment
     */
    public Optional<TableSegment> getIntoSegment() {
        return Optional.ofNullable(intoSegment);
    }
    
    /**
     * Get with table hint segment.
     *
     * @return with table hint segment.
     */
    public Optional<WithTableHintSegment> getWithTableHintSegment() {
        return Optional.ofNullable(withTableHintSegment);
    }
}

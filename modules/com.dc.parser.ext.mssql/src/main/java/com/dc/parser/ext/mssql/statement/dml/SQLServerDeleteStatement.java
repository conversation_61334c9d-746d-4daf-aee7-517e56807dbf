package com.dc.parser.ext.mssql.statement.dml;

import com.dc.parser.ext.mssql.statement.SQLServerStatement;
import com.dc.parser.model.segment.generic.OutputSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * SQLServer delete statement.
 */
@Setter
public final class SQLServerDeleteStatement extends DeleteStatement implements SQLServerStatement {
    
    private WithSegment withSegment;
    
    private OutputSegment outputSegment;
    
    /**
     * Get with segment.
     *
     * @return with segment.
     */
    public Optional<WithSegment> getWithSegment() {
        return Optional.ofNullable(withSegment);
    }
    
    /**
     * Get output segment.
     *
     * @return output segment.
     */
    public Optional<OutputSegment> getOutputSegment() {
        return Optional.ofNullable(outputSegment);
    }
}

package com.dc.parser.ext.mssql.statement.ddl;

import com.dc.parser.ext.mssql.statement.SQLServerStatement;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.statement.ddl.CreateTableStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;

/**
 * SQLServer create table statement.
 */
@Getter
@Setter
public final class SQLServerCreateTableStatement extends CreateTableStatement implements SQLServerStatement {
    
    private final List<ColumnSegment> columns = new LinkedList<>();
}

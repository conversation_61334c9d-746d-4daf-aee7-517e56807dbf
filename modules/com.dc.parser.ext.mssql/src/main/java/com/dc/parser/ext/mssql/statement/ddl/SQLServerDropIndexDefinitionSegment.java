package com.dc.parser.ext.mssql.statement.ddl;

import com.dc.parser.model.segment.ddl.index.DropIndexDefinitionSegment;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import lombok.Getter;

import java.util.Collection;

/**
 * SQLServer drop index definition segment.
 * //TODO segment 位置
 */
@Getter
public class SQLServerDropIndexDefinitionSegment extends DropIndexDefinitionSegment {
    private final Collection<IndexSegment> names;

    public SQLServerDropIndexDefinitionSegment(int startIndex, int stopIndex, Collection<IndexSegment> names) {
        super(startIndex, stopIndex, null);
        this.names = names;
    }
}

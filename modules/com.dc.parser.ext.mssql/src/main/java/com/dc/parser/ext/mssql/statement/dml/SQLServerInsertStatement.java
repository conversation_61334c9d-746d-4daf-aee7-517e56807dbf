package com.dc.parser.ext.mssql.statement.dml;

import com.dc.parser.ext.mssql.statement.SQLServerStatement;
import com.dc.parser.model.segment.dml.exec.ExecSegment;
import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.parser.model.segment.dml.hint.WithTableHintSegment;
import com.dc.parser.model.segment.generic.OutputSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.statement.dml.InsertStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * SQLServer insert statement.
 */
@Setter
public final class SQLServerInsertStatement extends InsertStatement implements SQLServerStatement {
    
    private WithSegment withSegment;
    
    private OutputSegment outputSegment;
    
    private ExecSegment execSegment;
    
    private WithTableHintSegment withTableHintSegment;
    
    private FunctionSegment rowSetFunctionSegment;

    private boolean defaultValues = false;
    
    /**
     * Get with segment.
     *
     * @return with segment.
     */
    public Optional<WithSegment> getWithSegment() {
        return Optional.ofNullable(withSegment);
    }
    
    /**
     * Get output segment.
     *
     * @return output segment.
     */
    public Optional<OutputSegment> getOutputSegment() {
        return Optional.ofNullable(outputSegment);
    }
    
    /**
     * Get execute segment.
     *
     * @return execute segment.
     */
    public Optional<ExecSegment> getExecSegment() {
        return Optional.ofNullable(execSegment);
    }
    
    /**
     * Get with table hint segment.
     *
     * @return with table hint segment.
     */
    public Optional<WithTableHintSegment> getWithTableHintSegment() {
        return Optional.ofNullable(withTableHintSegment);
    }
    
    /**
     * Get rowSet function segment.
     *
     * @return rowSet function segment.
     */
    public Optional<FunctionSegment> getRowSetFunctionSegment() {
        return Optional.ofNullable(rowSetFunctionSegment);
    }
}

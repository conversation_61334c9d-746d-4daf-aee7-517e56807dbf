package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterTriggerStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter trigger statement context.
 */
@Getter
public final class AlterTriggerStatementContext extends CommonSQLStatementContext {

    public AlterTriggerStatementContext(final AlterTriggerStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterTriggerStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType(SqlConstant.KEY_TRIGGER);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getTriggerNameSegment().getTriggerName().getValue());
        SchemaInfo info = OwnerUtil.extract(sqlStatement.getTriggerNameSegment().getOwner(), getDatabaseType(), currentDatabaseName);
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterTriggerStatement getSqlStatement() {
        return (AlterTriggerStatement) super.getSqlStatement();
    }
}

package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropPluggableDatabaseStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

/**
 * Drop pluggable database statement context.
 */
public final class DropPluggableDatabaseStatementContext extends CommonSQLStatementContext {

    public DropPluggableDatabaseStatementContext(final DropPluggableDatabaseStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropPluggableDatabaseStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType("PLUGGABLE_DATABASE");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropPluggableDatabaseStatement getSqlStatement() {
        return (DropPluggableDatabaseStatement) super.getSqlStatement();
    }
}

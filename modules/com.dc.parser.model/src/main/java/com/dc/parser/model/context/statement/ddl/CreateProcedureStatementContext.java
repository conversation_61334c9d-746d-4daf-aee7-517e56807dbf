package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.extractor.TableExtractor;
import com.dc.parser.model.segment.ddl.routine.RoutineBodySegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.CreateProcedureStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

import java.util.Collection;
import java.util.Collections;
import java.util.Optional;

/**
 * Create procedure statement context.
 */
@Getter
public final class CreateProcedureStatementContext extends CommonSQLStatementContext {

    private final TablesContext tablesContext;

    public CreateProcedureStatementContext(final CreateProcedureStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);
        Optional<RoutineBodySegment> routineBodySegment = sqlStatement.getRoutineBody();
        Collection<SimpleTableSegment> tables = routineBodySegment.map(optional -> new TableExtractor().extractExistTableFromRoutineBody(optional)).orElseGet(Collections::emptyList);
        tablesContext = new TablesContext(tables);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    public void extractSqlAuthModel(final CreateProcedureStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType(SqlConstant.PROCEDURE);
        sqlAuthModel.setDdlSubdivideOperation(OperationAuthConstant.create_procedure);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlStatement.getProcedureName().ifPresent(procedureNameSegment -> {
            sqlAuthModel.setName(procedureNameSegment.getName().getValue());
            SchemaInfo info = OwnerUtil.extract(procedureNameSegment.getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
        });
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CreateProcedureStatement getSqlStatement() {
        return (CreateProcedureStatement) super.getSqlStatement();
    }
}

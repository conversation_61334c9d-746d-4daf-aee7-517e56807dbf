package com.dc.parser.model.context.statement.tcl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.statement.tcl.LockStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Lock statement context.
 */
@Getter
public final class LockStatementContext extends CommonSQLStatementContext implements TableAvailable {

    private final TablesContext tablesContext;

    public LockStatementContext(final LockStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);
        tablesContext = new TablesContext(sqlStatement.getTables());

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    private void extractSqlAuthModel(final LockStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        sqlStatement.getTables().forEach(simpleTableSegment -> {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setOperation(SqlConstant.KEY_LOCK);
            sqlAuthModel.setType(SqlConstant.KEY_TABLE);
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(simpleTableSegment.getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
            addSqlAuthModel(sqlAuthModel);
        });
    }

    @Override
    public LockStatement getSqlStatement() {
        return (LockStatement) super.getSqlStatement();
    }
}

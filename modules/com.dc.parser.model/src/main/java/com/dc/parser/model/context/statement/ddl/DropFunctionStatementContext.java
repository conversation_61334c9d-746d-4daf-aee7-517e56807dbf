package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropFunctionStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Drop function statement context.
 */
@Getter
public final class DropFunctionStatementContext extends CommonSQLStatementContext {

    public DropFunctionStatementContext(final DropFunctionStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    public void extractSqlAuthModel(final DropFunctionStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.FUNCTION);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlStatement.getFunctionName().ifPresent(functionNameSegment -> {
            sqlAuthModel.setName(functionNameSegment.getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(functionNameSegment.getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
        });
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropFunctionStatement getSqlStatement() {
        return (DropFunctionStatement) super.getSqlStatement();
    }
}

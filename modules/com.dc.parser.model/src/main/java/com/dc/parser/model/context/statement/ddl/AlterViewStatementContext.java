package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.statement.dml.SelectStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.enums.SubqueryType;
import com.dc.parser.model.extractor.TableExtractor;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.AlterViewStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

import java.util.*;

/**
 * Alter view statement context.
 */
@Getter
public final class AlterViewStatementContext extends CommonSQLStatementContext implements TableAvailable {

    private final TablesContext tablesContext;

    private final SelectStatementContext selectStatementContext;

    public AlterViewStatementContext(final List<Object> params, final AlterViewStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);
        Collection<SimpleTableSegment> tables = new LinkedList<>();
        tables.add(sqlStatement.getView());
        Optional<SelectStatement> selectStatement = sqlStatement.getSelect();
        selectStatement.ifPresent(optional -> extractTables(optional, tables));
        sqlStatement.getRenameView().ifPresent(tables::add);
        tablesContext = new TablesContext(tables);
        selectStatementContext = selectStatement.map(optional -> createSelectStatementContext(params, optional, currentDatabaseName)).orElse(null);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    private void extractSqlAuthModel(final AlterViewStatement sqlStatement, final String currentDatabaseName) {
        // 构造鉴权模型
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType(SqlConstant.KEY_VIEW);
        sqlAuthModel.setName(sqlStatement.getView().getTableName().getIdentifier().getValue());
        SchemaInfo info = OwnerUtil.extract(sqlStatement.getView().getOwner(), getDatabaseType(), currentDatabaseName);
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        addSqlAuthModel(sqlAuthModel);
    }

    private SelectStatementContext createSelectStatementContext(final List<Object> params, final SelectStatement selectStatement, final String currentDatabaseName) {
        SelectStatementContext result = new SelectStatementContext(params, selectStatement, currentDatabaseName, Collections.emptyList());
        result.setSubqueryType(SubqueryType.VIEW_DEFINITION);
        return result;
    }

    private void extractTables(final SelectStatement selectStatement, final Collection<SimpleTableSegment> tables) {
        TableExtractor extractor = new TableExtractor();
        extractor.extractTablesFromSelect(selectStatement);
        tables.addAll(extractor.getRewriteTables());
    }

    /**
     * Get select statement context.
     *
     * @return select statement context
     */
    public Optional<SelectStatementContext> getSelectStatementContext() {
        return Optional.ofNullable(selectStatementContext);
    }

    @Override
    public AlterViewStatement getSqlStatement() {
        return (AlterViewStatement) super.getSqlStatement();
    }
}

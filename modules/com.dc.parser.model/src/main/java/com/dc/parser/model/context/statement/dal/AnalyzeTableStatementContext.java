package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dal.AnalyzeTableStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Analyze table statement context.
 */
@Getter
public final class AnalyzeTableStatementContext extends CommonSQLStatementContext implements TableAvailable {

    private final TablesContext tablesContext;

    public AnalyzeTableStatementContext(final AnalyzeTableStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);
        tablesContext = new TablesContext(sqlStatement.getTables());

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    public void extractSqlAuthModel(final AnalyzeTableStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        sqlStatement.getTables().forEach(simpleTableSegment -> {
            extractTable(currentDatabaseName, catalogName, simpleTableSegment, SqlConstant.KEY_INSERT);
            extractTable(currentDatabaseName, catalogName, simpleTableSegment, SqlConstant.KEY_SELECT);
        });
    }

    private void extractTable(String currentDatabaseName, String catalogName, SimpleTableSegment simpleTableSegment, String operation) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(operation);
        sqlAuthModel.setType(SqlConstant.KEY_TABLE);
        sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
        SchemaInfo info = OwnerUtil.extract(simpleTableSegment.getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AnalyzeTableStatement getSqlStatement() {
        return (AnalyzeTableStatement) super.getSqlStatement();
    }
}

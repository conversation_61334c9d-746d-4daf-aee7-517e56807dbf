package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.IndexAvailable;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.AlterIndexStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;

/**
 * Alter index statement context.
 */
@Getter
public final class AlterIndexStatementContext extends CommonSQLStatementContext implements TableAvailable, IndexAvailable {

    private final TablesContext tablesContext;

    public AlterIndexStatementContext(final AlterIndexStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);
        SimpleTableSegment simpleTableSegment = sqlStatement.getSimpleTable().orElse(null);
        tablesContext = new TablesContext(simpleTableSegment);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    private void extractSqlAuthModel(final AlterIndexStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType(SqlConstant.KEY_INDEX);

        sqlStatement.getIndex().ifPresent(indexSegment -> {
            SchemaInfo info = OwnerUtil.extract(indexSegment.getOwner(), getDatabaseType(), currentDatabaseName);
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
            sqlAuthModel.setName(indexSegment.getIndexName().getIdentifier().getValue());
        });

        sqlStatement.getSimpleTable().ifPresent(simpleTableSegment -> {
            SchemaInfo info = OwnerUtil.extract(simpleTableSegment.getOwner(), getDatabaseType(), currentDatabaseName);
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
        });

        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterIndexStatement getSqlStatement() {
        return (AlterIndexStatement) super.getSqlStatement();
    }

    @Override
    public Collection<IndexSegment> getIndexes() {
        Collection<IndexSegment> result = new LinkedList<>();
        if (getSqlStatement().getIndex().isPresent()) {
            result.add(getSqlStatement().getIndex().get());
        }
        getSqlStatement().getRenameIndex().ifPresent(result::add);
        return result;
    }

    @Override
    public Collection<ColumnSegment> getIndexColumns() {
        return Collections.emptyList();
    }
}

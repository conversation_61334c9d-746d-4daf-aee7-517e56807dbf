package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.AlterMaterializedViewStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter materialized view statement context.
 */
@Getter
public final class AlterMaterializedViewStatementContext extends CommonSQLStatementContext {

    public AlterMaterializedViewStatementContext(final AlterMaterializedViewStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterMaterializedViewStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType(SqlConstant.KEY_VIEW);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        SimpleTableSegment view = sqlStatement.getView();
        sqlAuthModel.setName(view.getTableName().getIdentifier().getValue());
        SchemaInfo info = OwnerUtil.extract(view.getOwner(), getDatabaseType(), currentDatabaseName);
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterMaterializedViewStatement getSqlStatement() {
        return (AlterMaterializedViewStatement) super.getSqlStatement();
    }
}

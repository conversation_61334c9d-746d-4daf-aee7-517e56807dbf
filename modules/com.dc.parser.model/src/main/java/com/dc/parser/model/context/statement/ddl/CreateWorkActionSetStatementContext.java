package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.CreateWorkActionSetStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

/**
 * Create work action set statement context.
 */
public final class CreateWorkActionSetStatementContext extends CommonSQLStatementContext {

    public CreateWorkActionSetStatementContext(final CreateWorkActionSetStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final CreateWorkActionSetStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType("WORK_ACTION_SET");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }


    @Override
    public CreateWorkActionSetStatement getSqlStatement() {
        return (CreateWorkActionSetStatement) super.getSqlStatement();
    }
}

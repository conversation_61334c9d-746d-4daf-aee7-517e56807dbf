package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.CreateSynonymStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Create synonym statement context.
 */
@Getter
public final class CreateSynonymStatementContext extends CommonSQLStatementContext {

    public CreateSynonymStatementContext(final CreateSynonymStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    public void extractSqlAuthModel(final CreateSynonymStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType(SqlConstant.KEY_SYNONYM);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getSynonymNameSegment().getName().getValue());
        SchemaInfo info = OwnerUtil.extract(sqlStatement.getSynonymNameSegment().getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CreateSynonymStatement getSqlStatement() {
        return (CreateSynonymStatement) super.getSqlStatement();
    }
}

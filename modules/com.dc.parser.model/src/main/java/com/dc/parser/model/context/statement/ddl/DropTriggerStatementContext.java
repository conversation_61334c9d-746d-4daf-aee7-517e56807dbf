package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropTriggerStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Drop trigger statement context.
 */
@Getter
public final class DropTriggerStatementContext extends CommonSQLStatementContext {

    public DropTriggerStatementContext(final DropTriggerStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    public void extractSqlAuthModel(final DropTriggerStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.KEY_TRIGGER);
        sqlAuthModel.setName(sqlStatement.getTriggerNameSegment().getTriggerName().getValue());
        SchemaInfo info = OwnerUtil.extract(sqlStatement.getTriggerNameSegment().getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropTriggerStatement getSqlStatement() {
        return (DropTriggerStatement) super.getSqlStatement();
    }
}

package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.extractor.TableExtractor;
import com.dc.parser.model.segment.ddl.routine.RoutineBodySegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.CreateFunctionStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

import java.util.Collection;
import java.util.Collections;
import java.util.Optional;

/**
 * Create function statement context.
 */
@Getter
public final class CreateFunctionStatementContext extends CommonSQLStatementContext {

    private final TablesContext tablesContext;

    public CreateFunctionStatementContext(final CreateFunctionStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);
        Optional<RoutineBodySegment> routineBodySegment = sqlStatement.getRoutineBody();
        Collection<SimpleTableSegment> tables = routineBodySegment.map(optional -> new TableExtractor().extractExistTableFromRoutineBody(optional)).orElseGet(Collections::emptyList);
        tablesContext = new TablesContext(tables);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    public void extractSqlAuthModel(final CreateFunctionStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType(SqlConstant.FUNCTION);
        sqlAuthModel.setDdlSubdivideOperation(OperationAuthConstant.create_function);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlStatement.getFunctionName().ifPresent(functionNameSegment -> {
            sqlAuthModel.setName(functionNameSegment.getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(functionNameSegment.getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
        });
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CreateFunctionStatement getSqlStatement() {
        return (CreateFunctionStatement) super.getSqlStatement();
    }
}

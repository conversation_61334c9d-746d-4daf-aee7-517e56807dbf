package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.DropMaterializedViewStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Drop materialized view statement context.
 */
@Getter
public final class DropMaterializedViewStatementContext extends CommonSQLStatementContext {

    public DropMaterializedViewStatementContext(final DropMaterializedViewStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    public void extractSqlAuthModel(final DropMaterializedViewStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.KEY_VIEW);
        SimpleTableSegment view = sqlStatement.getView();
        sqlAuthModel.setName(view.getTableName().getIdentifier().getValue());
        SchemaInfo info = OwnerUtil.extract(view.getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropMaterializedViewStatement getSqlStatement() {
        return (DropMaterializedViewStatement) super.getSqlStatement();
    }
}

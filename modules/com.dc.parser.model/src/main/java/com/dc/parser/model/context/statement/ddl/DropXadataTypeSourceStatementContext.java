package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropXadataTypeSourceStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

/**
 * Drop xa data type source statement context.
 */
public final class DropXadataTypeSourceStatementContext extends CommonSQLStatementContext {

    public DropXadataTypeSourceStatementContext(final DropXadataTypeSourceStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropXadataTypeSourceStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType("XA_DATA_TYPE");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropXadataTypeSourceStatement getSqlStatement() {
        return (DropXadataTypeSourceStatement) super.getSqlStatement();
    }
}

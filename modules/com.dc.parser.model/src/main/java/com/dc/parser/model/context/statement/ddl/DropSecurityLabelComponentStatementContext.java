package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropSecurityLabelComponentStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

/**
 * Drop security label component statement context.
 */
public final class DropSecurityLabelComponentStatementContext extends CommonSQLStatementContext {

    public DropSecurityLabelComponentStatementContext(final DropSecurityLabelComponentStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropSecurityLabelComponentStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType("SECURITY_LABEL_COMPONENT");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropSecurityLabelComponentStatement getSqlStatement() {
        return (DropSecurityLabelComponentStatement) super.getSqlStatement();
    }
}

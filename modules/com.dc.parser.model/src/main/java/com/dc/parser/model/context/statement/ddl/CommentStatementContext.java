package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.CommentStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

import java.util.Collections;

/**
 * Comment statement context.
 */
@Getter
public final class CommentStatementContext extends CommonSQLStatementContext implements TableAvailable {

    private final TablesContext tablesContext;

    public CommentStatementContext(final CommentStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);
        tablesContext = new TablesContext(null == sqlStatement.getTable() ? Collections.emptyList() : Collections.singletonList(sqlStatement.getTable()));

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    public void extractSqlAuthModel(final CommentStatement sqlStatement, final String currentDatabaseName) {
        extractSqlAuthModel(sqlStatement, currentDatabaseName, currentDatabaseName);
    }

    public void extractSqlAuthModel(final CommentStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_COMMENT);
        if (sqlStatement.getColumn() != null) {
            sqlAuthModel.setType(SqlConstant.KEY_COLUMN);
        } else if (sqlStatement.getCommentObjectType().isPresent()) {
            sqlAuthModel.setType(sqlStatement.getCommentObjectType().get().name());
        }
        SimpleTableSegment tableSegment = sqlStatement.getTable();
        if (tableSegment != null) {
            sqlAuthModel.setName(tableSegment.getTableName().getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(tableSegment.getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
        } else {
            // tableSegment 为空时，使用默认的 catalogName 和 schemaName
            sqlAuthModel.setCatalogName(catalogName);
            sqlAuthModel.setSchemaName(currentDatabaseName);
        }
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CommentStatement getSqlStatement() {
        return (CommentStatement) super.getSqlStatement();
    }
}

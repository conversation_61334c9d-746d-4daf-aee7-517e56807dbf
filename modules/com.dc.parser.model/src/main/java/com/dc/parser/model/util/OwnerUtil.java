package com.dc.parser.model.util;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.segment.generic.OwnerSegment;

import java.util.Optional;

/**
 * OwnerSegment解析工具类
 */
public class OwnerUtil {

    /**
     * 从ownerSegment中提取catalogName和schemaName
     *
     * @param ownerSegment        可能为空的ownerSegment
     * @param databaseType        数据库类型
     * @param currentDatabaseName 当前数据库名
     * @param catalogName         catalog名称（可为null）
     * @return SchemaInfo包含解析后的catalog和schema信息
     */
    public static SchemaInfo extract(Optional<OwnerSegment> ownerSegment,
                                     DatabaseType databaseType,
                                     String currentDatabaseName,
                                     String catalogName) {

        // 无owner的情况，使用默认值
        return ownerSegment.map(segment -> extractInternal(segment, databaseType, catalogName))
                .orElseGet(() -> new SchemaInfo(catalogName, currentDatabaseName));

    }

    /**
     * 从ownerSegment中提取catalogName和schemaName (用于StatementContext场景)
     *
     * @param ownerSegment        可能为空的ownerSegment
     * @param databaseType        数据库类型
     * @param currentDatabaseName 当前数据库名
     * @return SchemaInfo包含解析后的catalog和schema信息
     */
    public static SchemaInfo extract(Optional<OwnerSegment> ownerSegment,
                                     DatabaseType databaseType,
                                     String currentDatabaseName) {

        return extract(ownerSegment, databaseType, currentDatabaseName, currentDatabaseName);
    }

    /**
     * 内部方法：处理ownerSegment解析的核心逻辑
     */
    private static SchemaInfo extractInternal(OwnerSegment ownerSegment, DatabaseType databaseType, String catalogName) {
        boolean isCatalogDb = isCatalogDatabase(databaseType.getType());

        // 处理嵌套的ownerSegment
        if (isCatalogDb && ownerSegment.getOwner().isPresent()) {
            // 三层架构：owner.owner = catalogName, owner = schemaName
            String catalog = ownerSegment.getOwner().get().getIdentifier().getValue();
            String schema = ownerSegment.getIdentifier().getValue();
            return new SchemaInfo(catalog, schema);
        } else {
            // 两层架构或只有一层owner：owner = schemaName
            String schema = ownerSegment.getIdentifier().getValue();
            return new SchemaInfo(catalogName, schema);
        }
    }

    public static boolean isCatalogDatabase(DatabaseType.Constant type) {
        return type == DatabaseType.Constant.PG_SQL ||
                type == DatabaseType.Constant.GAUSSDB ||
                type == DatabaseType.Constant.SQL_SERVER;
    }
}
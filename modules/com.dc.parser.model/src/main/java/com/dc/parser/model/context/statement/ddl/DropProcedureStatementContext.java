package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropProcedureStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Drop procedure statement context.
 */
@Getter
public final class DropProcedureStatementContext extends CommonSQLStatementContext {

    public DropProcedureStatementContext(final DropProcedureStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName, catalogName);
    }

    public void extractSqlAuthModel(final DropProcedureStatement sqlStatement, final String currentDatabaseName, final String catalogName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.PROCEDURE);
        sqlAuthModel.setName(sqlStatement.getProcedureNameSegment().getName().getValue());
        SchemaInfo info = OwnerUtil.extract(sqlStatement.getProcedureNameSegment().getOwner(), getDatabaseType(), currentDatabaseName, catalogName);
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropProcedureStatement getSqlStatement() {
        return (DropProcedureStatement) super.getSqlStatement();
    }
}

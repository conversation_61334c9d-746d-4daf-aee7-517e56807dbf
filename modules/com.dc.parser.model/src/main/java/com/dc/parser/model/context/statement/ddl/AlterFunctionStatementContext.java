package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterFunctionStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter function statement context.
 */
@Getter
public final class AlterFunctionStatementContext extends CommonSQLStatementContext {

    public AlterFunctionStatementContext(final AlterFunctionStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterFunctionStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType(SqlConstant.FUNCTION);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlStatement.getFunctionName().ifPresent(functionNameSegment -> {
            sqlAuthModel.setName(functionNameSegment.getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(functionNameSegment.getOwner(), getDatabaseType(), currentDatabaseName);
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
        });
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterFunctionStatement getSqlStatement() {
        return (AlterFunctionStatement) super.getSqlStatement();
    }
}

package com.dc.parser.model.statement.dcl;

import com.dc.parser.model.segment.dcl.DirectoryNameSegment;
import com.dc.parser.model.segment.dcl.OracleRoleOrPrivilegeSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Data;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.Optional;

/**
 * Revoke statement.
 */
@Data
public abstract class RevokeStatement extends AbstractSQLStatement implements DCLStatement {
    
    private final Collection<SimpleTableSegment> tables = new LinkedList<>();

    private final Collection<IdentifierValue> revokeFromUsers = new LinkedList<>();

    public Optional<SimpleTableSegment> getTableSegment() {
        return Optional.empty();
    }

    public Optional<DirectoryNameSegment> getDirectoryNameSegment() {
        return Optional.empty();
    }

    public Collection<OracleRoleOrPrivilegeSegment> getOracleRoleOrPrivileges() {
        return Collections.emptyList();
    }

}

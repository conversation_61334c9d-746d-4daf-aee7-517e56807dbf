package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.dal.VariableAssignSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;

import java.util.LinkedList;
import java.util.List;

/**
 * Set statement.
 */
@Getter
public abstract class SetStatement extends AbstractSQLStatement implements DALStatement {
    
    private final List<VariableAssignSegment> variableAssigns = new LinkedList<>();

    public String switchKeyword() {
        return null;
    }

}

package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropSequenceStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Drop sequence statement context.
 */
@Getter
public final class DropSequenceStatementContext extends CommonSQLStatementContext {

    public DropSequenceStatementContext(final DropSequenceStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropSequenceStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType(SqlConstant.KEY_SEQUENCE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getSequenceName().getValue());
        SchemaInfo info = OwnerUtil.extract(sqlStatement.getOwner(), getDatabaseType(), currentDatabaseName);
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropSequenceStatement getSqlStatement() {
        return (DropSequenceStatement) super.getSqlStatement();
    }
}

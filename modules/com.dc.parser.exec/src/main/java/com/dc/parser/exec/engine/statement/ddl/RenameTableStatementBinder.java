package com.dc.parser.exec.engine.statement.ddl;

import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.RenameTableStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.SneakyThrows;

/**
 * Rename table statement binder.
 */
public final class RenameTableStatementBinder implements SQLStatementBinder<RenameTableStatement> {

    @Override
    public RenameTableStatement bind(final RenameTableStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        RenameTableStatement result = copy(sqlStatement);
//        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
//        sqlStatement.getRenameTables().forEach(each -> result.getRenameTables().add(RenameTableDefinitionSegmentBinder.bind(each, binderContext, tableBinderContexts)));
        return result;
    }

    public void extractSqlAuthModel(final RenameTableStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        sqlStatement.getRenameTables().forEach(renameTableDefinitionSegment -> {
            SimpleTableSegment simpleTableSegment = renameTableDefinitionSegment.getTable();
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setType(SqlConstant.KEY_TABLE);
            sqlAuthModel.setOperation(SqlConstant.KEY_RENAME);
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(simpleTableSegment.getOwner(), binderContext.getMetaData().getProtocolType(), binderContext.getCurrentDatabaseName(), binderContext.getCatalogName());
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
            binderContext.addSqlAuthModel(sqlAuthModel);
        });
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static RenameTableStatement copy(final RenameTableStatement sqlStatement) {
        RenameTableStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}

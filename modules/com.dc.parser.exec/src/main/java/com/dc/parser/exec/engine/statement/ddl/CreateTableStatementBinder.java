package com.dc.parser.exec.engine.statement.ddl;

import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.exec.engine.statement.dml.SelectStatementBinder;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.CreateTableStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.SneakyThrows;

/**
 * Create table statement binder.
 */
public final class CreateTableStatementBinder implements SQLStatementBinder<CreateTableStatement> {

    @Override
    public CreateTableStatement bind(final CreateTableStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        CreateTableStatement result = copy(sqlStatement);
//        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
//        result.setTable(SimpleTableSegmentBinder.bind(sqlStatement.getTable(), binderContext, tableBinderContexts));
        sqlStatement.getSelectStatement().ifPresent(optional -> {
            binderContext.setOperation(SqlConstant.KEY_SELECT);
            SqlActionModel sqlActionModel = binderContext.getSqlActionModel();
            sqlActionModel.setCreateTableAsSelect(true);
            result.setSelectStatement(new SelectStatementBinder().bind(optional, binderContext));
        });
        return result;
    }

    public void extractSqlAuthModel(final CreateTableStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setType(SqlConstant.KEY_TABLE);
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        SimpleTableSegment view = sqlStatement.getTable();
        sqlAuthModel.setName(view.getTableName().getIdentifier().getValue());
        SchemaInfo info = OwnerUtil.extract(view.getOwner(), binderContext.getMetaData().getProtocolType(), binderContext.getCurrentDatabaseName(), binderContext.getCatalogName());
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        binderContext.addSqlAuthModel(sqlAuthModel);
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static CreateTableStatement copy(final CreateTableStatement sqlStatement) {
        CreateTableStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.setIfNotExists(sqlStatement.isIfNotExists());
        result.getColumns().addAll(sqlStatement.getColumns());
        sqlStatement.getLikeTable().ifPresent(result::setLikeTable);
        sqlStatement.getCreateTableOption().ifPresent(result::setCreateTableOption);
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        result.setTable(sqlStatement.getTable());
        return result;
    }
}

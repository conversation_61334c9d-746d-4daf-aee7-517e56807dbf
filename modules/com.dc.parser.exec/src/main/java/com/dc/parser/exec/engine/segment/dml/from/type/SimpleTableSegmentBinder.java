package com.dc.parser.exec.engine.segment.dml.from.type;

import com.dc.infra.database.DialectDatabaseMetaData;
import com.dc.infra.database.enums.QuoteCharacter;
import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.database.type.DatabaseTypeRegistry;
import com.dc.infra.exception.ShardingSpherePreconditions;
import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.context.type.SimpleTableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.util.SubqueryTableBindUtils;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.exception.TableExistsException;
import com.dc.parser.model.exception.TableNotFoundException;
import com.dc.parser.model.metadata.database.schema.ShardingSphereColumn;
import com.dc.parser.model.metadata.database.schema.ShardingSphereSchema;
import com.dc.parser.model.metadata.database.schema.ShardingSphereTable;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.table.RenameTableDefinitionSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.item.ColumnProjectionSegment;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.segment.generic.bounded.ColumnSegmentBoundInfo;
import com.dc.parser.model.segment.generic.bounded.TableSegmentBoundInfo;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.statement.ddl.*;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;

/**
 * Simple table segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SimpleTableSegmentBinder {

    /**
     * Bind simple table segment.
     *
     * @param segment             simple table segment
     * @param binderContext       SQL statement binder context
     * @param tableBinderContexts table binder contexts
     * @return bound simple table segment
     */
    public static SimpleTableSegment bind(final SimpleTableSegment segment, final SQLStatementBinderContext binderContext,
                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts) {
        fillPivotColumnNamesInBinderContext(segment, binderContext);
        IdentifierValue databaseName = getDatabaseName(segment, binderContext);
        IdentifierValue schemaName = getSchemaName(segment, binderContext, databaseName);
        IdentifierValue tableName = segment.getTableName().getIdentifier();
        ShardingSphereSchema schema = binderContext.getMetaData().getDatabase(databaseName).getSchema(schemaName);
//        checkTableExists(binderContext, schema, schemaName.getValue(), tableName);
        tableBinderContexts.put(new CaseInsensitiveString(segment.getAliasName().orElseGet(tableName::getValue)),
                createSimpleTableBinderContext(segment, schema, databaseName, schemaName, binderContext));
        TableNameSegment tableNameSegment = new TableNameSegment(segment.getTableName().getStartIndex(), segment.getTableName().getStopIndex(), tableName);
        tableNameSegment.setTableBoundInfo(new TableSegmentBoundInfo(databaseName, schemaName));
        SimpleTableSegment result = new SimpleTableSegment(tableNameSegment);
        segment.getOwner().ifPresent(result::setOwner);
        segment.getAliasSegment().ifPresent(result::setAlias);

        if (segment.getDbLink().isPresent()) {
            SqlActionModel sqlActionModel = binderContext.getSqlActionModel();
            sqlActionModel.setContainsDBLink(true);
        }
        return result;
    }

    private static void fillPivotColumnNamesInBinderContext(final SimpleTableSegment segment, final SQLStatementBinderContext binderContext) {
        segment.getPivot().ifPresent(optional -> optional.getPivotColumns().forEach(each -> binderContext.getPivotColumnNames().add(each.getIdentifier().getValue())));
    }

    private static IdentifierValue getDatabaseName(final SimpleTableSegment segment, final SQLStatementBinderContext binderContext) {
        DatabaseType databaseType = binderContext.getSqlStatement().getDatabaseType();
        String catalogName = binderContext.getCatalogName();
        String currentDatabaseName = binderContext.getCurrentDatabaseName();

        // 使用OwnerUtil.extract正确解析catalog和schema信息
        SchemaInfo schemaInfo = OwnerUtil.extract(segment.getOwner(), databaseType, currentDatabaseName, catalogName);

        // 对于三层数据库，返回catalogName；对于二层数据库，返回schemaName（实际上是databaseName）
        String databaseName = schemaInfo.getCatalogName() != null ? schemaInfo.getCatalogName() : schemaInfo.getSchemaName();

        DialectDatabaseMetaData dialectDatabaseMetaData = new DatabaseTypeRegistry(databaseType).getDialectDatabaseMetaData();
        return new IdentifierValue(databaseName, dialectDatabaseMetaData.getQuoteCharacter());
    }

    private static IdentifierValue getSchemaName(final SimpleTableSegment segment, final SQLStatementBinderContext binderContext, final IdentifierValue databaseName) {
        // 直接调用单参数版本，不做实际检查schema是否存在
        return getSchemaName(segment, binderContext);
    }

    private static IdentifierValue getSchemaName(final SimpleTableSegment segment, final SQLStatementBinderContext binderContext) {
        DatabaseType databaseType = binderContext.getSqlStatement().getDatabaseType();
        String catalogName = binderContext.getCatalogName();
        String currentDatabaseName = binderContext.getCurrentDatabaseName();

        // 使用OwnerUtil.extract正确解析catalog和schema信息
        SchemaInfo schemaInfo = OwnerUtil.extract(segment.getOwner(), databaseType, currentDatabaseName, catalogName);

        DialectDatabaseMetaData dialectDatabaseMetaData = new DatabaseTypeRegistry(databaseType).getDialectDatabaseMetaData();
        return new IdentifierValue(schemaInfo.getSchemaName(), dialectDatabaseMetaData.getQuoteCharacter());
    }

    private static void checkTableExists(final SQLStatementBinderContext binderContext, final ShardingSphereSchema schema, final String schemaName, final IdentifierValue tableName) {
        // TODO refactor table exists check with spi @duanzhengqiang
        if (binderContext.getSqlStatement() instanceof CreateTableStatement && isCreateTable(((CreateTableStatement) binderContext.getSqlStatement()).getTable(), tableName.getValue())) {
            ShardingSpherePreconditions.checkState(((CreateTableStatement) binderContext.getSqlStatement()).isIfNotExists() || !schema.containsTable(tableName), () -> new TableExistsException(tableName.getValue()));
            return;
        }
        if (binderContext.getSqlStatement() instanceof AlterTableStatement && isRenameTable((AlterTableStatement) binderContext.getSqlStatement(), tableName.getValue())) {
            ShardingSpherePreconditions.checkState(!schema.containsTable(tableName), () -> new TableExistsException(tableName.getValue()));
            return;
        }
        if (binderContext.getSqlStatement() instanceof DropTableStatement) {
            ShardingSpherePreconditions.checkState(((DropTableStatement) binderContext.getSqlStatement()).isIfExists() || schema.containsTable(tableName), () -> new TableNotFoundException(tableName.getValue()));
            return;
        }
        if (binderContext.getSqlStatement() instanceof RenameTableStatement && isRenameTable((RenameTableStatement) binderContext.getSqlStatement(), tableName.getValue())) {
            ShardingSpherePreconditions.checkState(!schema.containsTable(tableName), () -> new TableExistsException(tableName.getValue()));
            return;
        }
        if (binderContext.getSqlStatement() instanceof CreateViewStatement && isCreateTable(((CreateViewStatement) binderContext.getSqlStatement()).getView(), tableName.getValue())) {
            ShardingSpherePreconditions.checkState(((CreateViewStatement) binderContext.getSqlStatement()).isReplaceView() || !schema.containsTable(tableName), () -> new TableExistsException(tableName.getValue()));
            return;
        }
        if (binderContext.getSqlStatement() instanceof AlterViewStatement && isRenameView((AlterViewStatement) binderContext.getSqlStatement(), tableName.getValue())) {
            ShardingSpherePreconditions.checkState(!schema.containsTable(tableName), () -> new TableExistsException(tableName.getValue()));
            return;
        }
        if (binderContext.getSqlStatement() instanceof DropViewStatement) {
            ShardingSpherePreconditions.checkState(((DropViewStatement) binderContext.getSqlStatement()).isIfExists() || schema.containsTable(tableName), () -> new TableNotFoundException(tableName.getValue()));
            return;
        }
        if ("DUAL".equalsIgnoreCase(tableName.getValue())) {
            return;
        }
        if (binderContext.getExternalTableBinderContexts().containsKey(new CaseInsensitiveString(tableName.getValue()))) {
            return;
        }
        if (binderContext.getCommonTableExpressionsSegmentsUniqueAliases().contains(tableName)) {
            return;
        }
        ShardingSpherePreconditions.checkState(schema.containsTable(tableName), () -> new TableNotFoundException(tableName.getValue()));
    }

    private static boolean isCreateTable(final SimpleTableSegment simpleTableSegment, final String tableName) {
        return simpleTableSegment.getTableName().getIdentifier().getValue().equalsIgnoreCase(tableName);
    }

    private static boolean isRenameTable(final AlterTableStatement alterTableStatement, final String tableName) {
        return alterTableStatement.getRenameTable().isPresent() && alterTableStatement.getRenameTable().get().getTableName().getIdentifier().getValue().equalsIgnoreCase(tableName);
    }

    private static boolean isRenameTable(final RenameTableStatement renameTableStatement, final String tableName) {
        for (RenameTableDefinitionSegment each : renameTableStatement.getRenameTables()) {
            if (each.getRenameTable().getTableName().getIdentifier().getValue().equalsIgnoreCase(tableName)) {
                return true;
            }
        }
        return false;
    }

    private static boolean isRenameView(final AlterViewStatement alterViewStatement, final String tableName) {
        return alterViewStatement.getRenameView().isPresent() && alterViewStatement.getRenameView().get().getTableName().getIdentifier().getValue().equalsIgnoreCase(tableName);
    }

    private static SimpleTableSegmentBinderContext createSimpleTableBinderContext(final SimpleTableSegment segment, final ShardingSphereSchema schema, final IdentifierValue databaseName,
                                                                                  final IdentifierValue schemaName, final SQLStatementBinderContext binderContext) {
        IdentifierValue tableName = segment.getTableName().getIdentifier();
        if (binderContext.getMetaData().getDatabase(databaseName).getSchema(schemaName).containsTable(tableName) && !binderContext.getCommonTableExpressionsSegmentsUniqueAliases().contains(tableName.getValue())) {
            return createSimpleTableSegmentBinderContextWithMetaData(segment, schema, databaseName, schemaName, binderContext, tableName);
        }
        if (binderContext.getSqlStatement() instanceof CreateTableStatement) {
            return new SimpleTableSegmentBinderContext(createProjectionSegments((CreateTableStatement) binderContext.getSqlStatement(), databaseName, schemaName, tableName));
        }
        CaseInsensitiveString caseInsensitiveTableName = new CaseInsensitiveString(tableName.getValue());
        if (binderContext.getExternalTableBinderContexts().containsKey(caseInsensitiveTableName)) {
            TableSegmentBinderContext tableSegmentBinderContext = binderContext.getExternalTableBinderContexts().get(caseInsensitiveTableName).iterator().next();
            return new SimpleTableSegmentBinderContext(
                    SubqueryTableBindUtils.createSubqueryProjections(tableSegmentBinderContext.getProjectionSegments(), tableName, binderContext.getSqlStatement().getDatabaseType()));
        }
        return new SimpleTableSegmentBinderContext(Collections.emptyList());
    }

    private static Collection<ProjectionSegment> createProjectionSegments(final CreateTableStatement sqlStatement, final IdentifierValue databaseName,
                                                                          final IdentifierValue schemaName, final IdentifierValue tableName) {
        Collection<ProjectionSegment> result = new LinkedList<>();
        for (ColumnDefinitionSegment each : sqlStatement.getRelationalTable().get().getColumnDefinitions()) {
            each.getColumnName().setColumnBoundInfo(new ColumnSegmentBoundInfo(new TableSegmentBoundInfo(databaseName, schemaName), tableName, each.getColumnName().getIdentifier()));
            result.add(new ColumnProjectionSegment(each.getColumnName()));
        }
        return result;
    }

    private static SimpleTableSegmentBinderContext createSimpleTableSegmentBinderContextWithMetaData(final SimpleTableSegment segment, final ShardingSphereSchema schema,
                                                                                                     final IdentifierValue databaseName, final IdentifierValue schemaName,
                                                                                                     final SQLStatementBinderContext binderContext, final IdentifierValue tableName) {
        Collection<ProjectionSegment> projectionSegments = new LinkedList<>();
        QuoteCharacter quoteCharacter = new DatabaseTypeRegistry(binderContext.getSqlStatement().getDatabaseType()).getDialectDatabaseMetaData().getQuoteCharacter();
        ShardingSphereTable table = schema.getTable(tableName);
        for (ShardingSphereColumn each : table.getAllColumns()) {
            ColumnProjectionSegment columnProjectionSegment = new ColumnProjectionSegment(createColumnSegment(segment, databaseName, schemaName, each, quoteCharacter, tableName));
            columnProjectionSegment.setVisible(each.isVisible());
            projectionSegments.add(columnProjectionSegment);
        }
        addSqlAuthModel(table, databaseName, schemaName, tableName, binderContext);
        return new SimpleTableSegmentBinderContext(projectionSegments);
    }

    private static void addSqlAuthModel(final ShardingSphereTable table, final IdentifierValue databaseName, final IdentifierValue schemaName,
                                        final IdentifierValue tableName, final SQLStatementBinderContext binderContext) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();

        // 获取数据库类型并判断是否为三层数据库
        DatabaseType databaseType = binderContext.getSqlStatement().getDatabaseType();

        // 对于三层数据库（如PostgreSQL），设置catalogName
        if (OwnerUtil.isCatalogDatabase(databaseType.getType())) {
            sqlAuthModel.setCatalogName(databaseName.getValue());
        }
        
        sqlAuthModel.setSchemaName(schemaName.getValue());
        sqlAuthModel.setName(tableName.getValue());
        sqlAuthModel.setOperation(binderContext.getOperation());

        switch (table.getType()) {
            case TABLE:
                sqlAuthModel.setType(SqlConstant.KEY_TABLE);
                break;
            case VIEW:
                sqlAuthModel.setType(SqlConstant.KEY_VIEW);
                break;
            default:
                sqlAuthModel.setType(SqlConstant.KEY_TABLE);
        }

        binderContext.addSqlAuthModel(sqlAuthModel);
    }

    private static ColumnSegment createColumnSegment(final SimpleTableSegment segment, final IdentifierValue databaseName, final IdentifierValue schemaName,
                                                     final ShardingSphereColumn column, final QuoteCharacter quoteCharacter, final IdentifierValue tableName) {
        ColumnSegment result = new ColumnSegment(0, 0, new IdentifierValue(column.getName(), quoteCharacter));
        result.setOwner(new OwnerSegment(0, 0, segment.getAlias().orElse(tableName)));
        result.setColumnBoundInfo(new ColumnSegmentBoundInfo(new TableSegmentBoundInfo(databaseName, schemaName), tableName, new IdentifierValue(column.getName(), quoteCharacter)));
        return result;
    }
}

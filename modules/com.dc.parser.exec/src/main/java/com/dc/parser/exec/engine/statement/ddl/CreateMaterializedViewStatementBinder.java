package com.dc.parser.exec.engine.statement.ddl;

import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.exec.engine.statement.dml.SelectStatementBinder;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.CreateMaterializedViewStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.SneakyThrows;

/**
 * Create materialized view statement binder.
 */
public final class CreateMaterializedViewStatementBinder implements SQLStatementBinder<CreateMaterializedViewStatement> {

    @Override
    public CreateMaterializedViewStatement bind(final CreateMaterializedViewStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        CreateMaterializedViewStatement result = copy(sqlStatement);
        if (null != sqlStatement.getSelect()) {
            binderContext.setOperation(SqlConstant.KEY_SELECT);
            result.setSelect(new SelectStatementBinder().bind(sqlStatement.getSelect(), binderContext));
        }
        return result;
    }

    public void extractSqlAuthModel(final CreateMaterializedViewStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setType(SqlConstant.KEY_VIEW);
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        SimpleTableSegment view = sqlStatement.getView();
        sqlAuthModel.setName(view.getTableName().getIdentifier().getValue());
        SchemaInfo info = OwnerUtil.extract(view.getOwner(), binderContext.getMetaData().getProtocolType(), binderContext.getCurrentDatabaseName(), binderContext.getCatalogName());
        sqlAuthModel.setCatalogName(info.getCatalogName());
        sqlAuthModel.setSchemaName(info.getSchemaName());
        binderContext.addSqlAuthModel(sqlAuthModel);
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static CreateMaterializedViewStatement copy(final CreateMaterializedViewStatement sqlStatement) {
        CreateMaterializedViewStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.setViewDefinition(sqlStatement.getViewDefinition());
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        result.setView(sqlStatement.getView());
        return result;
    }
}

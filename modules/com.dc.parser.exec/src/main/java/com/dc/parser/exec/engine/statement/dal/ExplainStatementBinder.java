package com.dc.parser.exec.engine.statement.dal;

import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.exec.engine.type.DMLStatementBindEngine;
import com.dc.parser.model.statement.dal.ExplainStatement;
import com.dc.parser.model.statement.dml.DMLStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

/**
 * Explain statement binder.
 */
public final class ExplainStatementBinder implements SQLStatementBinder<ExplainStatement> {

    @Override
    public ExplainStatement bind(final ExplainStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        sqlStatement.getSqlStatement().ifPresent(statement -> {
            if (statement instanceof DMLStatement) {
                new DMLStatementBindEngine().bind(binderContext, (DMLStatement) statement);
            }
        });
        return sqlStatement;
    }

    @Override
    public void extractSqlAuthModel(final ExplainStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        sqlStatement.getSimpleTable().ifPresent(simpleTableSegment -> {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setOperation(SqlConstant.KEY_DESC);
            sqlAuthModel.setType(SqlConstant.KEY_TABLE);
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(simpleTableSegment.getOwner(), binderContext.getMetaData().getProtocolType(), binderContext.getCurrentDatabaseName(), binderContext.getCatalogName());
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
            binderContext.addSqlAuthModel(sqlAuthModel);
        });
    }
}

package com.dc.parser.exec.engine.statement.ddl;

import com.dc.infra.utils.CaseInsensitiveMap;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.ddl.DropIndexStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

/**
 * Drop index statement binder.
 */
public final class DropIndexStatementBinder implements SQLStatementBinder<DropIndexStatement> {

    @Override
    public DropIndexStatement bind(final DropIndexStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        if (sqlStatement.getSimpleTable().isEmpty()) {
            return sqlStatement;
        }
        DropIndexStatement result = copy(sqlStatement);
        Multimap<CaseInsensitiveMap.CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        binderContext.setOperation(SqlConstant.KEY_ALTER);
        result.setSimpleTable(SimpleTableSegmentBinder.bind(sqlStatement.getSimpleTable().get(), binderContext, tableBinderContexts));
        sqlStatement.getIndexes().forEach(each -> result.getIndexes().add(each));
        return result;
    }

    public void extractSqlAuthModel(final DropIndexStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        sqlStatement.getIndexes().forEach(indexSegment -> {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setType(SqlConstant.KEY_INDEX);
            sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
            sqlAuthModel.setName(indexSegment.getIndexName().getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(indexSegment.getOwner(), binderContext.getMetaData().getProtocolType(), binderContext.getCurrentDatabaseName(), binderContext.getCatalogName());
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
            binderContext.addSqlAuthModel(sqlAuthModel);
        });
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static DropIndexStatement copy(final DropIndexStatement sqlStatement) {
        DropIndexStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        sqlStatement.getSimpleTable().ifPresent(result::setSimpleTable);
        sqlStatement.getAlgorithmType().ifPresent(result::setAlgorithmType);
        sqlStatement.getLockTable().ifPresent(result::setLockTable);
        result.setIfExists(sqlStatement.isIfExists());
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}

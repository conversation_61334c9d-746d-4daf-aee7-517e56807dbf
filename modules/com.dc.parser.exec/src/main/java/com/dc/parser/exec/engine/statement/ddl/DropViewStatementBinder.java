package com.dc.parser.exec.engine.statement.ddl;

import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.ddl.DropViewStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.SneakyThrows;

/**
 * Drop view statement binder.
 */
public final class DropViewStatementBinder implements SQLStatementBinder<DropViewStatement> {

    @Override
    public DropViewStatement bind(final DropViewStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        DropViewStatement result = copy(sqlStatement);
//        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
//        sqlStatement.getViews().forEach(each -> result.getViews().add(SimpleTableSegmentBinder.bind(each, binderContext, tableBinderContexts)));
        return result;
    }

    public void extractSqlAuthModel(final DropViewStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        sqlStatement.getViews().forEach(simpleTableSegment -> {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setType(SqlConstant.KEY_VIEW);
            sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(simpleTableSegment.getOwner(), binderContext.getMetaData().getProtocolType(), binderContext.getCurrentDatabaseName(), binderContext.getCatalogName());
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
            binderContext.addSqlAuthModel(sqlAuthModel);
        });
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static DropViewStatement copy(final DropViewStatement sqlStatement) {
        DropViewStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        result.getViews().addAll(sqlStatement.getViews());
        return result;
    }
}

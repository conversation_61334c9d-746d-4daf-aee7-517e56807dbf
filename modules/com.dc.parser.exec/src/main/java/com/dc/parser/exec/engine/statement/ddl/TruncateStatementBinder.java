package com.dc.parser.exec.engine.statement.ddl;

import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.ddl.TruncateStatement;
import com.dc.parser.model.util.OwnerUtil;
import com.dc.parser.model.util.SchemaInfo;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.SneakyThrows;

/**
 * Truncate statement binder.
 */
public final class TruncateStatementBinder implements SQLStatementBinder<TruncateStatement> {

    @Override
    public TruncateStatement bind(final TruncateStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        TruncateStatement result = copy(sqlStatement);
//        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
//        sqlStatement.getTables().forEach(each -> result.getTables().add(SimpleTableSegmentBinder.bind(each, binderContext, tableBinderContexts)));
        return result;
    }

    public void extractSqlAuthModel(final TruncateStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        sqlStatement.getTables().forEach(simpleTableSegment -> {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setType(SqlConstant.KEY_TABLE);
            sqlAuthModel.setOperation(SqlConstant.KEY_TRUNCATE);
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
            SchemaInfo info = OwnerUtil.extract(simpleTableSegment.getOwner(), binderContext.getMetaData().getProtocolType(), binderContext.getCurrentDatabaseName(), binderContext.getCatalogName());
            sqlAuthModel.setCatalogName(info.getCatalogName());
            sqlAuthModel.setSchemaName(info.getSchemaName());
            binderContext.addSqlAuthModel(sqlAuthModel);
        });
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static TruncateStatement copy(final TruncateStatement sqlStatement) {
        TruncateStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        result.getTables().addAll(sqlStatement.getTables());
        return result;
    }
}

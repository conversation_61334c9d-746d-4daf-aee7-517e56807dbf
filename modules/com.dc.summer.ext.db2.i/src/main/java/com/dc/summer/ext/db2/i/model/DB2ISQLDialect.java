
package com.dc.summer.ext.db2.i.model;

import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.code.NotNull;
import com.dc.summer.ext.generic.model.GenericSQLDialect;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.sql.SQLConstants;

public class DB2ISQLDialect extends GenericSQLDialect {

    private static final String[][] DB2I_BEGIN_END_BLOCK = new String[][]{
            {SQLConstants.BLOCK_BEGIN, SQLConstants.BLOCK_END},
            {"WHILE", SQLConstants.BLOCK_END + " WHILE"},
            {"FOR", SQLConstants.BLOCK_END + " FOR"},
    };

    public DB2ISQLDialect() {
        super("IBM DB2 i", "db2_i");
    }

    @Override
    public boolean supportsOrderByIndex() {
        return false;
    }

    public void initDriverSettings(JDBCSession session, JDBCDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super.initDriverSettings(session, dataSource, metaData);
        turnFunctionIntoKeyword("TRUNCATE");
    }

    @Override
    public boolean supportsAliasInSelect() {
        return true;
    }

    @NotNull
    @Override
    public MultiValueInsertMode getDefaultMultiValueInsertMode() {
        return MultiValueInsertMode.GROUP_ROWS;
    }

    @Override
    public boolean supportsAliasInConditions() {
        return false;
    }

    @Override
    public String[][] getBlockBoundStrings() {
        return DB2I_BEGIN_END_BLOCK;
    }
}

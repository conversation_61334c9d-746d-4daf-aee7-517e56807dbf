
package com.dc.summer.ext.db2.i.model;

import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * DB2IDataSourceInfo
 */
@Slf4j
class DB2IDataSourceInfo extends JDBCDataSourceInfo {

    public DB2IDataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {

        return "SELECT\n" +
                "    a.constraint_name AS \"constraint_name\",\n" +
                "    a.table_schema as \"schema_name\",\n" +
                "    a.table_name as \"table_name\",\n" +
                "    a.constraint_type as \"constraint_type\",\n" +
                "    case when a.enabled='Y' then 1 else 0 end as \"status\",\n" +
                "    b.column_name as \"colname\",\n" +
                "    1 as lowerline\n" +
                "from QSYS2.syscst a\n" +
                "left join QSYS2.SYSCSTCOL b on a.constraint_name = b.constraint_name and a.constraint_schema =b.constraint_schema\n" +
                "where a.table_schema = '"+schemaName+"' and a.table_name = '"+tableName+"' and a.constraint_type='PRIMARY KEY'";
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map: list) {
            if (map.get("colname") != null) {
                columns.add(map.get("colname").toString());
            } else if (map.get("COLNAME") != null) {
                columns.add(map.get("COLNAME").toString());
            }
        }

        return columns;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {

        return "select table_name as \"tablename\" from QSYS2.SYSTABLES \n" +
                "where upper(table_name) = upper('"+tableName+"') and upper(TABLE_SCHEMA) = upper('"+schemaName+"') AND trim(table_type) in ('T','P')";
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map: list) {
            if (map.get("tablename") != null) {
                realName = (map.get("tablename").toString()).trim();
                break;
            } else if (map.get("TABLENAME") != null) {
                realName = (map.get("TABLENAME").toString()).trim();
                break;
            }
        }

        return realName;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {

        if (list == null) {
            return "";
        }

        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {
            if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2", "VARGRAPHIC").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                    data.add(String.format("'%s'", varcharData));
                } else if (Arrays.asList("SMALLINT", "INTEGER", "BIGINT", "DECIMAL", "REAL", "FLOAT", "DOUBLE", "DECFLOAT").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    data.add(String.format("%s", item.getFieldValue()));
                } else {
                    data.add(String.format("'%s'", item.getFieldValue()));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = org.apache.commons.lang3.StringUtils.join(fields, "\",\"");
        String values = org.apache.commons.lang3.StringUtils.join(data, ",");

        if (null != schemaName) {
            return String.format("INSERT INTO %s.\"%s\" (%s) VALUES (%s)", schemaName, tableName, "\"" + columns + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES (%s)", tableName, "\"" + columns + "\"", values);
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }

        String fields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.joining("\",\""));

        String values = list.stream().map(sqlFieldDataList -> {
            List<String> data = new ArrayList<>();
            for (SqlFieldData item : sqlFieldDataList) {
                if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                    if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2", "VARGRAPHIC").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                        data.add(String.format("'%s'", varcharData));
                    } else if (Arrays.asList("SMALLINT", "INTEGER", "BIGINT", "DECIMAL", "REAL", "FLOAT", "DOUBLE", "DECFLOAT").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        data.add(String.format("%s", item.getFieldValue()));
                    } else {
                        data.add(String.format("'%s'", item.getFieldValue()));
                    }
                } else {
                    data.add(String.format("%s", "NULL"));
                }
            }
            return String.format("(%s)", StringUtils.join(data, ","));
        }).collect(Collectors.joining(","));

        if (null != schemaName) {
            return String.format("INSERT INTO %s.\"%s\" (%s) VALUES %s", schemaName, tableName, "\"" + fields + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES %s", tableName, "\"" + fields + "\"", values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(schemaName)) {
            return String.format("TRUNCATE TABLE %s.\"%s\"", schemaName, tableName);
        } else {
            return String.format("TRUNCATE TABLE \"%s\"", tableName);
        }
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {

        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "SELECT trim(a.SCHEMA_NAME) as \"username\",\n" +
                        "       count(1) as \"count\",1 \n" +
                        "from QSYS2.SYSSCHEMAS a\n" +
                        "left join QSYS2.SYSTABLES b \n" +
                        "on trim(a.SCHEMA_NAME) = trim(b.TABLE_SCHEMA) \n" +
                        "AND trim(b.table_type) in ('T','P') \n" +
                        "group by a.SCHEMA_NAME \n" +
                        "ORDER BY a.SCHEMA_NAME");

        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for(Map<String, Object> map: list){
            Map<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("addLabel", "dc_db2_iSeries_db_schema");
            // db2检索结果会补空格
            returnMap.put("username", ((String)map.get("username")).trim());
            returnMap.put("charset", "");

            if(Arrays.asList("QSYS2","SYSIBM","SYSADM","SYSFUN","SYSPROC","SYSPUBLIC","SYSSTAT",
                    "SYSIBMADM","QSYS","QSYS2").contains(((String)map.get("username")).trim())){
                returnMap.put("is_sys", 1);
            }else{
                returnMap.put("is_sys", 0);
            }

            returnMap.put("count", 0L);
            if(map.get("count") instanceof Integer){
                returnMap.put("count", ((Integer)map.get("count")).longValue());
            }else if(map.get("count") instanceof Long){
                returnMap.put("count", map.get("count"));
            }

            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        return "select\n" +
                "  a.TABLE_NAME as \"table_name\",\n" +
                "  a.COLUMN_NAME as \"column_name\",\n" +
                "  a.DATA_TYPE as \"data_type\",\n" +
                "  (\n" +
                "    CASE\n" +
                "      when DATETIME_PRECISION is not null then DATETIME_PRECISION\n" +
                "      when CHARACTER_OCTET_LENGTH is not null then CHARACTER_OCTET_LENGTH\n" +
                "      WHEN (\n" +
                "        DATA_TYPE = 'BIGINT'\n" +
                "        OR DATA_TYPE = 'SMALLINT'\n" +
                "        OR DATA_TYPE = 'INTEGER'\n" +
                "        OR DATA_TYPE = 'REAL'\n" +
                "        OR DATA_TYPE = 'DOUBLE'\n" +
                "      ) THEN NULL\n" +
                "      WHEN (\n" +
                "        DATA_TYPE = 'DECFLOAT'\n" +
                "        AND LENGTH = 8\n" +
                "      ) THEN 16\n" +
                "      WHEN (\n" +
                "        DATA_TYPE = 'DECFLOAT'\n" +
                "        AND LENGTH = 16\n" +
                "      ) THEN 34\n" +
                "      ELSE LENGTH\n" +
                "    END\n" +
                "  ) as \"data_length\",\n" +
                "  numeric_scale as \"numeric_scale\",\n" +
                "  CASE\n" +
                "    WHEN a.is_nullable = 'Y' THEN 1\n" +
                "    ELSE 0\n" +
                "  END AS \"nullable\",\n" +
                "  a.column_DEFAULT as \"data_default\",\n" +
                "  (\n" +
                "    case\n" +
                "      when t.column_name is not null then 1\n" +
                "      else 0\n" +
                "    end\n" +
                "  ) as \"is_primary_key\",\n" +
                "  (\n" +
                "    case\n" +
                "      when a.COLUMN_TEXT IS NULL THEN a.LONG_COMMENT\n" +
                "      ELSE a.COLUMN_TEXT\n" +
                "    END\n" +
                "  ) as \"comments\",\n" +
                "  case\n" +
                "    when a.identity_generation = 'ALWAYS' then 1\n" +
                "    else 0\n" +
                "  end as \"is_increment\",\n" +
                "  identity_generation as \"identity_generation\",\n" +
                "  identity_increment as \"identity_increment\",\n" +
                "  identity_maximum as \"identity_maximum\",\n" +
                "  identity_minimum as \"identity_minimum\",\n" +
                "  identity_cycle as \"identity_cycle\",\n" +
                "  identity_cache as \"identity_cache\",\n" +
                "  identity_order as \"identity_order\",\n" +
                "  1 as lowerline\n" +
                "from\n" +
                "  QSYS2.sysCOLUMNS a\n" +
                "  LEFT JOIN (\n" +
                "    select\n" +
                "      a.table_schema,\n" +
                "      a.table_name,\n" +
                "      b.column_name\n" +
                "    from\n" +
                "      QSYS2.syscst a\n" +
                "      left join QSYS2.SYSCSTCOL b on a.constraint_name = b.constraint_name\n" +
                "      and a.constraint_schema = b.constraint_schema\n" +
                "    where\n" +
                "      constraint_type = 'PRIMARY KEY'\n" +
                "  ) t on a.COLUMN_NAME = t.COLUMN_NAME\n" +
                "  and a.table_name = t.table_name\n" +
                "  and a.table_schema = t.table_schema\n" +
                "where\n" +
                "  a.table_schema = '" + schemaName + "'\n" +
                "  and a.table_name = '" + tableName + "'\n" +
                "order by\n" +
                "  ordinal_position";
    }

    @Override
    public boolean showInteger(DBDAttributeBinding column) {
        if (List.of("REAL", "DOUBLE", "FLOAT", "DECFLOAT").contains(column.getTypeName().toUpperCase(Locale.ROOT))) {
            return false;
        }
        return super.showInteger(column);
    }

    @Override
    public boolean isSupportsNumber(DBDAttributeBinding column) {
        return Objects.equals("DECFLOAT", column.getTypeName().toUpperCase(Locale.ROOT));
    }
}


package com.dc.summer.ext.db2.i.model;

import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.GenericTable;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;

public class DB2ITable extends GenericTable {

    DB2ITable(GenericStructContainer container, @Nullable String tableName, @Nullable String tableType, @Nullable JDBCResultSet dbResult) {
        super(container, tableName, tableType, dbResult);
    }
}

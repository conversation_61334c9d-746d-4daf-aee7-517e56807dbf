package com.dc.summer.ext.db2.i.data;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCObjectValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Slf4j
public class DB2IStringValueHandler extends JDBCObjectValueHandler {

    public static final DB2IStringValueHandler INSTANCE = new DB2IStringValueHandler();

    /**
     * 补充1388编码规则
     */
    public static final HashMap<Byte, String> patchCharset1388 = new HashMap<>();

    /**
     * 临时的转码数据集合
     */
    private List<ConvertChar> convertList;

    /**
     * 初始化patchCharset1388属性
     */
    static {
        patchCharset1388.put((byte) 99, "[");
        patchCharset1388.put((byte) -4, "]");
        patchCharset1388.put((byte) 67, "{");
        patchCharset1388.put((byte) -36, "}");
        patchCharset1388.put((byte) 89, "~");
        patchCharset1388.put((byte) -75, "@");
        patchCharset1388.put((byte) -20, "\\");
        patchCharset1388.put((byte) 79, "!");
        patchCharset1388.put((byte) 91, "$");
        patchCharset1388.put((byte) 95, "^");
        patchCharset1388.put((byte) -69, "|");
    }

    /**
     * 解码-1388
     */
    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {

        try {
            byte[] temp = resultSet.getBytes(index);
            byte[] bytes = transcoding(temp);
            String result;
            if (bytes != null) {
                String charset = session.getExecutionContext().getCharset();
                int codePage = charset == null ? 1388 : Integer.parseInt(charset);
                Class<?> clazz = session.getDataSource().getContainer().getDriver().getClassLoader().loadClass("com.ibm.as400.access.CharConverter");
                Constructor<?> constructor = clazz.getConstructor(int.class);
                Object converter = constructor.newInstance(codePage);
                Method byteArrayToString = clazz.getDeclaredMethod("byteArrayToString", byte[].class);
                String string = (String) byteArrayToString.invoke(converter, new Object[]{bytes});
                if (this.convertList != null && !this.convertList.isEmpty()) {
                    StringBuffer buffer = new StringBuffer(string);
                    for (int i = 0; i < this.convertList.size(); i++) {
                        ConvertChar c = this.convertList.get(i);
                        int n = c.index;
                        buffer = buffer.replace(n, n + 1, c.convertChar);
                    }
                    string = buffer.toString();
                }
                log.info("db2 as400 fetch string => " + string);
                result = string.replaceAll("\\p{C}.", "");
            } else {
                result = resultSet.getString(index);
            }
            log.info("db2 as400 fetch result => " + result);
            return result;
        } catch (ClassNotFoundException | InvocationTargetException | NoSuchMethodException | InstantiationException |
                 IllegalAccessException e) {
            throw new DBCException("Use CharConverter Error.", e);
        } finally {
            convertList = null;
        }
    }


    /**
     * 补充1388的转码函数
     */
    private byte[] transcoding(byte[] temp) {
        byte[] bytes;
        if (temp != null && temp.length > 2 && temp[0] == 0) {
            bytes = Arrays.copyOfRange(temp, 2, temp.length);
        } else {
            bytes = temp;
        }
        if (bytes != null && bytes.length > 0) {
            List<ConvertChar> list = new ArrayList<>();
            for (int i = 0; i < bytes.length; i++) {
                if (patchCharset1388.get(bytes[i]) != null) {
                    ConvertChar cc = new ConvertChar(i, patchCharset1388.get(bytes[i]));
                    list.add(cc);
                }
            }
            this.convertList = list;
        }
        return bytes;
    }

    /**
     * 补充转码函数需要用到的转换类（用于保存转换信息）
     */
    private class ConvertChar {
        int index;
        String convertChar;

        public ConvertChar(int i, String c) {
            index = i;
            convertChar = c;
        }
    }

}




package com.dc.summer.ext.db2.i.edit;

import com.dc.summer.DBException;
import com.dc.summer.ext.db2.i.model.DB2IConstraint;
import com.dc.summer.ext.generic.model.*;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEObjectRenamer;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.ext.generic.edit.GenericTableManager;
import com.dc.summer.ext.generic.model.*;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public class DB2ITableManager extends GenericTableManager implements DBEObjectRenamer<GenericTableBase> {

    private static final Class<? extends DBSObject>[] CHILD_TYPES = CommonUtils.array(
        GenericTableColumn.class,
        DB2IConstraint.class,
        GenericTableForeignKey.class,
        GenericTableIndex.class
    );

    @NotNull
    @Override
    public Class<? extends DBSObject>[] getChildTypes() {
        return CHILD_TYPES;
    }

    @Override
    public Collection<? extends DBSObject> getChildObjects(DBRProgressMonitor monitor, GenericTableBase object, Class<? extends DBSObject> childType) throws DBException {
        if (childType == DB2IConstraint.class) {
            return object.getConstraints(monitor);
        }
        return super.getChildObjects(monitor, object, childType);
    }

    @Override
    public void renameObject(@NotNull DBECommandContext commandContext, @NotNull GenericTableBase object, @NotNull Map<String, Object> options, @NotNull String newName) throws DBException {
        processObjectRename(commandContext, object, options, newName);
    }

    @Override
    protected void addObjectRenameActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectRenameCommand command, Map<String, Object> options) {
        GenericTableBase tableBase = command.getObject();
        GenericDataSource dataSource = tableBase.getDataSource();
        GenericStructContainer container = tableBase.getParentObject();
        if (container != null) {
            actions.add(
                new SQLDatabasePersistAction(
                    "Rename table",
                    "RENAME TABLE " + DBUtils.getFullyQualifiedName(dataSource, container.getName(), command.getOldName()) + //$NON-NLS-1$
                        " TO " + DBUtils.getQuotedIdentifier(dataSource, command.getNewName())) //$NON-NLS-1$
            );
        }
    }
}

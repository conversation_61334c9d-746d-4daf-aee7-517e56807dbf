<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.dataSourceProvider">

        <!-- DB2 i -->
        <datasource
                class="com.dc.summer.ext.db2.i.DB2IDataSourceProvider"
                description="%datasource.db2_i.description"
                id="db2_i"
                parent="generic"
                label="DB2 iSeries"
                icon="icons/db2_i_icon.png"
                dialect="db2_i">
            <treeInjection path="generic/catalog/schema/table"
                           changeFolderType="com.dc.summer.ext.db2.i.model.DB2ITable"/>
            <treeInjection path="generic/catalog/schema/table/uniqueKey"
                           changeFolderType="com.dc.summer.ext.db2.i.model.DB2IConstraint"/>
            <drivers managable="true">

                <driver
                        id="db2_iseries_10"
                        label="DB2 iSeries/AS 400"
                        icon="icons/db2_i_icon.png"
                        iconBig="icons/db2_i_icon_big.png"
                        category="DB2"
                        class="com.ibm.as400.access.AS400JDBCDriver"
                        sampleURL="jdbc:as400://{host};[libraries={database};]"
                        defaultPort="446"
                        description="IBM DB2 iSeries/AS 400 driver"
                        categories="sql">
                    <replace provider="generic" driver="db2_iseries"/>

                    <file type="jar" path="maven:/net.sf.jt400:jt400:10.7" bundle="!drivers.jt400"/>

                    <file type="license" path="drivers/db2-jt400/LICENSE.txt" bundle="drivers.jt400"/>
                    <file type="jar" path="drivers/db2-jt400" bundle="drivers.jt400"/>

                    <parameter name="query-get-active-db" value="SELECT CURRENT_SCHEMA FROM SYSIBM.SYSDUMMY1"/>
                    <parameter name="query-set-active-db" value="SET SCHEMA ?"/>
                    <parameter name="omit-catalog" value="true"/>
                    <parameter name="schema-filters-enabled" value="false"/>

                    <!-- Enable table/column descriptions -->
                    <property name="translate binary" value="true"/>
                    <!-- Metasource=0 enables metadata comments read but may break long catalog/schema names (#10302) -->
<!--                    <property name="metadata source" value="0"/>-->

                    <property name="@summer-default-connect-validation-query" value="VALUES 1"/>

                </driver>

            </drivers>

        </datasource>
    </extension>


    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.db2.i.data.DB2IValueHandlerProvider"
                description="DB2 I data types provider"
                id="com.dc.summer.ext.db2.i.data.DB2IValueHandlerProvider"
                label="DB2 I data types provider">

            <datasource id="db2_i"/>

            <type name="CHAR"/>
            <type name="CHARACTER"/>
            <type name="VARCHAR"/>
            <type name="LONG VARCHAR"/>

            <type name="GRAPHIC"/>
            <type name="VARGRAPHIC"/>
            <type name="LONG VARGRAPHIC"/>

        </provider>
    </extension>

    <extension point="com.dc.summer.generic.meta">
        <meta id="db2iseries" class="com.dc.summer.ext.db2.i.model.DB2IMetaModel" driverClass="com.ibm.as400.access.AS400JDBCDriver"/>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="db2_i" parent="generic" class="com.dc.summer.ext.db2.i.model.DB2ISQLDialect" label="DB2 i" description="IBM DB2 i SQL dialect." icon="icons/db2_i_icon.png">
        </dialect>
    </extension>

    <extension point="com.dc.summer.objectManager">
        <manager class="com.dc.summer.ext.db2.i.edit.DB2ITableManager" objectType="com.dc.summer.ext.db2.i.model.DB2ITable"/>
        <manager class="com.dc.summer.ext.db2.i.edit.DB2IConstraintManager" objectType="com.dc.summer.ext.db2.i.model.DB2IConstraint"/>
    </extension>

</plugin>

<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.generic.meta">
        <meta id="informix" class="com.dc.summer.ext.informix.model.InformixMetaModel" driverClass="com.informix.jdbc.IfxDriver"/>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.informix.model.data.InformixValueHandlerProvider"
                description="%provider.data.type.postgresql.description"
                id="com.dc.summer.ext.informix.model.data.InformixValueHandlerProvider"
                label="%provider.data.type.postgresql.name">

            <datasource id="informix"/>

            <type name="*"/>
        </provider>
    </extension>

    <extension point="com.dc.summer.dataSourceProvider">

        <datasource
                class="com.dc.summer.ext.informix.InformixDataSourceProvider"
                description="Informix"
                id="informix"
                dialect="informix"
                label="Informix">
            <drivers managable="true">
                <driver
                        id="informix"
                        label="Informix"
                        icon="icons/informix_icon.png"
                        iconBig="icons/informix_icon_big.png"
                        class="com.informix.jdbc.IfxDriver"
                        sampleURL="jdbc:informix-sqli://{host}:{port}/{database}"
                        defaultPort="1533"
                        webURL="https://www.developers.net/ibmshowcase/focus/Informix"
                        description="IBM Informix Dynamic Server"
                        categories="sql">

                    <file type="jar" path="maven:/com.ibm.informix:jdbc:RELEASE[********]" bundle="!drivers.informix"/>

                    <file type="jar" path="drivers/informix" bundle="drivers.informix"/>
                    <file type="license" path="drivers/informix/LICENSE.txt" bundle="drivers.informix"/>

                    <parameter name="query-get-active-db" value="SELECT ODB_DBName FROM SysMaster:informix.SysOpenDB WHERE ODB_IsCurrent = 'Y' AND ODB_SessionID = DBINFO('sessionid')"/>
                    <parameter name="query-set-active-db" value="DATABASE ?"/>
                    <parameter name="legacy-sql-dialect" value="true"/>
                    <parameter name="omit-catalog-name" value="false"/>

                    <parameter name="ddl-drop-column-short" value="true"/>

                    <property name="@summer-default-resultset.column.label.ignore" value="true"/>
                    <property name="@summer-default-connect-validation-query" value=""/>
                </driver>
            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="informix" parent="generic" class="com.dc.summer.ext.informix.model.InformixDialect" label="Informix" description="Informix Dialect on JDBC API information." icon="#database_icon_default" hidden="true">
        </dialect>
    </extension>

</plugin>

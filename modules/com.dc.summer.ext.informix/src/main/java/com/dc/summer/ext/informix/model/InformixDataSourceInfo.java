package com.dc.summer.ext.informix.model;

import com.dc.annotation.SQL;
import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class InformixDataSourceInfo extends JDBCDataSourceInfo {

    public InformixDataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public boolean supportsDDLAutoCommit() {
        return false;
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {

        return "select \n" +
                "sc.colname as column_name \n" +
                "from \n" +
                schemaName + ":systables st,\n" +
                schemaName + ":sysconstraints so,\n" +
                schemaName + ":sysindexes si,\n" +
                schemaName + ":syscolumns sc\n" +
                "where\n" +
                "        st.tabname = '" + tableName + "'\n" +
                "        and st.tabid = so.tabid\n" +
                "        and so.constrtype = 'P'\n" +
                "        and so.idxname = si.idxname\n" +
                "        and sc.tabid = st.tabid\n" +
                "        and st.owner like '%'\n" +
                "        and(\n" +
                "                sc.colno = abs( si.part1 )\n" +
                "                or sc.colno = abs( si.part2 )\n" +
                "                or sc.colno = abs( si.part3 )\n" +
                "                or sc.colno = abs( si.part4 )\n" +
                "                or sc.colno = abs( si.part5 )\n" +
                "                or sc.colno = abs( si.part6 )\n" +
                "                or sc.colno = abs( si.part7 )\n" +
                "                or sc.colno = abs( si.part8 )\n" +
                "                or sc.colno = abs( si.part9 )\n" +
                "                or sc.colno = abs( si.part10 )\n" +
                "                or sc.colno = abs( si.part11 )\n" +
                "                or sc.colno = abs( si.part12 )\n" +
                "                or sc.colno = abs( si.part13 )\n" +
                "                or sc.colno = abs( si.part14 )\n" +
                "                or sc.colno = abs( si.part15 )\n" +
                "                or sc.colno = abs( si.part16 )\n" +
                "        )";
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map : list) {
            if (map.get("column_name") != null) {
                columns.add((map.get("column_name").toString()).trim());
            } else if (map.get("COLUMN_NAME") != null) {
                columns.add((map.get("COLUMN_NAME").toString()).trim());
            }
        }

        return columns;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {

        return "select tabname from "
                + schemaName
                + ":systables where tabtype = 'T' "
                + "and upper(tabname) = upper('" + tableName + "')";
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map : list) {
            if (map.get("tabname") != null) {
                realName = (map.get("tabname").toString()).trim();
                break;
            } else if (map.get("TABNAME") != null) {
                realName = (map.get("TABNAME").toString()).trim();
                break;
            }
        }

        return realName;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        if (list == null) {
            return "";
        }

        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {

            String regExp = ".*\\(\\d+\\).*";
            String regReplace = "\\(\\d+\\)";
            String replace = "";
            String columnType = null;
            if (Pattern.matches(regExp, item.getFieldType())) {
                columnType = item.getFieldType().replaceAll(regReplace, replace);
            } else {
                columnType = item.getFieldType();
            }
            if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2").contains(columnType.toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                    data.add(String.format("'%s'", varcharData));
                } else {
                    data.add(String.format("'%s'", item.getFieldValue()));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = StringUtils.join(fields, ",");
        String values = StringUtils.join(data, ",");

        if (null != schemaName) {
            return String.format("INSERT INTO %s:%s (%s) VALUES (%s)", schemaName, tableName, columns, values);
        }
        return String.format("INSERT INTO %s (%s) VALUES (%s)", tableName, columns, values);
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }

        String fields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.joining(","));
        String values = list.stream().map(sqlFieldDataList -> {
            List<String> data = new ArrayList<>();
            for (SqlFieldData item : sqlFieldDataList) {

                String regExp = ".*\\(\\d+\\).*";
                String regReplace = "\\(\\d+\\)";
                String replace = "";
                String columnType = null;
                if (Pattern.matches(regExp, item.getFieldType())) {
                    columnType = item.getFieldType().replaceAll(regReplace, replace);
                } else {
                    columnType = item.getFieldType();
                }
                if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                    if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2").contains(columnType.toUpperCase(Locale.ROOT))) {
                        String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                        data.add(String.format("'%s'", varcharData));
                    } else {
                        data.add(String.format("'%s'", item.getFieldValue()));
                    }
                } else {
                    data.add(String.format("%s", "NULL"));
                }
            }
            return String.format("(%s)", StringUtils.join(data, ","));
        }).collect(Collectors.joining(","));

        if (null != schemaName) {
            return String.format("INSERT INTO %s:%s (%s) VALUES %s", schemaName, tableName, fields, values);
        }
        return String.format("INSERT INTO %s (%s) VALUES %s", tableName, fields, values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(schemaName)) {
            return String.format("TRUNCATE TABLE %s:%s", schemaName, tableName);
        } else {
            return String.format("TRUNCATE TABLE %s", tableName);
        }
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {
        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "select name as owner from sysmaster:sysdatabases");

        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("addLabel", "dc_informix_db_schema");
            returnMap.put("username", map.get("owner").toString().trim());

            try {
                List<Map<String, Object>> listCount = DBExecUtils.executeQuery(monitor, context, "get schema count",
                        "select count(0) as count from " + map.get("owner").toString().trim() + ":systables where tabtype = 'T'");
                returnMap.put("count", listCount.size() > 0 ? Long.parseLong(listCount.get(0).get("count").toString()) : 0L);
            } catch (DBException e) {
                returnMap.put("count", 0L);
            }

            List<Map<String, Object>> listCharset = DBExecUtils.executeQuery(monitor, context, "get schema charset",
                    "select dbs_collate as value from sysmaster:sysdbslocale " +
                            "where dbs_dbsname = '" + map.get("owner").toString().trim() + "'");
            returnMap.put("charset", listCharset.size() > 0 ? listCharset.get(0).get("value").toString().trim() : "");

            if (Arrays.asList("sysadmin", "sysmaster", "sysuser", "sysutils").contains(map.get("owner").toString().trim())) {
                returnMap.put("is_sys", 1);
            } else {
                returnMap.put("is_sys", 0);
            }

            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        return "SELECT      \n" +
                "            t.tabname as table_name,\n" +
                "            c.colname as column_name,\n" +
                "            c.collength as collength,\n" +
                "            schema_precision(c.coltype, c.extended_id, c.collength) as data_length,\n" +
                "            schema_numscale(c.coltype, c.collength) as area,\n" +
                "            schema_numprecradix(c.coltype) as precision,\n" +
                "            lower(schema_coltypename(coltype, extended_id)) as data_type,\n" +
                "            schema_coltypename(c.coltype,c.extended_id) as col_type,\n" +
                "            schema_isnullable(c.coltype) as nullable,\n" +
                "            (\n" +
                "                CASE\n" +
                "                    d.type\n" +
                "                WHEN 'L' THEN\n" +
                "                    get_default_value(c.coltype, c.extended_id, c.collength, d.default::lvarchar(256))::VARCHAR(254)\n" +
                "                WHEN 'C' THEN\n" +
                "                    'current'::VARCHAR(254)\n" +
                "                WHEN 'S' THEN\n" +
                "                    'dbservername'::VARCHAR(254)\n" +
                "                WHEN 'U' THEN\n" +
                "                    'user'::VARCHAR(254)\n" +
                "                WHEN 'T' THEN\n" +
                "                    'today'::VARCHAR(254)\n" +
                "                ELSE\n" +
                "                    NULL::VARCHAR(254)\n" +
                "                END\n" +
                "            ) as column_default,\n" +
                "            (\n" +
                "                case\n" +
                "                when pk.colno > 0 then\n" +
                "                    1\n" +
                "                else\n" +
                "                    0\n" +
                "                end\n" +
                "            ) as is_primary_key,\n" +
                "            '' as special_column,\n" +
                "            '' as comments\n" +
                "        FROM\n" +
                             schemaName + ":systables t,\n" +
                "            outer " + schemaName + ":sysdefaults d,\n" +
                             schemaName + ":syscolumns c,\n" +
                "            outer (\n" +
                "                select\n" +
                "                    c.colno, t.tabid\n" +
                "                from\n" +
                                     schemaName + ":systables as t,\n" +
                                     schemaName + ":sysindexes as si,\n" +
                                     schemaName + ":sysconstraints as so,\n" +
                                     schemaName + ":syscolumns as c\n" +
                "                where\n" +
                "                    t.tabid = so.tabid\n" +
                "                    and so.idxname = si.idxname\n" +
                "                    and c.tabid = t.tabid\n" +
                "                    and so.constrtype = 'P'\n" +
                "                    and t.tabname = '" + tableName + "'\n" +
                "                    and (\n" +
                "                        c.colno = ABS(si.part1)\n" +
                "                        or c.colno = ABS(si.part2)\n" +
                "                        or c.colno = ABS(si.part3)\n" +
                "                        or c.colno = ABS(si.part4)\n" +
                "                        or c.colno = ABS(si.part5)\n" +
                "                        or c.colno = ABS(si.part6)\n" +
                "                        or c.colno = ABS(si.part7)\n" +
                "                        or c.colno = ABS(si.part8)\n" +
                "                        or c.colno = ABS(si.part9)\n" +
                "                        or c.colno = ABS(si.part10)\n" +
                "                        or c.colno = ABS(si.part11)\n" +
                "                        or c.colno = ABS(si.part12)\n" +
                "                        or c.colno = ABS(si.part13)\n" +
                "                        or c.colno = ABS(si.part14)\n" +
                "                        or c.colno = ABS(si.part15)\n" +
                "                        or c.colno = ABS(si.part16)\n" +
                "                    )\n" +
                "            ) as pk\n" +
                "        where t.tabid = c.tabid and d.tabid = t.tabid AND c.colno = d.colno\n" +
                "            and pk.colno = c.colno and t.tabid = pk.tabid\n" +
                "            and t.tabname = '" + tableName + "'\n" +
                "        order by c.colno";
    }

    @Override
    public String getSynonymSql(String schemaName, String synonym) {
        @SQL
        String sql = "select\n" +
                "    t1.dbname as source_schema, case when t1.tabname is null\n" +
                "                then (select tabname from " + schemaName + ":systables t3 where t3.tabid = t1.btabid)\n" +
                "            else\n" +
                "                t1.tabname\n" +
                "            end as source_table\n" +
                "from\n" +
                "    " + schemaName + ":syssyntable t1\n" +
                "join\n" +
                "    " + schemaName + ":systables t2 on t1.tabid = t2.tabid\n" +
                "where\n" +
                "    (t2.tabtype = 'S' or t2.tabtype = 'P') and t2.tabname='" + synonym + "';";
        return sql;
    }

    @Override
    public boolean showInteger(DBDAttributeBinding column) {
        if (List.of("FLOAT", "SMALLFLOAT", "MONEY").contains(column.getTypeName().toUpperCase(Locale.ROOT))) {
            return false;
        }
        return super.showInteger(column);
    }

    @Override
    public boolean catalogIsSchema() {
        return true;
    }
}

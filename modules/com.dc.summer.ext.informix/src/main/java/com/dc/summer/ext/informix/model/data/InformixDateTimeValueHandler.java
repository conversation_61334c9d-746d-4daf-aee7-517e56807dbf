package com.dc.summer.ext.informix.model.data;

import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCDateTimeValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.utils.DateUtil;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

public class InformixDateTimeValueHandler extends JDBCDateTimeValueHandler {

    private static final List<String> DATE_TIME_TYPE_LIST = Arrays.asList(
            "DATETIME YEAR TO YEAR",
            "DATETIME YEAR TO MONTH",
            "DATETIME YEAR TO DAY",
            "DATETIME YEAR TO HOUR",
            "DATETIME YEAR TO MINUTE",
            "DATETIME YEAR TO SECOND",
            "D<PERSON>ETIM<PERSON> MONTH TO MONTH",
            "DATETIME MONTH TO DAY",
            "DATETIME MONTH TO HOUR",
            "DATETIME MONTH TO MINUTE",
            "DATETIME MONTH TO SECOND",
            "DATETIME DAY TO DAY",
            "DATETIME DAY TO HOUR",
            "DATETIME DAY TO MINUTE",
            "DATETIME DAY TO SECOND",
            "DATETIME HOUR TO HOUR",
            "DATETIME HOUR TO MINUTE",
            "DATETIME HOUR TO SECOND",
            "DATETIME MINUTE TO MINUTE",
            "DATETIME MINUTE TO SECOND",
            "DATETIME SECOND TO SECOND",
            "DATETIME YEAR TO FRACTION",
            "DATETIME MONTH TO FRACTION",
            "DATETIME DAY TO FRACTION",
            "DATETIME HOUR TO FRACTION",
            "DATETIME MINUTE TO FRACTION",
            "DATETIME SECOND TO FRACTION");

    public InformixDateTimeValueHandler(DBDFormatSettings formatSettings) {
        super(formatSettings);
    }



    @Override
    public String getValueDisplayString(DBSTypedObject column, Object value, DBDDisplayFormat format) {
        if (column.getTypeName().toUpperCase(Locale.ROOT).contains("DATETIME") && DATE_TIME_TYPE_LIST.contains(column.getTypeName().toUpperCase(Locale.ROOT))) {
            if (!column.getTypeName().toUpperCase(Locale.ROOT).contains("FRACTION")) {
                return DateUtil.timestamp2Str((Timestamp) value, DateTimeType.getPatternByType(column.getTypeName().toUpperCase(Locale.ROOT)));
            } else {
                String fieldType = column.getTypeName().toUpperCase(Locale.ROOT);
                String subFieldType = fieldType.substring(0,fieldType.indexOf("("));
                value = DateUtil.timestamp2Str((Timestamp) value, DateTimeType.getPatternByType(subFieldType));
                String size = fieldType.substring(fieldType.indexOf("(")+1,fieldType.indexOf(")"));
                String zero = "000000".substring(0,Integer.parseInt(size));
                return String.format("%s.%s", value, zero);
            }
        }
        return super.getValueDisplayString(column, value, format);
    }
}

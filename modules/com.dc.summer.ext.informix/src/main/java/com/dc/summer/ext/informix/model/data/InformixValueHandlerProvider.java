package com.dc.summer.ext.informix.model.data;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStandardValueHandlerProvider;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.Types;

public class InformixValueHandlerProvider extends JDBCStandardValueHandlerProvider {


    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {
        int typeID = typedObject.getTypeID();
        switch (typeID) {
            case Types.TIMESTAMP:
                return new InformixDateTimeValueHandler(preferences);
        }
        return super.getValueHandler(dataSource, preferences, typedObject);
    }
}

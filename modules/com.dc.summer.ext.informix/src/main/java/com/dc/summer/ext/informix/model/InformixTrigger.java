
package com.dc.summer.ext.informix.model;

import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.generic.model.GenericTableTrigger;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.utils.CommonUtils;

public class InformixTrigger extends GenericTableTrigger {

    public enum TriggerEventType {
        D("DELETE"),
        I("INSERT"),
        U("UPDATE"),
        S("SELECT"),
        d("INSTEAD OF Delete"),
        i("INSTEAD OF Insert"),
        u("INSTEAD OF Update");

        private final String eventType;

        TriggerEventType(String eventType) {
            this.eventType = eventType;
        }

        public String getEventType() {
            return eventType;
        }
    }

    private TriggerEventType eventType;
    private final String beforeValue;
    private final String afterValue;
    private final String collation;

    public InformixTrigger(@NotNull GenericTableBase container, String name, @NotNull JDBCResultSet resultSet) {
        super(container, name, null);
        String eventTypeLetter = JDBCUtils.safeGetString(resultSet, "event");
        if (CommonUtils.isNotEmpty(eventTypeLetter)) {
            this.eventType = CommonUtils.valueOf(TriggerEventType.class, eventTypeLetter);
        }
        this.beforeValue = JDBCUtils.safeGetString(resultSet, "old");
        this.afterValue = JDBCUtils.safeGetString(resultSet, "new");
        this.collation = JDBCUtils.safeGetString(resultSet, "collation");
    }

    @Property(viewable = true, order = 5)
    public String getEventType() {
        return eventType.getEventType();
    }

    @Property(viewable = true, order = 6)
    public String getBeforeValue() {
        return beforeValue;
    }

    @Property(viewable = true, order = 7)
    public String getAfterValue() {
        return afterValue;
    }

    @Property(viewable = true, order = 8)
    public String getCollation() {
        return collation;
    }

    // Hide property
    @Nullable
    @Override
    public String getDescription() {
        return super.getDescription();
    }
}

package com.dc.summer.ext.informix.model.data;

import com.dc.utils.DateUtil;

public enum DateTimeType {
    YEAR_TO_YEAR("DATETIME YEAR TO YEAR", DateUtil.yy_str_1),
    YEAR_TO_MONTH("DATETIME YEAR TO MONTH", DateUtil.ym_str_1),
    YEAR_TO_DAY("DATETIME YEAR TO DAY", DateUtil.ymd_str_1),
    YEAR_TO_HOUR("DATETIME YEAR TO HOUR", DateUtil.ymd_h_str_1),
    YEAR_TO_MINUTE("DATETIME YEAR TO MINUTE", DateUtil.ymd_hm_str_1),
    YEAR_TO_SECOND("DATETIME YEAR TO SECOND", DateUtil.ymd_hms_str_1),
    MONTH_TO_MONTH("DATETIME MONTH TO MONTH", DateUtil.mm_str_1),
    MONTH_TO_DAY("DATETIM<PERSON> MONTH TO DAY", DateUtil.md_str_1),
    MONTH_TO_HOUR("DATETIME MONTH TO HOUR", DateUtil.md_hh_str_1),
    MONTH_TO_MINUTE("DATETIME MONTH TO MINUTE", DateUtil.md_hm_str_1),
    MONTH_TO_SECOND("DATETIME MONTH TO SECOND", DateUtil.md_hms_str_1),
    DAY_TO_DAY("DATETIME DAY TO DAY", DateUtil.dd_str_1),
    DAY_TO_HOUR("DATETIME DAY TO HOUR", DateUtil.dd_hh_str_1),
    DAY_TO_MINUTE("DATETIME DAY TO MINUTE", DateUtil.dd_hm_str_1),
    DAY_TO_SECOND("DATETIME DAY TO SECOND", DateUtil.dd_hms_str_1),
    HOUR_TO_HOUR("DATETIME HOUR TO HOUR", DateUtil.hh_str_1),
    HOUR_TO_MINUTE("DATETIME HOUR TO MINUTE", DateUtil.hm_str_1),
    HOUR_TO_SECOND("DATETIME HOUR TO SECOND", DateUtil.hms_str_1),
    MINUTE_TO_MINUTE("DATETIME MINUTE TO MINUTE", DateUtil.mm1_str_1),
    MINUTE_TO_SECOND("DATETIME MINUTE TO SECOND", DateUtil.ms_str_1),
    SECOND_TO_SECOND("DATETIME SECOND TO SECOND", DateUtil.ss_str_1),
    YEAR_TO_FRACTION("DATETIME YEAR TO FRACTION", DateUtil.ymd_hms_str_1),
    MONTH_TO_FRACTION("DATETIME MONTH TO FRACTION", DateUtil.md_hms_str_1),
    DAY_TO_FRACTION("DATETIME DAY TO FRACTION", DateUtil.dd_hms_str_1),
    HOUR_TO_FRACTION("DATETIME HOUR TO FRACTION", DateUtil.hms_str_1),
    MINUTE_TO_FRACTION("DATETIME MINUTE TO FRACTION", DateUtil.ms_str_1),
    SECOND_TO_FRACTION("DATETIME SECOND TO FRACTION", DateUtil.ss_str_1);

    public String type;
    public String pattern;

    DateTimeType(String type, String pattern) {
        this.type = type;
        this.pattern = pattern;
    }

    public static String getPatternByType(String type) {
        for (DateTimeType gbase : DateTimeType.values()) {
            if (gbase.type.equals(type)) {
                return gbase.pattern;
            }
        }
        return DateUtil.ymd_hms_str_4;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }
}

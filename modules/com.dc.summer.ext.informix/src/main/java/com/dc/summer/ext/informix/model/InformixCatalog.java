package com.dc.summer.ext.informix.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericCatalog;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;

public class InformixCatalog extends GenericCatalog {

    public InformixCatalog(GenericDataSource dataSource, String catalogName) {
        super(dataSource, catalogName);
    }

    @Override
    public DBSObject getChild(DBRProgressMonitor monitor, String childName) throws DBException {
        return getTable(monitor, childName);
    }

}

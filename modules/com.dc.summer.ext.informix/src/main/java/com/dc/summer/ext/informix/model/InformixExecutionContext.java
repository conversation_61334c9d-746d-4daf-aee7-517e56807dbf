package com.dc.summer.ext.informix.model;

import com.dc.summer.ext.generic.model.GenericExecutionContext;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class InformixExecutionContext extends GenericExecutionContext {

    public InformixExecutionContext(JDBCRemoteInstance instance, String purpose) {
        super(instance, purpose);
    }

    @Override
    protected List<Map<String, Object>> initContextBootstrap(DBRProgressMonitor monitor, boolean autoCommit) throws DBCException {
        getBootstrapSettings().setInitQueries(Collections.singleton("SELECT DBINFO('sessionid') as pid FROM informix.systables WHERE tabid = 1;"));
        List<Map<String, Object>> list = execContextBootstrap(monitor, autoCommit);
        if (list.size() > 0) {
            String pid = String.valueOf(list.get(0).get("pid"));
            super.setProcessId(pid);
        }
        return list;
    }
}


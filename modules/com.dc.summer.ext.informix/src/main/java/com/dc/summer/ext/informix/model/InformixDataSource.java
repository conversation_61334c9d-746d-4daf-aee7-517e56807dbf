package com.dc.summer.ext.informix.model;

import com.dc.function.RuntimeRunnable;
import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.utils.PrefUtils;

public class InformixDataSource extends GenericDataSource {

    public InformixDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel) throws DBException {
        super(monitor, container, metaModel, new InformixDialect());
        PrefUtils.setPreferenceValue(container.getPreferenceStore(), ModelPreferences.CONNECT_VALIDATION_QUERY, "");
    }

    protected InformixDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel, SQLDialect dialect) throws DBException {
        super(monitor, container, metaModel, dialect);
        PrefUtils.setPreferenceValue(container.getPreferenceStore(), ModelPreferences.CONNECT_VALIDATION_QUERY, "");
    }

    @Override
    protected JDBCExecutionContext createExecutionContext(JDBCRemoteInstance instance, String type) {
        return new InformixExecutionContext(instance, type);
    }

    @Override
    public RuntimeRunnable killConnection(DBRProgressMonitor monitor, JDBCExecutionContext defaultContext, String processId) {
        String sql = String.format("execute function sysadmin:admin( 'onmode','z','%s')", processId);
        return () -> DBExecUtils.execute(monitor, defaultContext, killConnection(), sql);
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new InformixDataSourceInfo(metaData);
    }

    @Override
    public boolean supportsCatalogChangeInTransaction() {
        return false;
    }

    @Override
    protected boolean isSelectedEntityFromAPI() {
        return false;
    }

    @Override
    public boolean isMergeEntities() {
        return false;
    }
}

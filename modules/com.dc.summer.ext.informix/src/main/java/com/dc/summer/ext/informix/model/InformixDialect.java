package com.dc.summer.ext.informix.model;

import com.dc.code.NotNull;
import com.dc.summer.ext.generic.model.GenericSQLDialect;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.sql.SQLConstants;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLSyntaxManager;
import com.dc.summer.model.sql.parser.SQLParserActionKind;
import com.dc.summer.model.sql.parser.SQLRuleManager;
import com.dc.summer.model.sql.parser.SQLTokenPredicateSet;
import com.dc.summer.model.sql.parser.tokens.SQLTokenType;
import com.dc.summer.model.sql.parser.tokens.predicates.TokenPredicateFactory;
import com.dc.summer.model.sql.parser.tokens.predicates.TokenPredicateSet;
import com.dc.summer.model.sql.parser.tokens.predicates.TokenPredicatesCondition;

import java.util.stream.Stream;

public class InformixDialect extends GenericSQLDialect {

    private SQLTokenPredicateSet cachedDialectSkipTokenPredicates = null;

    public InformixDialect() {
        super("Informix", "informix");
    }

    protected InformixDialect(String name, String id) {
        super(name, id);
    }

    @Override
    public boolean isDateTimeType(String word) {
        return Stream.of("DATETIME YEAR TO HOUR", "DATETIME YEAR TO MINUTE", "DATETIME YEAR TO SECOND",
                        "DATETIME MONTH TO HOUR", "DATETIME MONTH TO MINUTE", "DATETIME MONTH TO SECOND",
                        "DATETIME DAY TO HOUR", "DATETIME DAY TO MINUTE", "DATETIME DAY TO SECOND",
                        "DATETIME YEAR TO FRACTION", "DATETIME MONTH TO FRACTION", "DATETIME DAY TO FRACTION")
                .anyMatch(s -> s.equalsIgnoreCase(word));
    }

    @Override
    public boolean isDateType(String word) {
        return Stream.of("DATE", "DATETIME YEAR TO YEAR", "DATETIME YEAR TO MONTH", "DATETIME YEAR TO DAY"
                        , "DATETIME MONTH TO MONTH", "DATETIME MONTH TO DAY", "DATETIME DAY TO DAY")
                .anyMatch(s -> s.equalsIgnoreCase(word));
    }

    @Override
    public boolean isTimeType(String word) {
        return Stream.of("DATETIME HOUR TO HOUR", "DATETIME HOUR TO MINUTE", "DATETIME HOUR TO SECOND",
                        "DATETIME MINUTE TO MINUTE", "DATETIME MINUTE TO SECOND", "DATETIME SECOND TO SECOND",
                        "DATETIME HOUR TO FRACTION", "DATETIME MINUTE TO FRACTION", "DATETIME SECOND TO FRACTION")
                .anyMatch(s -> s.equalsIgnoreCase(word));
    }

    @Override
    public char getStructSeparator() {
        return ':';
    }

    @Override
    public String[][] getBlockBoundStrings() {
        return new String[][]{
                {
                        SQLConstants.BLOCK_BEGIN,
                        SQLConstants.BLOCK_END
                },
                {
                        SQLConstants.BLOCK_BEGIN,
                        "WORK"
                },
        };
    }

    @Override
    public void initDriverSettings(JDBCSession session, JDBCDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super.initDriverSettings(session, dataSource, metaData);
        cachedDialectSkipTokenPredicates = getInformixTokenPredicateSet(this, dataSource);
    }

    @Override
    @NotNull
    public SQLTokenPredicateSet getSkipTokenPredicates() {
        return cachedDialectSkipTokenPredicates == null ? super.getSkipTokenPredicates() : cachedDialectSkipTokenPredicates;
    }

    private static TokenPredicateSet getInformixTokenPredicateSet(SQLDialect sqlDialect, JDBCDataSource dataSource) {
        SQLSyntaxManager syntaxManager = new SQLSyntaxManager();
        syntaxManager.init(sqlDialect, dataSource.getContainer().getPreferenceStore());
        SQLRuleManager ruleManager = new SQLRuleManager(syntaxManager);
        ruleManager.loadRules(dataSource, false);
        TokenPredicateFactory tt = TokenPredicateFactory.makeDialectSpecificFactory(ruleManager);

        return TokenPredicateSet.of(
                TokenPredicateSet.getForProgram(tt, null, null)
        );
    }

}

package com.dc.summer.model.parser;

import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.tree.IterativeParseTreeWalker;
import org.antlr.v4.runtime.tree.ParseTreeListener;

import java.util.List;

abstract public class AntlrSplitParser {
    public List<SQLSplit> split(String input) {
        ISplitListener listener = this.createParserListener();
        Parser parser = this.createParser(input);
        /*parser.addParseListener((ParseTreeListener)listener);*/
        IterativeParseTreeWalker.DEFAULT.walk((ParseTreeListener) listener, this.parse(parser));

        for (SQLSplit sqlSplit : listener.getStatements()) {
            sqlSplit.text = input.substring(sqlSplit.offset, sqlSplit.offset + sqlSplit.length);
        }
        return listener.getStatements();
    }

    public Lexer createLexer(String input) {
        Lexer lexer = this.createLexerFormCharStream(CharStreams.fromString(input));
        lexer.removeErrorListeners();
        lexer.addErrorListener(new BaseErrorListener());
        return lexer;
    }

    public Parser createParser(String input) {
        Lexer lexer = this.createLexer(input);
        CommonTokenStream tokenStream = new CommonTokenStream(lexer);
        Parser parser = this.createParserFromTokenStream(tokenStream);
        parser.removeErrorListeners();
        parser.addErrorListener(new BaseErrorListener());
        return parser;
    }

    protected abstract ParserRuleContext parse(Parser parser);

    protected abstract Lexer createLexerFormCharStream(CharStream charStream);

    protected abstract Parser createParserFromTokenStream(CommonTokenStream tokenStream);

    protected abstract ISplitListener createParserListener();
}

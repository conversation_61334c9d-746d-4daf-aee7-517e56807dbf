<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>


<plugin>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
            class="com.dc.summer.exec.unity.UnityDataSourceProvider"
            description="Unity connector"
            id="unity"
            label="Unity"
            dialect="mysql">
            <drivers managable="true">
                <driver
                        id="unity"
                        label="Unity"
                        class="org.apache.calcite.jdbc.Driver"
                        sampleURL="jdbc:calcite:"
                        webURL="https://calcite.apache.org/javadocAggregate/org/apache/calcite/jdbc/package-summary.html"
                        description="Driver for Unity"
                        promoted="1"
                        categories="sql">
                </driver>
            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="calcite_sql" parent="basic" class="com.dc.summer.exec.unity.model.UnitySQLDialect" label="Unity SQL"/>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.exec.unity.data.UnityValueHandlerProvider"
                description="Exec Unity data types provider"
                id="com.dc.summer.exec.unity.data.UnityValueHandlerProvider"
                label="Exec Unity data types provider">

            <datasource id="unity"/>

            <type kind="BOOLEAN"/>
            <type kind="NUMERIC"/>
            <type kind="STRING"/>
            <type kind="DATETIME"/>
            <type kind="BINARY"/>
            <type kind="CONTENT"/>
            <type kind="STRUCT"/>
            <type kind="DOCUMENT"/>
            <type kind="ARRAY"/>
            <type kind="OBJECT"/>
            <type kind="REFERENCE"/>
            <type kind="ROWID"/>
            <type kind="ANY"/>
            <type kind="ROW"/>
            <type kind="UNKNOWN"/>

        </provider>
    </extension>

</plugin>

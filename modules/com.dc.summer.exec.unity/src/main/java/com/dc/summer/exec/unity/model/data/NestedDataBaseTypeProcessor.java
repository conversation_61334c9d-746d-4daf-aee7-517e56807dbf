package com.dc.summer.exec.unity.model.data;

import com.dc.summer.exec.unity.model.UnityUtils;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDAttributeBindingProcessor;
import com.dc.summer.model.exec.DBCAttributeMetaData;
import com.dc.summer.model.impl.jdbc.exec.JDBCColumnMetaData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class NestedDataBaseTypeProcessor implements DBDAttributeBindingProcessor {

    private final DBPConnectionConfiguration connectionConfiguration;

    @Override
    public void processAndBind(DBDAttributeBinding[] bindings) {

        UnityUtils.transformNestedConfig(connectionConfiguration, nestedConnectionConfiguration -> {

            for (DBDAttributeBinding binding : bindings) {
                DBCAttributeMetaData metaAttribute = binding.getMetaAttribute();

                if (metaAttribute instanceof JDBCColumnMetaData) {
                    JDBCColumnMetaData metaData = (JDBCColumnMetaData) metaAttribute;

                    String resultConcatName = DBUtils.getConcatName(UnityUtils.STRUCT_SEPARATOR, metaData.getCatalogName(), metaData.getSchemaName());
                    if (nestedConnectionConfiguration.getConcatName().equals(resultConcatName)) {

                        binding.setDbType(nestedConnectionConfiguration.getDatabaseType().getValue());
                    }
                }
            }
        });
    }
}

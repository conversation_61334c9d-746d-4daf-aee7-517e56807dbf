package com.dc.summer.exec.unity.model.data;

import com.dc.summer.exec.unity.model.UnityUtils;
import com.dc.summer.model.DBPCloseableObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.type.DatabaseType;
import lombok.Data;
import lombok.ToString;


@ToString(callSuper = true)
@Data
public class NestedConnectionConfiguration extends DBPConnectionConfiguration {

    private NestedConnectionConfiguration next;

    private String dbLinkName;
    private String catalogName;
    private String schemaName;
    private String tableName;

    private DBRProgressMonitor progressMonitor;

    private DBCExecutionContext executionContext;

    private DBPCloseableObject closeableObject;

    private DatabaseType databaseType;

    public String getConcatName() {
        return DBUtils.getConcatName(UnityUtils.STRUCT_SEPARATOR, dbLinkName, catalogName, schemaName);
    }

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

}

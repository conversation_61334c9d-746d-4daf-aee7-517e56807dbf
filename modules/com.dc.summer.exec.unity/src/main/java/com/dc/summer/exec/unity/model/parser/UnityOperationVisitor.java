package com.dc.summer.exec.unity.model.parser;

import org.apache.calcite.sql.SqlCall;
import org.apache.calcite.sql.SqlKind;
import org.apache.calcite.sql.SqlSelect;
import org.apache.calcite.sql.util.SqlBasicVisitor;

import java.util.concurrent.atomic.AtomicReference;

public class UnityOperationVisitor extends SqlBasicVisitor<String> {

    private final AtomicReference<String> operationReference = new AtomicReference<>();

    private final String operation;

    public UnityOperationVisitor(String operation) {
        this.operation = operation;
    }

    @Override
    public String visit(SqlCall sqlCall) {
        super.visit(sqlCall);
        if (operationReference.get() == null) {
            if (sqlCall instanceof SqlSelect) {
                operationReference.set(SqlKind.SELECT.name());
            } else {
                operationReference.set(operation);
            }
        }
        return operationReference.get();
    }

}

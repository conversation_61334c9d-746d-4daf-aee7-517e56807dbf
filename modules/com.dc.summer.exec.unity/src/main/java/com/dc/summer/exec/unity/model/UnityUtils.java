package com.dc.summer.exec.unity.model;

import com.dc.function.ConsumerFunction;
import com.dc.summer.exec.unity.model.data.NestedConnectionConfiguration;
import com.dc.summer.exec.unity.model.parser.UnityOperationVisitor;
import com.dc.summer.exec.unity.model.parser.UnityParserModel;
import com.dc.summer.exec.unity.model.parser.UnityTableNameVisitor;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.util.SqlBasicVisitor;
import org.apache.calcite.sql.validate.SqlConformanceEnum;

import java.util.List;

@Slf4j
public class UnityUtils {

    private UnityUtils() {
    }

    public static final String STRUCT_SEPARATOR = "-";

    public static final SqlConformanceEnum SQL_CONFORMANCE_ENUM = SqlConformanceEnum.BABEL;

    @SneakyThrows(Exception.class)
    public static void transformNestedConfig(DBPConnectionConfiguration configuration, ConsumerFunction<NestedConnectionConfiguration> consumer) {

        if (configuration instanceof NestedConnectionConfiguration) {
            NestedConnectionConfiguration nestedConnectionConfiguration = (NestedConnectionConfiguration) configuration;

            while ((nestedConnectionConfiguration = nestedConnectionConfiguration.getNext()) != null) {
                consumer.accept(nestedConnectionConfiguration);
            }
        }

    }

    public static UnityParserModel parse(String sql) throws SqlParseException {
        SqlParser.Config parserConfig = SqlParser.configBuilder()
                .setConformance(SQL_CONFORMANCE_ENUM)
                .build();
        SqlParser parser = SqlParser.create(sql, parserConfig);

        SqlNode sqlNode = parser.parseQuery();

        UnityParserModel unityParserModel = new UnityParserModel();
        unityParserModel.setOperation(sqlNode.accept(new UnityOperationVisitor(sqlNode.getKind().name())));
        unityParserModel.setTableNames(sqlNode.accept(new UnityTableNameVisitor()));

        return unityParserModel;
    }

}

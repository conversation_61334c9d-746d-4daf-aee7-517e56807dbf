package com.dc.summer.exec.unity.model.data;

import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import lombok.RequiredArgsConstructor;

import javax.sql.DataSource;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.util.logging.Logger;

@RequiredArgsConstructor
public class NestedDataSource implements DataSource {


    private final NestedConnectionConfiguration nestedConnectionConfiguration;

    @Override
    public Connection getConnection() throws SQLException {
        try {
            DBCExecutionContext context = nestedConnectionConfiguration.getExecutionContext();
            if (context instanceof JDBCExecutionContext) {
                Connection connection = ((JDBCExecutionContext) context).getConnection(nestedConnectionConfiguration.getProgressMonitor());
                return new NestedConnection(connection);
            } else {
                throw new SQLException("Not Supports Context : " + context);
            }
        } catch (Exception e) {
            throw new SQLException(e);
        }
    }

    @Override
    public Connection getConnection(String username, String password) throws SQLException {
        throw new UnsupportedOperationException();
    }

    @Override
    public PrintWriter getLogWriter() throws SQLException {
        throw new UnsupportedOperationException();
    }

    @Override
    public void setLogWriter(PrintWriter out) throws SQLException {
        throw new UnsupportedOperationException();
    }

    @Override
    public void setLoginTimeout(int seconds) throws SQLException {
        throw new UnsupportedOperationException();
    }

    @Override
    public int getLoginTimeout() throws SQLException {
        throw new UnsupportedOperationException();
    }

    @Override
    public Logger getParentLogger() throws SQLFeatureNotSupportedException {
        throw new UnsupportedOperationException();
    }

    @Override
    public <T> T unwrap(Class<T> iface) throws SQLException {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        throw new UnsupportedOperationException();
    }
}

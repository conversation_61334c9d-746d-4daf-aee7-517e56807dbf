package com.dc.summer.exec.unity.model;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContextDefaults;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSCatalog;
import com.dc.summer.model.struct.rdb.DBSSchema;

public class UnityExecutionContext extends JDBCExecutionContext implements DBCExecutionContextDefaults<DBSCatalog, DBSSchema> {

    public UnityExecutionContext(JDBCRemoteInstance instance, String purpose) {
        super(instance, purpose);
    }

    @NotNull
    @Override
    public UnityExecutionContext getContextDefaults() {
        return this;
    }


    @Override
    public void close() {
        UnityUtils.transformNestedConfig(this.getConfiguration(),
                nestedConnectionConfiguration -> nestedConnectionConfiguration.getCloseableObject().close());
        super.close();
    }

    @Override
    public DBSCatalog getDefaultCatalog() {
        return null;
    }

    @Override
    public DBSSchema getDefaultSchema() {
        return null;
    }

    @Override
    public boolean supportsCatalogChange() {
        return false;
    }

    @Override
    public boolean supportsSchemaChange() {
        return false;
    }

    @Override
    public void setDefaultCatalog(DBRProgressMonitor monitor, DBSCatalog catalog, DBSSchema schema, boolean force) throws DBCException {
    }

    @Override
    public void setDefaultSchema(DBRProgressMonitor monitor, DBSSchema schema, boolean force) throws DBCException {
    }

    @Override
    public boolean refreshDefaults(DBRProgressMonitor monitor, boolean useBootstrapSettings) throws DBException {
        return false;
    }

}

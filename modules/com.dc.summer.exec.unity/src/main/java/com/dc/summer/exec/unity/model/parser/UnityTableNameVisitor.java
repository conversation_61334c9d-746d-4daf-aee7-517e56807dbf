package com.dc.summer.exec.unity.model.parser;

import org.apache.calcite.sql.SqlCall;
import org.apache.calcite.sql.SqlIdentifier;
import org.apache.calcite.sql.util.SqlBasicVisitor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class UnityTableNameVisitor extends SqlBasicVisitor<List<String>> {

    private final List<String> tableNames = new ArrayList<>();

    @Override
    public List<String> visit(SqlCall sqlCall) {
        super.visit(sqlCall);
        return tableNames;
    }

    @Override
    public List<String> visit(SqlIdentifier id) {
        if (id.names.size() > 2) {
            // 识别表名
            Optional<String> tableName = id.names.stream().reduce((s, s2) -> s + "." + s2);
            tableName.ifPresent(tableNames::add);
        }
        return tableNames;
    }

}

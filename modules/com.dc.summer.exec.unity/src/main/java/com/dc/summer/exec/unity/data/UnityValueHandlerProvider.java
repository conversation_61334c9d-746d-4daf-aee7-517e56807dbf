
package com.dc.summer.exec.unity.data;

import com.dc.summer.exec.unity.model.UnityUtils;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.data.DBDValueHandlerProvider;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.jdbc.exec.JDBCColumnMetaData;
import com.dc.summer.model.struct.DBSTypedObject;

import java.util.concurrent.atomic.AtomicReference;


public class UnityValueHandlerProvider implements DBDValueHandlerProvider {

    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {

        AtomicReference<DBDValueHandler> valueHandlerAtomicReference = new AtomicReference<>();

        if (typedObject instanceof JDBCColumnMetaData) {

            JDBCColumnMetaData metaData = (JDBCColumnMetaData) typedObject;
            DBCSession session = DBUtils.getAdapter(DBCSession.class, preferences);

            UnityUtils.transformNestedConfig(session.getExecutionContext().getConfiguration(), nestedConnectionConfiguration -> {
                String resultConcatName = DBUtils.getConcatName(UnityUtils.STRUCT_SEPARATOR, metaData.getCatalogName(), metaData.getSchemaName());
                if (valueHandlerAtomicReference.get() == null && nestedConnectionConfiguration.getConcatName().equals(resultConcatName)) {
                    DBCExecutionContext context = nestedConnectionConfiguration.getExecutionContext();
                    valueHandlerAtomicReference.set(DBUtils.findValueHandler(context.getDataSource(), context.getDataSource().getContainer(), typedObject));
                }
            });
        }

        return valueHandlerAtomicReference.get();
    }

}
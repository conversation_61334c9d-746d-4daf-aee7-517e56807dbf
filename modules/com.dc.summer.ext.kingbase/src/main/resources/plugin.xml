<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>


<plugin>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
                class="com.dc.summer.ext.kingbase.KingBaseDataSourceProvider"
                description="%datasource.kingbase.description"
                id="kingbase"
                label="KingBase"
                icon="icons/kingbase_icon.png"
                dialect="kingbase">

            <drivers managable="true">
                <driver
                        id="kingbase8.6"
                        label="KingBase"
                        icon="icons/kingbase_icon.png"
                        iconBig="icons/kingbase_icon_big.png"
                        class="com.kingbase8.Driver"
                        sampleURL="jdbc:kingbase8://{host}[:{port}]/[{database}]"
                        useURL="true"
                        defaultPort="54321"
                        webURL="https://www.kingbase.com.cn/"
                        description="%driver.kingbase.description"
                        promoted="1"
                        categories="sql"
                        dialect="kingbase">
                    <file type="jar" path="maven:/com.kingbase:kingbase8:8.6.0" bundle="!drivers.kingbase"/>
                </driver>
                <driver
                        id="kingbase8.2"
                        label="KingBase"
                        icon="icons/kingbase_icon.png"
                        iconBig="icons/kingbase_icon_big.png"
                        class="com.kingbase8.Driver"
                        sampleURL="jdbc:kingbase8://{host}[:{port}]/[{database}]"
                        useURL="true"
                        defaultPort="54321"
                        webURL="https://www.kingbase.com.cn/"
                        description="%driver.kingbase.description"
                        promoted="1"
                        categories="sql"
                        dialect="kingbase">
                    <file type="jar" path="maven:/com.kingbase:kingbase8:8.2.0" bundle="!drivers.kingbase"/>
                </driver>
            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="kingbase" parent="postgresql" class="com.dc.summer.ext.kingbase.model.KingBaseDialect" label="KingBase" description="KingBase Dialect on JDBC API information." icon="#database_icon_default" hidden="true">
        </dialect>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.kingbase.model.data.KingBaseValueHandlerProvider"
                description="%provider.data.type.kingbase.description"
                id="com.dc.summer.ext.kingbase.model.data.KingBaseValueHandlerProvider"
                label="%provider.data.type.kingbase.name">

            <datasource id="kingbase"/>

            <type name="*"/>
        </provider>
    </extension>

</plugin>

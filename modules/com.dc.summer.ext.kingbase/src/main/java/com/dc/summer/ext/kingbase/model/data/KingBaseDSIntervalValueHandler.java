package com.dc.summer.ext.kingbase.model.data;

import com.dc.summer.ext.postgresql.model.data.PostgreIntervalValueHandler;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

public class KingBaseDSIntervalValue<PERSON>and<PERSON> extends PostgreIntervalValueHandler {

    public static final KingBaseDSIntervalValueHandler INSTANCE = new KingBaseDSIntervalValueHandler();

    @Override
    public void bindParameter(JDBCSession session, JDBCPreparedStatement statement, DBSTypedObject paramType, int paramIndex, Object value) throws SQLException {
        if (value == null) {
            statement.setNull(paramIndex, paramType.getTypeID());
        } else {
            statement.setString(paramIndex, value.toString());
        }
    }
}

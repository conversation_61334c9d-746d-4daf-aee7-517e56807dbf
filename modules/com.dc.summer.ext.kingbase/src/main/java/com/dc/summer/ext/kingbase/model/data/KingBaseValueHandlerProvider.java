package com.dc.summer.ext.kingbase.model.data;

import com.dc.summer.ext.postgresql.model.data.PostgreValueHandlerProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

public class KingBaseValueHandlerProvider extends PostgreValueHandlerProvider {

    private static final String DSINTERVAL = "dsinterval";

    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {
        switch (typedObject.getTypeName()) {
            case DSINTERVAL:
                return KingBaseDSIntervalValueHandler.INSTANCE;
            default:
                return super.getValueHandler(dataSource, preferences, typedObject);
        }
    }
}

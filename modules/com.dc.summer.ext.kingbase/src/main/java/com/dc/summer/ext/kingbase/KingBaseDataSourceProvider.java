package com.dc.summer.ext.kingbase;

import com.dc.summer.DBException;
import com.dc.summer.ext.kingbase.model.KingBaseDataSource;
import com.dc.summer.ext.postgresql.PostgreDataSourceProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.jdbc.JDBCURL;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class KingBaseDataSourceProvider extends PostgreDataSourceProvider {

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        return JDBCURL.generateUrlByTemplate(driver, connectionInfo);
    }

    @Override
    public DBPDataSource openDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new KingBaseDataSource(monitor, container);
    }

}

package com.dc.summer.ext.kingbase.model;

import com.dc.summer.ext.postgresql.model.PostgreDialect;
import com.dc.summer.model.sql.SQLDialect;
import java.util.stream.Stream;


public class KingBaseDialect extends PostgreDialect {

    public KingBaseDialect() {
        super("KingBase", "kingbase");
    }

    public static String[] KINGBASE_FUNCTIONS_AGGREGATE = new String[]{
            "ARRAY_AGG",
            "BIT_AND",
            "BIT_OR",
            "BOOL_AND",
            "BOOLEQ",
            "BOOL_OR",
            "EVERY",
            "JSON_AGG",
            "JSONB_AGG",
            "JSON_OBJECT_AGG",
            "JSONB_OBJECT_AGG",
            "MODE",
            "STRING_AGG",
            "XMLAGG",
            "CORR",
            "COVAR_POP",
            "COVAR_SAMP",
            "STDDEV",
            "STDDEV_POP",
            "STDDEV_SAMP",
            "VARIANCE",
            "VAR_POP",
            "VAR_SAMP"};
    public static String[] KINGBASE_FUNCTIONS_STRING = new String[]{
            "BIT_LENGTH",
            "BTRIM",
            "CHR",
            "CONCAT_WS",
            "CONVERT",
            "CONVERT_FROM",
            "CONVERT_TO",
            "DECODE",
            "ENCODE",
            "INITCAP",
            "LEFT",
            "LENGTH",
            "LPAD",
            "MD5",
            "OVERLAY",
            "PARSE_IDENT",
            "PG_CLIENT_ENCODING",
            "POSITION",
            "QUOTE_IDENT",
            "QUOTE_LITERAL",
            "QUOTE_NULLABLE",
            "REGEXP_MATCH",
            "REGEXP_MATCHES",
            "REGEXP_REPLACE",
            "REGEXP_SPLIT_TO_ARRAY",
            "REGEXP_SPLIT_TO_TABLE",
            "REPLACE",
            "REVERSE",
            "RIGHT",
            "RPAD",
            "SPLIT_PART",
            "STRPOS",
            "SUBSTRING",
            "TO_ASCII",
            "TO_HEX",
            "TRANSLATE",
            "TREAT",
            "VALUE",
            "NANVL"};
    public static String[] KINGBASE_FUNCTIONS_DATETIME = new String[]{
            "AGE",
            "CLOCK_TIMESTAMP",
            "DATE_PART",
            "DATE_TRUNC",
            "ISFINITE",
            "JUSTIFY_DAYS",
            "JUSTIFY_HOURS",
            "JUSTIFY_INTERVAL",
            "MAKE_DATE",
            "MAKE_INTERVAL",
            "MAKE_TIME",
            "MAKE_TIMESTAMP",
            "MAKE_TIMESTAMPTZ",
            "STATEMENT_TIMESTAMP",
            "TIMEOFDAY",
            "TRANSACTION_TIMESTAMP",
            "TO_DSINTERVAL",
            "NUMTODSINTERVAL",
            "NUMTOYMINTERVAL",
            "TO_YMINTERVAL"};
    public static String[] KINGBASE_FUNCTIONS_ARRAY = new String[]{
            "ARRAY_APPEND",
            "ARRAY_CAT",
            "ARRAY_NDIMS",
            "ARRAY_DIMS",
            "ARRAY_FILL",
            "ARRAY_LENGTH",
            "ARRAY_LOWER",
            "ARRAY_POSITION",
            "ARRAY_POSITIONS",
            "ARRAY_PREPEND",
            "ARRAY_REMOVE",
            "ARRAY_REPLACE",
            "ARRAY_TO_STRING",
            "ARRAY_UPPER",
            "CARDINALITY",
            "STRING_TO_ARRAY",
            "UNNEST",
            "NUMBER"};
    public static String[] KINGBASE_FUNCTIONS_INFO = new String[]{
            "STR_TO_DATE",
            "REMAINDER",
            "CURRENT_DATABASE",
            "CURRENT_QUERY",
            "CURRENT_SCHEMA",
            "CURRENT_SCHEMAS",
            "INET_CLIENT_ADDR",
            "INET_CLIENT_PORT",
            "INET_SERVER_ADDR",
            "INET_SERVER_PORT",
            "ROW_SECURITY_ACTIVE",
            "FORMAT_TYPE",
            "TO_REGCLASS",
            "TO_REGPROC",
            "TO_REGPROCEDURE",
            "TO_REGOPER",
            "TO_REGOPERATOR",
            "TO_REGTYPE",
            "TO_REGNAMESPACE",
            "TO_REGROLE",
            "COL_DESCRIPTION",
            "OBJ_DESCRIPTION",
            "SHOBJ_DESCRIPTION",
            "TXID_CURRENT",
            "TXID_CURRENT_IF_ASSIGNED",
            "TXID_CURRENT_SNAPSHOT",
            "TXID_SNAPSHOT_XIP",
            "TXID_SNAPSHOT_XMAX",
            "TXID_SNAPSHOT_XMIN",
            "TXID_VISIBLE_IN_SNAPSHOT",
            "TXID_STATUS",
            "VALUE",
            "KEEP",
            "GROUPING_ID",
            "GROUPING",
            "NANVAL",
            "EXTRACTVALUE",
            "XMLTYPE",
            "XMLSEQUENCE",
            "ORA_HASH"};
    public static String[] KINGBASE_FUNCTIONS_FORMATTING = new String[]{
            "TO_CHAR",
            "TO_DATE",
            "TO_NUMBER",
            "TO_TIMESTAMP",
            "UNISTR",
            "ASCIISTR"};

    @Override
    protected String[] getFunctionsFormatting() {
        return KINGBASE_FUNCTIONS_FORMATTING;
    }

    @Override
    protected String[] getFunctionsInfo() {
        return KINGBASE_FUNCTIONS_INFO;
    }

    @Override
    protected String[] getFunctionsArray() {
        return KINGBASE_FUNCTIONS_ARRAY;
    }

    @Override
    protected String[] getFunctionsAggregate() {
        return KINGBASE_FUNCTIONS_AGGREGATE;
    }

    @Override
    protected String[] getFunctionsString() {
        return KINGBASE_FUNCTIONS_STRING;
    }

    @Override
    protected String[] getFunctionsDatetime() {
        return KINGBASE_FUNCTIONS_DATETIME;
    }

    @Override
    public boolean isDateTimeType(String word) {
        return "DATE".equalsIgnoreCase(word);
    }

    @Override
    public boolean isDateType(String word) {
        return false;
    }

    @Override
    public boolean isTimeType(String word) {
        return Stream.of("TIME", "TIMETZ").anyMatch(s -> s.equalsIgnoreCase(word));
    }

    @Override
    public int getCatalogUsage() {
        return SQLDialect.USAGE_NONE;
    }

    @Override
    public boolean supportsAliasInConditions() {
        return true;
    }

    @Override
    public boolean supportsColumnAutoIncrement() {
        return true;
    }

}

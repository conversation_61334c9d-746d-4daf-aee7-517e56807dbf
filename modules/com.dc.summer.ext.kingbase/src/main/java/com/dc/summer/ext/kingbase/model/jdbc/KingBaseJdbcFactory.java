
package com.dc.summer.ext.kingbase.model.jdbc;

import com.dc.code.NotNull;
import com.dc.summer.ext.postgresql.model.jdbc.PostgreJdbcFactory;
import com.dc.summer.ext.postgresql.model.jdbc.PostgreResultSetMetaDataImpl;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCResultSetMetaData;

import java.sql.SQLException;

public class KingBaseJdbcFactory extends PostgreJdbcFactory {
    @Override
    public JDBCResultSetMetaData createResultSetMetaData(@NotNull JDBCResultSet resultSet) throws SQLException {
        return new PostgreResultSetMetaDataImpl(resultSet);
    }
}

package com.dc.summer.parser.sql.constants;

import java.util.List;

public class SqlConstant {

    // type
    public static final String KEY_TABLE = "TABLE";
    public static final String KEY_VIEW = "VIEW";
    public static final String KEY_INDEX = "INDEX";
    public static final String KEY_JOB = "JOB";
    public static final String KEY_USER = "USER";
    public static final String KEY_SCHEMA = "SCHEMA";
    public static final String KEY_COLUMN = "COLUMN";
    public static final String KEY_TRIGGER = "TRIGGER";
    public static final String KEY_TEMPLATE = "TEMPLATE";
    public static final String KEY_SEQUENCE = "SEQUENCE";
    public static final String FUNCTION = "FUNCTION";
    public static final String PACKAGE = "PACKAGE";
    public static final String PROCEDURE = "PROCEDURE";
    public static final String KEY_TABLESPACE = "TABLESPACE";
    public static final String KEY_DATABASE_LINK = "DATABASE_LINK";
    public static final String KEY_SYNONYM = "SYNONYM";
    public static final String KEY_ANONYMOUS_BLOCK = "ANONYMOUS_BLOCK";
    public static final String KEY_RESOURCE_COST = "RESOURCE_COST";
    public static final String KEY_STATS = "STATS";
    public static final String KEY_METADATA = "METADATA";
    public static final String KEY_FUNCTIONS = "FUNCTIONS";
    public static final String KEY_TESTCASE = "TESTCASE";

    // keyword
    public static final String KEY_ORDER = "ORDER";
    public static final String KEY_ORDER_BY = "ORDER BY";
    public static final String KEY_GROUP = "GROUP";
    public static final String KEY_GROUP_BY = "GROUP BY";
    public static final String KEY_DISTINCT = "DISTINCT";
    public static final String KEY_TABLE_DUAL = "DUAL";
    public static final String KEY_TABLE_SYSDUMMY1 = "SYSDUMMY1";
    public static final String KEY_BEGIN = "BEGIN";
    public static final String KEY_END = "END";
    public static final String KEY_GO = "GO";
    public static final String KEY_WITH = "WITH";
    public static final String KEY_DECLARE = "DECLARE";
    public static final String KEY_SP_RENAME = "SP_RENAME";
    public static final String KEY_COUNT = "COUNT";
    public static final String KEY_DO = "DO";

    public static final String KEY_TS_VECTOR = "TSVECTOR";
    public static final String KEY_TS_QUERY = "TSQUERY";

    // symbol
    public static final String KEY_SLASH = "/";
    public static final String KEY_BRACE = "{}";
    public static final String KEY_DOUBLE_COLON = "::";

    // operation
    public static final String KEY_SET = "SET";
    public static final String KEY_UPDATE = "UPDATE";
    public static final String KEY_INSERT = "INSERT";
    public static final String KEY_DELETE = "DELETE";
    public static final String KEY_SELECT = "SELECT";
    public static final String KEY_MERGE = "MERGE";
    public static final String KEY_CREATE = "CREATE";
    public static final String KEY_ALTER = "ALTER";
    public static final String KEY_COMMENT = "COMMENT";
    public static final String KEY_EXPLAIN = "EXPLAIN";
    public static final String KEY_COMMIT = "COMMIT";
    public static final String KEY_REVOKE = "REVOKE";
    public static final String KEY_ROLLBACK = "ROLLBACK";
    public static final String KEY_CALL = "CALL";
    public static final String KEY_EXEC = "EXEC";
    public static final String KEY_EXECUTE = "EXECUTE";
    public static final String KEY_SHOW = "SHOW";
    public static final String KEY_DESC = "DESC";
    public static final String KEY_DESCRIBE = "DESCRIBE";
    public static final String KEY_GRANT = "GRANT";
    public static final String KEY_DROP = "DROP";
    public static final String KEY_TRUNCATE = "TRUNCATE";
    public static final String KEY_DATABASE = "DATABASE";
    public static final String KEY_USE = "USE";
    public static final String KEY_VALUES = "VALUES";
    public static final String KEY_RENAME = "RENAME";
    public static final String KEY_LOAD = "LOAD";
    public static final String KEY_LOCK = "LOCK";
    public static final String KEY_REPLACE = "REPLACE";
    public static final String KEY_OPTIMIZE = "OPTIMIZE";
    public static final String KEY_ATTACH = "ATTACH";
    public static final String KEY_DETACH = "DETACH";
    public static final String KEY_CHECK = "CHECK";
    public static final String KEY_EXPORT = "EXPORT";
    public static final String KEY_EXPORT_OPERATION = "EXPORT_OPERATION";
    public static final String KEY_IMPORT = "IMPORT";
    public static final String KEY_SAVE = "SAVE";
    public static final String KEY_DENY = "DENY";
    public static final String KEY_ENABLE = "ENABLE";
    public static final String KEY_DISABLE = "DISABLE";
    public static final String KEY_SUBMIT = "SUBMIT";
    public static final String KEY_CANCEL = "CANCEL";
    public static final String KEY_COMPUTE = "COMPUTE";
    public static final String KEY_VACUUM = "VACUUM";
    public static final String KEY_BACKUP = "BACKUP";
    public static final String KEY_RESTORE = "RESTORE";
    public static final String KEY_CONVERSATION = "CONVERSATION";
    public static final String KEY_RECONFIGURE = "RECONFIGURE";
    public static final String KEY_AUTHORIZATION = "AUTHORIZATION";
    public static final String KEY_CONTROL = "CONTROL";
    public static final String KEY_OPEN = "OPEN";
    public static final String STATISTICS = "STATISTICS";
    public static final String KEY_BEGIN_END = "BEGIN_END";
    public static final String KEY_SPOOL = "SPOOL";
    public static final String KEY_STAT = "STAT";
    public static final String KEY_FLASHBACK = "FLASHBACK";
    public static final String KEY_PURGE = "PURGE";
    public static final String KEY_NOAUDIT = "NOAUDIT";
    public static final String KEY_SWITCH = "SWITCH";
    public static final String KEY_SYSTEM_ACTION = "SYSTEM";
    public static final String KEY_BINLOG = "BINLOG";
    public static final String KEY_DATABASE_OPERATION = "DATABASE_OPERATION";
    public static final String KEY_ALLOCATE = "ALLOCATE";
    public static final String KEY_ASSOCIATE = "ASSOCIATE";
    public static final String KEY_CURSOR = "CURSOR";
    public static final String KEY_GLOBAL_TEMPORARY_TABLE = "GLOBAL_TEMPORARY_TABLE";
    public static final String KEY_DISCONNECT = "DISCONNECT";
    public static final String KEY_VARIABLE = "VARIABLE";
    public static final String KEY_REFRESH = "REFRESH";
    public static final String KEY_ADD = "ADD";
    public static final String KEY_CACHE = "CACHE";
    public static final String KEY_IMMEDIATE = "IMMEDIATE";
    public static final String KEY_LIST = "LIST";
    public static final String KEY_HIVE_COMMAND = "HIVE_COMMAND";
    public static final String KEY_SHUTDOWN = "SHUTDOWN";
    public static final String KEY_INVALIDATE = "INVALIDATE";
    public static final String KEY_COPY = "COPY";
    public static final String KEY_UPSERT = "UPSERT";
    public static final String KEY_EXISTS = "EXISTS";
    public static final String KEY_KILL = "KILL";
    public static final String KEY_EXCHANGE = "EXCHANGE";
    public static final String KEY_MOVE = "MOVE";
    public static final String KEY_UN_DROP = "UN_DROP";
    public static final String KEY_WATCH = "WATCH";
    public static final String KEY_RECOVER = "RECOVER";
    public static final String KEY_UN_SET = "UN_SET";
    public static final String KEY_VALIDATE = "VALIDATE";
    public static final String KEY_UNKNOWN = "UNKNOWN";

    // special
    public static final String KEY_ORACLE_JOB = "ORACLE_JOB";
    public static final String KEY_MSSQL_WORK = "WORK";
    public static final String KEY_AGGREGATE = "AGGREGATE";
    public static final String KEY_CREATE_COLLECTION = "createCollection";
    public static final String KEY_CREATE_VIEW = "createView";
    public static final String KEY_CREATE_INDEX = "createIndex";
    public static final String KEY_CREATE_INDEXES = "createIndexes";
    public static final String KEY_DROP_INDEX = "dropIndex";
    public static final String KEY_DROP_INDEXES = "dropIndexes";
    public static final String KEY_FIND = "find";
    public static final String KEY_FIND_AND_MODIFY = "findAndModify";
    public static final String KEY_FIND_ONE = "findOne";
    public static final String KEY_FIND_ONE_AND_DELETE = "findOneAndDelete";
    public static final String KEY_FIND_ONE_AND_REPLACE = "findOneAndReplace";
    public static final String KEY_FIND_ONE_AND_UPDATE = "findOneAndUpdate";
    public static final String KEY_DROP_DATABASE = "dropDatabase";
    public static final String KEY_AGGS = "AGGS";
    public static final String KEY_CREATE_TABLE = "CREATE_TABLE";
    public static final String KEY_COLLATION = "COLLATION";
    public static final String KEY_CONVERSION = "CONVERSION";
    public static final String KEY_DOMAIN = "DOMAIN";
    public static final String KEY_EXTENSION = "EXTENSION";
    public static final String KEY_PREPARE = "PREPARE";
    public static final String KEY_RELEASE = "RELEASE";
    public static final String KEY_CLOSE = "CLOSE";

    public static final String[] DDL_OPERATION = {
            SqlConstant.KEY_CREATE, SqlConstant.KEY_DROP, SqlConstant.KEY_ALTER, SqlConstant.KEY_TRUNCATE, SqlConstant.KEY_COMMENT
    };
    public static final String[] CALL_DDL_UTIL = {
            SqlConstant.KEY_ALTER, SqlConstant.KEY_CREATE, SqlConstant.KEY_DROP, SqlConstant.KEY_TRUNCATE, SqlConstant.KEY_COMMENT,
            SqlConstant.KEY_RENAME, SqlConstant.KEY_OPTIMIZE, SqlConstant.KEY_ATTACH, SqlConstant.KEY_DETACH, SqlConstant.KEY_CHECK, SqlConstant.KEY_SP_RENAME,
            SqlConstant.KEY_ENABLE, SqlConstant.KEY_DISABLE
    };
    public static final String[] CALL_CRUD_UTIL = {
            SqlConstant.KEY_SELECT, SqlConstant.KEY_INSERT, SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE,
            SqlConstant.KEY_MERGE, SqlConstant.KEY_WITH, SqlConstant.KEY_EXPLAIN, SqlConstant.KEY_TABLE, SqlConstant.KEY_LOAD,
            SqlConstant.KEY_REPLACE, SqlConstant.KEY_IMPORT, SqlConstant.KEY_EXPORT
    };
    public static final String[] CALL_FUNCTION_UTIL = {
            SqlConstant.KEY_CALL, SqlConstant.KEY_EXEC, SqlConstant.KEY_EXECUTE, SqlConstant.KEY_SHOW, SqlConstant.KEY_DESC,
            SqlConstant.KEY_DESCRIBE, SqlConstant.KEY_VALUES, SqlConstant.KEY_LOCK, SqlConstant.KEY_SAVE, SqlConstant.KEY_BEGIN, SqlConstant.KEY_COMPUTE
    };
    public static final String[] MONGO_CAN_EDIT = {
            KEY_FIND, KEY_FIND_AND_MODIFY, KEY_FIND_ONE, KEY_FIND_ONE_AND_DELETE, KEY_FIND_ONE_AND_REPLACE, KEY_FIND_ONE_AND_UPDATE
    };

    public static final String[] POST_GRE_SQL_TEXT_SEARCH_TYPES = {
            KEY_TS_VECTOR, KEY_TS_QUERY
    };

    public static final List<String> STA_SEL = List.of(STATISTICS, KEY_SELECT);

}

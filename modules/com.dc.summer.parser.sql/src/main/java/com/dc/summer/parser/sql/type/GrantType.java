package com.dc.summer.parser.sql.type;

public enum GrantType {

    COMMIT("COMMIT"),
    R<PERSON><PERSON><PERSON><PERSON><PERSON>("ROLL<PERSON>CK"),

    // DCL
    GRANT("GRANT"),
    REVOKE("<PERSON><PERSON><PERSON><PERSON>"),

    // DDL
    ALTER("ALTER"),
    CREATE("CREATE"),
    DROP("DROP"),
    TRUNCATE("TRUNCATE"),
    COMMENT("COMMENT"),

    // DQL
    SELECT("SELECT"),

    // DML
    INSERT("INSERT"),
    UPDATE("UPDATE"),
    DELETE("DELETE"),

    // 函数
    COUNT("COUNT"),
    SUM("SUM"),
    AVG("AVG"),
    MAX("MAX"),
    MIN("MIN"),
    GROUP("GROUP"),
    FIRST("FIRST"),
    LAST("LAST"),
    ORDER("ORDER"),
    DISTINCT("DISTINCT"),

    CALL("CALL"),
    EXEC("EXEC"),
    SHOW("SHOW"),
    DESC("DESC"),
    DESCRIBE("DESCRIBE"),
    EDIT("EDIT"),
    DETAIL("DETAIL"),
    EXPORT("EXPORT"),

    selectTarget("select_target"), // 查询
    alterTarget("alter_target"), // 变更
    riskTarget("risk_target"), // 高危
    manageTarget("manage_target"), // 管理
    callTarget("call_target"); // 执行

    String value;

    GrantType(String type) {
        this.value = type;
    }

    public String getValue() {
        return value;
    }

    public static GrantType getTypeByCode(String code) {
        for (GrantType status : GrantType.values()) {
            if (status.getValue().equals(code)) {
                return status;
            }
        }
        return GrantType.GRANT;
    }
}

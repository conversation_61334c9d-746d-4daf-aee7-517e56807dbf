package com.dc.summer.parser.sql.utils;


import com.dc.summer.parser.sql.type.RegularType;

import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegularUtil {

    public static String transformSql(String sql, Integer regularType) {

        if (RegularType.PARTITION.getValue().equals(regularType)) {
            return sql.replaceFirst("(?i)PARTITION\\s+([0-9a-zA-Z_\"'`]+|\\([0-9a-zA-Z_\"'\\[\\]`.:,=\\s]+\\))", " ");
        } else if (RegularType.MERGE_INTO_WHERE.getValue().equals(regularType)) {
            int index = sql.toLowerCase(Locale.ROOT).indexOf("where");
            if (index > 0) {
                return sql.substring(0, index);
            }
        } else if (RegularType.DOUBLE_COLON.getValue().equals(regularType)) {
            return sql.replaceAll("::([0-9a-zA-Z_\\(\\):\\s]+),", ",");
        } else if (RegularType.OFFSET.getValue().equals(regularType)) {
            return sql.replaceAll("(?i)OFFSET\\s+([0-9]+)", " ");
        } else if (RegularType.STRUCT.getValue().equals(regularType)) {
            return sql.replaceAll("(?i)struct\\s*<[0-9a-zA-Z_\"'\\[\\]`.,\\s]+>", "struct");
        } else if (RegularType.OVERWRITE_TO_INTO.getValue().equals(regularType)) {
            return sql.replaceFirst("(?i)OVERWRITE", "INTO");
        } else if (RegularType.FETCH_FIRST_ROW_WITH_TIES.getValue().equals(regularType)) {
            return sql.replaceFirst("(?i)FETCH\\s+FIRST\\s+ROW\\s+WITH\\s+TIES", " ");
        } else if (RegularType.PARTITIONS.getValue().equals(regularType)) {
            return sql.replaceFirst("(?i)PARTITIONS\\s+", " ");
        } else if (RegularType.LIMIT.getValue().equals(regularType)) {
            return sql.replaceFirst("(?i)\\s+limit\\s+([0-9]+)\\s*;*$", " ");
        } else if (RegularType.ROW_ARCHIVAL.getValue().equals(regularType)) {
            return sql.replaceAll("(?i)\\bno\\s+ROW\\s+ARCHIVAL\\b|\\bROW\\s+ARCHIVAL\\b", " ADD new_column NUMBER");
        } else if (RegularType.EXCEPT.getValue().equals(regularType)) {
            return sql.replaceAll("(?i)\\bexcept\\b", "UNION");
        } else if (RegularType.MINUS.getValue().equals(regularType)) {
            return sql.replaceAll("(?i)\\bminus\\b", "UNION");
        } else if (RegularType.START_TRANSACTION.getValue().equals(regularType)) {
            return sql.replaceAll("(?i)\\bSTART TRANSACTION\\b", "");
        } else if (RegularType.REMOVE_SQL_LINE_COMMENTS.getValue().equals(regularType)) {
            return Pattern.compile("--.*?$", Pattern.MULTILINE).matcher(sql).replaceAll("");
        } else if (RegularType.TDPG_EXECUTE_DIRECT_ON_DN.getValue().equals(regularType)) {
            String regex = "^(?s)\\s*EXECUTE\\s+DIRECT\\s+ON\\s*\\(([^()\\s]+)\\)\\s*(?:'(\\s*(WITH.*)?\\s*(?i)SELECT.*)'|\"(\\s*(WITH.*)?\\s*(?i)SELECT.*)\")";
            Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(sql);
            if (matcher.find()) {
                if (matcher.group(2) != null) {
                    String group2 = matcher.group(2);
                    //''在td pg中是单引号转义，需要这里手动转义一下。
                    group2 = group2.replaceAll("''([^']+)''", "'$1'");
                    return group2;
                } else if (matcher.group(4) != null) {
                    String group4 = matcher.group(4);
                    group4 = group4.replaceAll("\"\"", "\"");
                    return group4;
                }
            }
        } else if (RegularType.DORIS_REMOVE_PROPERTIES_OP.getValue().equals(regularType)) {
            String regex = "\\s*(?i)PROPERTIES\\s*\\([^)]*\\)";
            return sql.replaceFirst(regex, " ");
        } else if (RegularType.DORIS_DELETE_REMOVE_PARTITION.getValue().equals(regularType)) {
            String regex = "(\\s*\\bPARTITION\\b\\s+[a-zA-Z0-9_\\.]+\\s* | \\s*\\bPARTITIONS\\b\\s*\\([^)]+\\)\\s*)";
            return sql.replaceAll(regex, " ");
        } else if (RegularType.DORIS_SELECT_REMOVE_ALLEXCEPT.getValue().equals(regularType)) {
            String regex = "(?si)\\s+EXCEPT\\s*\\([0-9a-zA-Z_@$%`\\.]+(\\s*,\\s*[0-9a-zA-Z`.]+)*\\)";
            return sql.replaceAll(regex, " ");
        }

        return sql;
    }

}

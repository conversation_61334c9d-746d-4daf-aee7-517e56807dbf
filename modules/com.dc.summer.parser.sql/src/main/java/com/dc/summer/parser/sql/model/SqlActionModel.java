package com.dc.summer.parser.sql.model;

import com.dc.summer.parser.sql.enums.OracleJobOperationType;

import java.util.*;
import java.util.stream.Collectors;

public class SqlActionModel {

    private String objectName; // 表名或对象名
    private Set<String> tables; // 所有表名的集合
    private Set<String> columns; // 所有字段名的集合,脱敏用
    private Set<String> functions = new HashSet<>(); // 所有函数的集合,鉴权用
    private Set<String> staticsFunctions = new HashSet<>(); // 所有统计函数的集合,鉴权用
    public Map<String, Set<String>> statisticsFuncMap;
    private final Map<String, Set<String>> staFuncMap = new HashMap<>(); //新的map
    private Map<String, String> alias; // 字段对应的别名,脱敏用
    private boolean isSelectInto = false; // 是select into ... sql
    private String explainOperation; // 是explain select/delete/update ... sql
    private boolean isInsertIntoSelect = false; // 是insert into select ... sql

    //私有表需求需要
    private boolean isCreateTableAsSelect = false; //是create table as select
    private boolean isUpdateSelect = false; //是update xxx set xxx = select ...
    private boolean needChangeMessage = false;  //需要强行改变无权限提示信息（目前只针对 私有表需求）

    private boolean isMerge = false; // 是merge ... sql
    private boolean isInsertAllFirst = false; // 是insert all/first ... sql
    private Set<String> insertAllFirstTables; // insert all/first ... sql对应的select表
    private List<String> funcArgs; // 存储过程/函数的参数,鉴权用
    private boolean isDefaultSchema = false; // 是否不带schemaName
    private boolean isSelectFunction = false; // 是select function
    private Set<String> tablesInWhereClause; // 在where条件中的表,如update tab1  where (select tab2)
    private boolean isChangeSchema = false; // 是否是切换数据库sql
    private Map<String, List<String>> aliasBk; // 字段对应的别名备份,脱敏用
    private String ckOperation; // clickhouse的update/delete语法: alter table tab1 update/delete .....
    private String ckWhere; // clickhouse的update/delete语法对应的where条件,影响行数用
    private String ckInsertSelectSql; // clickhouse的insert into select ... sql,影响行数用
    private boolean isSelectSequence = false; // 是select Sequence
    private boolean isDuplicateKeyUpdate = false; // insert into ... on duplicate key update ... 若主键冲突则更新的sql
    private boolean hasWhereClause = false; // show语法中带有where: show ... where ...
    private Set<String> needOtherOperations; // 同一个sql需要两种权限判定,如 insert into select...
    private boolean containsDBLink; // 是否包含dbLink
    private boolean needCreateSchema; // 是否会同时创建数据库(db2 建表时指定一个不存在的schema,会先创建这个schema)
    private boolean isSelectValue = false; // db2: values current schema;
    private boolean containsBackupTable; // 含有可以备份的表
    private int transactionStatus;
    private Boolean forceBackup; // 强制需要备份
    private boolean isDescDatabase; // heTu: describe database qyy1;
    private Set<String> dblinkTables; // dblink相关表

    private Set<String> multiInsertTables; // 同时insert多个表的情况

    private List<ColumnData> columnDataList; // select的所有字段及所属表

    private boolean isTemplate; // 是es的模板

    private boolean showReason; // 显示不备份的原因

    private Set<String> valuesSelectTables;

    private boolean hasCaseWhen;
    private boolean hasDistinct;
    private boolean hasGroupBy;
    private boolean hasJoin;
    private boolean hasUnion;
    private boolean hasWithClause;
    private boolean hasSubQuery;

    private boolean isInsertIgnore;
    private boolean isReplace;

    private boolean isSelectForUpdate;

    private boolean isAnonymousBlock;

    private List<OracleJobOperationType> oracleJobOperates = new ArrayList<>();

    public SqlActionModel() {
    }

    /**
     * 合并两个 SqlActionModel 对象。
     *
     * @param model1 第一个模型，作为基础。
     * @param model2 第二个模型，其值在冲突时通常具有更高优先级或用于补充。
     * @return 一个新的合并后的 SqlActionModel 实例。
     */
    public static SqlActionModel merge(SqlActionModel model1, SqlActionModel model2) {
        if (model1 == null && model2 == null) {
            return new SqlActionModel(); // 或者返回 null，取决于你的需求
        }
        if (model1 == null) {
            // 如果 model1 为空，可以考虑深度复制 model2 或根据业务返回
            return deepCopy(model2); // 假设有一个 deepCopy 方法
        }
        if (model2 == null) {
            // 如果 model2 为空，可以考虑深度复制 model1 或根据业务返回
            return deepCopy(model1); // 假设有一个 deepCopy 方法
        }

        SqlActionModel mergedResult = new SqlActionModel();

        // 1. 合并 String 字段 (model2 优先)
        mergedResult.setObjectName(model2.getObjectName() != null ? model2.getObjectName() : model1.getObjectName());
        mergedResult.setExplainOperation(model2.getExplainOperation() != null ? model2.getExplainOperation() : model1.getExplainOperation());
        mergedResult.setCkOperation(model2.getCkOperation() != null ? model2.getCkOperation() : model1.getCkOperation());
        mergedResult.setCkWhere(model2.getCkWhere() != null ? model2.getCkWhere() : model1.getCkWhere());
        mergedResult.setCkInsertSelectSql(model2.getCkInsertSelectSql() != null ? model2.getCkInsertSelectSql() : model1.getCkInsertSelectSql());

        // 2. 合并 Set<String> 字段 (并集)
        mergedResult.setTables(mergeSets(model1.getTables(), model2.getTables()));
        mergedResult.setColumns(mergeSets(model1.getColumns(), model2.getColumns()));
        mergedResult.setFunctions(mergeSets(model1.getFunctions(), model2.getFunctions()));
        mergedResult.setStaticsFunctions(mergeSets(model1.getStaticsFunctions(), model2.getStaticsFunctions()));
        mergedResult.setInsertAllFirstTables(mergeSets(model1.getInsertAllFirstTables(), model2.getInsertAllFirstTables()));
        mergedResult.setTablesInWhereClause(mergeSets(model1.getTablesInWhereClause(), model2.getTablesInWhereClause()));
        mergedResult.setNeedOtherOperations(mergeSets(model1.getNeedOtherOperations(), model2.getNeedOtherOperations()));
        mergedResult.setDblinkTables(mergeSets(model1.getDblinkTables(), model2.getDblinkTables()));
        mergedResult.setMultiInsertTables(mergeSets(model1.getMultiInsertTables(), model2.getMultiInsertTables()));
        mergedResult.setValuesSelectTables(mergeSets(model1.getValuesSelectTables(), model2.getValuesSelectTables()));

        // 3. 合并 Map<String, String> alias (model2 覆盖 model1)
        mergedResult.setAlias(mergeStringMaps(model1.getAlias(), model2.getAlias()));

        // 4. 合并 Map<String, Set<String>> statisticsFuncMap (键合并，值Set取并集)
        mergedResult.setStatisticsFuncMap(mergeMapOfSets(model1.getStatisticsFuncMap(), model2.getStatisticsFuncMap()));

        // 5. 特殊处理 final Map<String, Set<String>> staFuncMap
        // staFuncMap 在 mergedResult 中已初始化为空 HashMap
        // 我们需要将 model1 和 model2 的内容合并到 mergedResult.getStaFuncMap() 中
        populateMapOfSets(mergedResult.getStaFuncMap(), model1.getStaFuncMap());
        populateMapOfSets(mergedResult.getStaFuncMap(), model2.getStaFuncMap());


        // 6. 合并 Map<String, List<String>> aliasBk (键合并，值List连接)
        mergedResult.setAliasBk(mergeMapOfLists(model1.getAliasBk(), model2.getAliasBk()));

        // 7. 合并 List<String> funcArgs (连接)
        mergedResult.setFuncArgs(mergeLists(model1.getFuncArgs(), model2.getFuncArgs()));

        // 8. 合并 List<ColumnData> columnDataList (连接)
        mergedResult.setColumnDataList(mergeLists(model1.getColumnDataList(), model2.getColumnDataList()));


        // 9. 合并 boolean 字段 (逻辑或)
        mergedResult.setSelectInto(model1.isSelectInto() || model2.isSelectInto());
        mergedResult.setInsertIntoSelect(model1.isInsertIntoSelect() || model2.isInsertIntoSelect());
        mergedResult.setCreateTableAsSelect(model1.isCreateTableAsSelect() || model2.isCreateTableAsSelect());
        mergedResult.setUpdateSelect(model1.isUpdateSelect() || model2.isUpdateSelect());
        mergedResult.setNeedChangeMessage(model1.isNeedChangeMessage() || model2.isNeedChangeMessage());
        mergedResult.setMerge(model1.isMerge() || model2.isMerge()); // 注意字段名和方法名可能不完全对应，这里假设方法是 isMerge()
        mergedResult.setInsertAllFirst(model1.isInsertAllFirst() || model2.isInsertAllFirst());
        mergedResult.setDefaultSchema(model1.isDefaultSchema() || model2.isDefaultSchema());
        mergedResult.setSelectFunction(model1.isSelectFunction() || model2.isSelectFunction());
        mergedResult.setChangeSchema(model1.isChangeSchema() || model2.isChangeSchema());
        mergedResult.setSelectSequence(model1.isSelectSequence() || model2.isSelectSequence());
        mergedResult.setDuplicateKeyUpdate(model1.isDuplicateKeyUpdate() || model2.isDuplicateKeyUpdate());
        mergedResult.setHasWhereClause(model1.isHasWhereClause() || model2.isHasWhereClause());
        mergedResult.setContainsDBLink(model1.isContainsDBLink() || model2.isContainsDBLink());
        mergedResult.setNeedCreateSchema(model1.isNeedCreateSchema() || model2.isNeedCreateSchema());
        mergedResult.setSelectValue(model1.isSelectValue() || model2.isSelectValue());
        mergedResult.setContainsBackupTable(model1.isContainsBackupTable() || model2.isContainsBackupTable());
        mergedResult.setDescDatabase(model1.isDescDatabase() || model2.isDescDatabase());
        mergedResult.setTemplate(model1.isTemplate() || model2.isTemplate());
        mergedResult.setShowReason(model1.isShowReason() || model2.isShowReason());
        mergedResult.setHasCaseWhen(model1.isHasCaseWhen() || model2.isHasCaseWhen());
        mergedResult.setHasDistinct(model1.isHasDistinct() || model2.isHasDistinct());
        mergedResult.setHasGroupBy(model1.isHasGroupBy() || model2.isHasGroupBy());
        mergedResult.setHasJoin(model1.isHasJoin() || model2.isHasJoin());
        mergedResult.setHasUnion(model1.isHasUnion() || model2.isHasUnion());
        mergedResult.setHasWithClause(model1.isHasWithClause() || model2.isHasWithClause());
        mergedResult.setHasSubQuery(model1.isHasSubQuery() || model2.isHasSubQuery());
        mergedResult.setInsertIgnore(model1.isInsertIgnore() || model2.isInsertIgnore());
        mergedResult.setReplace(model1.isReplace() || model2.isReplace());
        mergedResult.setSelectForUpdate(model1.isSelectForUpdate() || model2.isSelectForUpdate());
        mergedResult.setAnonymousBlock(model1.isAnonymousBlock() || model2.isAnonymousBlock());

        // 10. 合并 int 字段 (model2 优先, 或根据业务选择其他策略, 如求和、取最大值等)
        mergedResult.setTransactionStatus(model2.getTransactionStatus()); // 示例：model2 优先

        // 11. 合并 Boolean 字段 (有一个为 TRUE 则为 TRUE, 否则若都为 null则为 null, 否则为 FALSE)
        Boolean fb1 = model1.isForceBackup();
        Boolean fb2 = model2.isForceBackup();
        if (Boolean.TRUE.equals(fb1) || Boolean.TRUE.equals(fb2)) {
            mergedResult.setForceBackup(Boolean.TRUE);
        } else if (fb1 == null && fb2 == null) {
            mergedResult.setForceBackup(null);
        } else { // 剩下情况：(null, FALSE), (FALSE, null), (FALSE, FALSE)
            mergedResult.setForceBackup(Boolean.FALSE);
        }

        mergedResult.setOracleJobOperates(mergeLists(model1.getOracleJobOperates(), model2.getOracleJobOperates()));

        return mergedResult;
    }

    // --- 辅助合并方法 ---

    private static <T> Set<T> mergeSets(Set<T> set1, Set<T> set2) {
        Set<T> result = new HashSet<>();
        if (set1 != null) {
            result.addAll(set1);
        }
        if (set2 != null) {
            result.addAll(set2);
        }
        return result; // 总是返回一个Set，即使为空
    }

    private static <T> List<T> mergeLists(List<T> list1, List<T> list2) {
        List<T> result = new ArrayList<>();
        if (list1 != null) {
            result.addAll(list1);
        }
        if (list2 != null) {
            result.addAll(list2);
        }
        return result; // 总是返回一个List，即使为空
    }

    private static Map<String, String> mergeStringMaps(Map<String, String> map1, Map<String, String> map2) {
        Map<String, String> result = new HashMap<>();
        if (map1 != null) {
            result.putAll(map1);
        }
        if (map2 != null) {
            result.putAll(map2); // map2 的值会覆盖 map1 中相同的键
        }
        return result;
    }

    private static Map<String, Set<String>> mergeMapOfSets(Map<String, Set<String>> map1, Map<String, Set<String>> map2) {
        Map<String, Set<String>> result = new HashMap<>();
        populateMapOfSets(result, map1);
        populateMapOfSets(result, map2);
        return result;
    }

    /**
     * 将 sourceMap 的内容合并到 targetMap 中。
     * 对于已存在的键，其对应的 Set<String> 会进行并集操作。
     */
    private static void populateMapOfSets(Map<String, Set<String>> targetMap, Map<String, Set<String>> sourceMap) {
        if (sourceMap != null) {
            sourceMap.forEach((key, valueSet) -> {
                targetMap.computeIfAbsent(key, k -> new HashSet<>()).addAll(valueSet);
            });
        }
    }

    private static Map<String, List<String>> mergeMapOfLists(Map<String, List<String>> map1, Map<String, List<String>> map2) {
        Map<String, List<String>> result = new HashMap<>();
        if (map1 != null) {
            map1.forEach((key, valueList) -> {
                result.computeIfAbsent(key, k -> new ArrayList<>()).addAll(valueList);
            });
        }
        if (map2 != null) {
            map2.forEach((key, valueList) -> {
                result.computeIfAbsent(key, k -> new ArrayList<>()).addAll(valueList);
            });
        }
        return result;
    }

    /**
     * 创建一个 SqlActionModel 的深拷贝。
     */
    public static SqlActionModel deepCopy(SqlActionModel original) {
        if (original == null) return null;
        SqlActionModel copy = new SqlActionModel();

        copy.setObjectName(original.getObjectName());
        copy.setExplainOperation(original.getExplainOperation());
        copy.setCkOperation(original.getCkOperation());
        copy.setCkWhere(original.getCkWhere());
        copy.setCkInsertSelectSql(original.getCkInsertSelectSql());

        if (original.getTables() != null) copy.setTables(new HashSet<>(original.getTables()));
        if (original.getColumns() != null) copy.setColumns(new HashSet<>(original.getColumns()));
        if (original.getFunctions() != null) copy.setFunctions(new HashSet<>(original.getFunctions()));
        if (original.getStaticsFunctions() != null)
            copy.setStaticsFunctions(new HashSet<>(original.getStaticsFunctions()));
        if (original.getInsertAllFirstTables() != null)
            copy.setInsertAllFirstTables(new HashSet<>(original.getInsertAllFirstTables()));
        if (original.getTablesInWhereClause() != null)
            copy.setTablesInWhereClause(new HashSet<>(original.getTablesInWhereClause()));
        if (original.getNeedOtherOperations() != null)
            copy.setNeedOtherOperations(new HashSet<>(original.getNeedOtherOperations()));
        if (original.getDblinkTables() != null) copy.setDblinkTables(new HashSet<>(original.getDblinkTables()));
        if (original.getMultiInsertTables() != null)
            copy.setMultiInsertTables(new HashSet<>(original.getMultiInsertTables()));
        if (original.getValuesSelectTables() != null)
            copy.setValuesSelectTables(new HashSet<>(original.getValuesSelectTables()));


        if (original.getAlias() != null) copy.setAlias(new HashMap<>(original.getAlias()));

        if (original.getStatisticsFuncMap() != null) {
            Map<String, Set<String>> statsMapCopy = new HashMap<>();
            original.getStatisticsFuncMap().forEach((k, v) -> statsMapCopy.put(k, new HashSet<>(v)));
            copy.setStatisticsFuncMap(statsMapCopy);
        }

        // 深拷贝 staFuncMap 的内容到新的 final map 中
        if (original.getStaFuncMap() != null) {
            original.getStaFuncMap().forEach((k, v) -> {
                copy.getStaFuncMap().computeIfAbsent(k, key -> new HashSet<>()).addAll(v);
            });
        }


        if (original.getAliasBk() != null) {
            Map<String, List<String>> aliasBkCopy = new HashMap<>();
            original.getAliasBk().forEach((k, v) -> aliasBkCopy.put(k, new ArrayList<>(v)));
            copy.setAliasBk(aliasBkCopy);
        }

        if (original.getFuncArgs() != null) copy.setFuncArgs(new ArrayList<>(original.getFuncArgs()));

        if (original.getColumnDataList() != null) {
            // 假设 ColumnData 自身也需要深拷贝，或者它是不可变的
            // 如果 ColumnData 是可变的，你需要遍历并深拷贝每个 ColumnData 对象
            List<ColumnData> columnDataCopy = original.getColumnDataList().stream()
                    // .map(ColumnData::deepCopy) // 如果 ColumnData 有深拷贝方法
                    .collect(Collectors.toList()); // 这里是浅拷贝列表元素
            copy.setColumnDataList(columnDataCopy);
        }


        copy.setSelectInto(original.isSelectInto());
        copy.setInsertIntoSelect(original.isInsertIntoSelect());
        copy.setCreateTableAsSelect(original.isCreateTableAsSelect());
        copy.setUpdateSelect(original.isUpdateSelect());
        copy.setNeedChangeMessage(original.isNeedChangeMessage());
        copy.setMerge(original.isMerge());
        copy.setInsertAllFirst(original.isInsertAllFirst());
        copy.setDefaultSchema(original.isDefaultSchema());
        copy.setSelectFunction(original.isSelectFunction());
        copy.setChangeSchema(original.isChangeSchema());
        copy.setSelectSequence(original.isSelectSequence());
        copy.setDuplicateKeyUpdate(original.isDuplicateKeyUpdate());
        copy.setHasWhereClause(original.isHasWhereClause());
        copy.setContainsDBLink(original.isContainsDBLink());
        copy.setNeedCreateSchema(original.isNeedCreateSchema());
        copy.setSelectValue(original.isSelectValue());
        copy.setContainsBackupTable(original.isContainsBackupTable());
        copy.setDescDatabase(original.isDescDatabase());
        copy.setTemplate(original.isTemplate());
        copy.setShowReason(original.isShowReason());
        copy.setHasCaseWhen(original.isHasCaseWhen());
        copy.setHasDistinct(original.isHasDistinct());
        copy.setHasGroupBy(original.isHasGroupBy());
        copy.setHasJoin(original.isHasJoin());
        copy.setHasUnion(original.isHasUnion());
        copy.setHasWithClause(original.isHasWithClause());
        copy.setHasSubQuery(original.isHasSubQuery());
        copy.setInsertIgnore(original.isInsertIgnore());
        copy.setReplace(original.isReplace());
        copy.setSelectForUpdate(original.isSelectForUpdate());
        copy.setAnonymousBlock(original.isAnonymousBlock());

        copy.setTransactionStatus(original.getTransactionStatus());
        copy.setForceBackup(original.isForceBackup()); // isForceBackup() 是 Boolean, 可以为 null

        if (original.getOracleJobOperates() != null) {
            copy.setOracleJobOperates(new ArrayList<>(original.getOracleJobOperates()));
        }

        return copy;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    public Set<String> getTables() {
        return tables;
    }

    public void setTables(Set<String> tables) {
        this.tables = tables;
    }

    public Set<String> getColumns() {
        return columns;
    }

    public void setColumns(Set<String> columns) {
        this.columns = columns;
    }

    public Set<String> getFunctions() {
        return functions;
    }

    public void setFunctions(Set<String> functions) {
        this.functions = functions;
    }

    public Map<String, Set<String>> getStatisticsFuncMap() {
        return statisticsFuncMap;
    }

    public void setStatisticsFuncMap(Map<String, Set<String>> statisticsFuncMap) {
        this.statisticsFuncMap = statisticsFuncMap;
    }

    public Map<String, Set<String>> getStaFuncMap() {
        return staFuncMap;
    }

    public Map<String, String> getAlias() {
        return alias;
    }

    public void setAlias(Map<String, String> alias) {
        this.alias = alias;
    }

    public boolean isSelectInto() {
        return isSelectInto;
    }

    public void setSelectInto(boolean selectInto) {
        isSelectInto = selectInto;
    }

    public String getExplainOperation() {
        return explainOperation;
    }

    public void setExplainOperation(String explainOperation) {
        this.explainOperation = explainOperation;
    }

    public boolean isInsertIntoSelect() {
        return isInsertIntoSelect;
    }

    public void setInsertIntoSelect(boolean insertIntoSelect) {
        isInsertIntoSelect = insertIntoSelect;
    }

    public boolean isCreateTableAsSelect() {
        return isCreateTableAsSelect;
    }

    public void setCreateTableAsSelect(boolean isCreateTableAsSelect){
        this.isCreateTableAsSelect = isCreateTableAsSelect;
    }

    public boolean isUpdateSelect(){
        return isUpdateSelect;
    }

    public void setUpdateSelect(boolean isUpdateSelect){
        this.isUpdateSelect = isUpdateSelect;
    }

    public boolean isNeedChangeMessage(){
        return this.needChangeMessage;
    }

    public void setNeedChangeMessage(boolean needChangeMessage){
        this.needChangeMessage = needChangeMessage;
    }

    public boolean isMerge() {
        return isMerge;
    }

    public void setMerge(boolean merge) {
        isMerge = merge;
    }

    public boolean isInsertAllFirst() {
        return isInsertAllFirst;
    }

    public void setInsertAllFirst(boolean insertAllFirst) {
        isInsertAllFirst = insertAllFirst;
    }

    public Set<String> getInsertAllFirstTables() {
        return insertAllFirstTables;
    }

    public void setInsertAllFirstTables(Set<String> insertAllFirstTables) {
        this.insertAllFirstTables = insertAllFirstTables;
    }

    public List<String> getFuncArgs() {
        return funcArgs;
    }

    public void setFuncArgs(List<String> funcArgs) {
        this.funcArgs = funcArgs;
    }

    public boolean isDefaultSchema() {
        return isDefaultSchema;
    }

    public void setDefaultSchema(boolean defaultSchema) {
        isDefaultSchema = defaultSchema;
    }

    public boolean isSelectFunction() {
        return isSelectFunction;
    }

    public void setSelectFunction(boolean selectFunction) {
        isSelectFunction = selectFunction;
    }

    public Set<String> getTablesInWhereClause() {
        return tablesInWhereClause;
    }

    public void setTablesInWhereClause(Set<String> tablesInWhereClause) {
        this.tablesInWhereClause = tablesInWhereClause;
    }

    public boolean isChangeSchema() {
        return isChangeSchema;
    }

    public void setChangeSchema(boolean changeSchema) {
        isChangeSchema = changeSchema;
    }

    public Map<String, List<String>> getAliasBk() {
        return aliasBk;
    }

    public void setAliasBk(Map<String, List<String>> aliasBk) {
        this.aliasBk = aliasBk;
    }

    public String getCkOperation() {
        return ckOperation;
    }

    public void setCkOperation(String ckOperation) {
        this.ckOperation = ckOperation;
    }

    public String getCkWhere() {
        return ckWhere;
    }

    public void setCkWhere(String ckWhere) {
        this.ckWhere = ckWhere;
    }

    public String getCkInsertSelectSql() {
        return ckInsertSelectSql;
    }

    public void setCkInsertSelectSql(String ckInsertSelectSql) {
        this.ckInsertSelectSql = ckInsertSelectSql;
    }

    public boolean isSelectSequence() {
        return isSelectSequence;
    }

    public void setSelectSequence(boolean selectSequence) {
        isSelectSequence = selectSequence;
    }

    public boolean isDuplicateKeyUpdate() {
        return isDuplicateKeyUpdate;
    }

    public void setDuplicateKeyUpdate(boolean duplicateKeyUpdate) {
        isDuplicateKeyUpdate = duplicateKeyUpdate;
    }

    public boolean isHasWhereClause() {
        return hasWhereClause;
    }

    public void setHasWhereClause(boolean hasWhereClause) {
        this.hasWhereClause = hasWhereClause;
    }

    public Set<String> getNeedOtherOperations() {
        return needOtherOperations;
    }

    public void setNeedOtherOperations(Set<String> needOtherOperations) {
        this.needOtherOperations = needOtherOperations;
    }

    public boolean isContainsDBLink() {
        return containsDBLink;
    }

    public void setContainsDBLink(boolean containsDBLink) {
        this.containsDBLink = containsDBLink;
    }

    public boolean isNeedCreateSchema() {
        return needCreateSchema;
    }

    public void setNeedCreateSchema(boolean needCreateSchema) {
        this.needCreateSchema = needCreateSchema;
    }

    public boolean isSelectValue() {
        return isSelectValue;
    }

    public void setSelectValue(boolean selectValue) {
        isSelectValue = selectValue;
    }

    public boolean isContainsBackupTable() {
        return containsBackupTable;
    }

    public void setContainsBackupTable(boolean containsBackupTable) {
        this.containsBackupTable = containsBackupTable;
    }

    public int getTransactionStatus() {
        return transactionStatus;
    }

    public void setTransactionStatus(int transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public Boolean isForceBackup() {
        return forceBackup;
    }

    public void setForceBackup(Boolean forceBackup) {
        this.forceBackup = forceBackup;
    }

    public boolean isDescDatabase() {
        return isDescDatabase;
    }

    public void setDescDatabase(boolean descDatabase) {
        isDescDatabase = descDatabase;
    }

    public Set<String> getDblinkTables() {
        return dblinkTables;
    }

    public void setDblinkTables(Set<String> dblinkTables) {
        this.dblinkTables = dblinkTables;
    }

    public Set<String> getMultiInsertTables() {
        return multiInsertTables;
    }

    public void setMultiInsertTables(Set<String> multiInsertTables) {
        this.multiInsertTables = multiInsertTables;
    }

    public List<ColumnData> getColumnDataList() {
        return columnDataList;
    }

    public void setColumnDataList(List<ColumnData> columnDataList) {
        this.columnDataList = columnDataList;
    }

    public boolean isTemplate() {
        return isTemplate;
    }

    public void setTemplate(boolean template) {
        isTemplate = template;
    }

    public boolean isShowReason() {
        return showReason;
    }

    public void setShowReason(boolean showReason) {
        this.showReason = showReason;
    }

    public Set<String> getValuesSelectTables() {
        return valuesSelectTables;
    }

    public void setValuesSelectTables(Set<String> valuesSelectTables) {
        this.valuesSelectTables = valuesSelectTables;
    }

    public boolean isHasCaseWhen() {
        return hasCaseWhen;
    }

    public void setHasCaseWhen(boolean hasCaseWhen) {
        this.hasCaseWhen = hasCaseWhen;
    }

    public boolean isHasDistinct() {
        return hasDistinct;
    }

    public void setHasDistinct(boolean hasDistinct) {
        this.hasDistinct = hasDistinct;
    }

    public boolean isHasGroupBy() {
        return hasGroupBy;
    }

    public void setHasGroupBy(boolean hasGroupBy) {
        this.hasGroupBy = hasGroupBy;
    }

    public boolean isHasJoin() {
        return hasJoin;
    }

    public void setHasJoin(boolean hasJoin) {
        this.hasJoin = hasJoin;
    }

    public boolean isHasUnion() {
        return hasUnion;
    }

    public void setHasUnion(boolean hasUnion) {
        this.hasUnion = hasUnion;
    }

    public boolean isHasWithClause() {
        return hasWithClause;
    }

    public void setHasWithClause(boolean hasWithClause) {
        this.hasWithClause = hasWithClause;
    }

    public boolean isHasSubQuery() {
        return hasSubQuery;
    }

    public void setHasSubQuery(boolean hasSubQuery) {
        this.hasSubQuery = hasSubQuery;
    }

    public boolean isInsertIgnore() {
        return isInsertIgnore;
    }

    public void setInsertIgnore(boolean insertIgnore) {
        isInsertIgnore = insertIgnore;
    }

    public boolean isReplace() {
        return isReplace;
    }

    public void setReplace(boolean replace) {
        isReplace = replace;
    }

    public boolean isSelectForUpdate() {
        return isSelectForUpdate;
    }

    public void setSelectForUpdate(boolean selectForUpdate) {
        isSelectForUpdate = selectForUpdate;
    }

    public List<OracleJobOperationType> getOracleJobOperates() {
        return oracleJobOperates;
    }

    public void setOracleJobOperates(List<OracleJobOperationType> oracleJobOperates) {
        this.oracleJobOperates = oracleJobOperates;
    }

    public boolean isAnonymousBlock() {
        return isAnonymousBlock;
    }

    public void setAnonymousBlock(boolean anonymousBlock) {
        isAnonymousBlock = anonymousBlock;
    }

    public Set<String> getStaticsFunctions() {
        return staticsFunctions;
    }

    public void setStaticsFunctions(Set<String> staticsFunctions) {
        this.staticsFunctions = staticsFunctions;
    }
}

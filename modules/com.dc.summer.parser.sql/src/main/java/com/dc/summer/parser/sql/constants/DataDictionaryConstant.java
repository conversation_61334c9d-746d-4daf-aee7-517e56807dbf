package com.dc.summer.parser.sql.constants;

public class DataDictionaryConstant {
    public static final String[] MYSQL_DATA_DICTIONARY = {"databases", "tables", "columns", "grants", "engines", "warnings",
            "errors", "processlist"};
    public static final String[] KINGBASE_DATA_DICTIONARY = {"sys_statistic",
            "sys_type",
            "sys_foreign_table",
            "sys_authid",
            "sys_statistic_ext_data",
            "sys_user_mapping",
            "sys_subscription",
            "sys_attribute",
            "sys_proc",
            "sys_class",
            "sys_attrdef",
            "sys_constraint",
            "sys_inherits",
            "sys_index",
            "sys_operator",
            "sys_opfamily",
            "sys_opclass",
            "sys_am",
            "sys_amop",
            "sys_amproc",
            "sys_language",
            "sys_largeobject_metadata",
            "sys_aggregate",
            "sys_statistic_ext",
            "sys_rewrite",
            "sys_trigger",
            "sys_event_trigger",
            "sys_description",
            "sys_cast",
            "sys_enum",
            "sys_namespace",
            "sys_conversion",
            "sys_depend",
            "sys_database",
            "sys_db_role_setting",
            "sys_tablespace",
            "sys_auth_members",
            "sys_shdepend",
            "sys_shdescription",
            "sys_ts_config",
            "sys_ts_config_map",
            "sys_ts_dict",
            "sys_ts_parser",
            "sys_ts_template",
            "sys_extension",
            "sys_foreign_data_wrapper",
            "sys_foreign_server",
            "sys_policy",
            "sys_replication_origin",
            "sys_default_acl",
            "sys_init_privs",
            "sys_seclabel",
            "sys_shseclabel",
            "sys_collation",
            "sys_partitioned_table",
            "sys_range",
            "sys_transform",
            "sys_sequence",
            "sys_publication",
            "sys_publication_rel",
            "sys_subscription_rel",
            "sys_largeobject"
    };
    public static final String[] PG_SQL_DATA_DICTIONARY = {"pg_statistic",
            "pg_type",
            "pg_foreign_table",
            "pg_authid",
            "pg_statistic_ext_data",
            "pg_user_mapping",
            "pg_subscription",
            "pg_attribute",
            "pg_proc",
            "pg_class",
            "pg_attrdef",
            "pg_constraint",
            "pg_inherits",
            "pg_index",
            "pg_operator",
            "pg_opfamily",
            "pg_opclass",
            "pg_am",
            "pg_amop",
            "pg_amproc",
            "pg_language",
            "pg_largeobject_metadata",
            "pg_aggregate",
            "pg_statistic_ext",
            "pg_rewrite",
            "pg_trigger",
            "pg_event_trigger",
            "pg_description",
            "pg_cast",
            "pg_enum",
            "pg_namespace",
            "pg_conversion",
            "pg_depend",
            "pg_database",
            "pg_db_role_setting",
            "pg_tablespace",
            "pg_auth_members",
            "pg_shdepend",
            "pg_shdescription",
            "pg_ts_config",
            "pg_ts_config_map",
            "pg_ts_dict",
            "pg_ts_parser",
            "pg_ts_template",
            "pg_extension",
            "pg_foreign_data_wrapper",
            "pg_foreign_server",
            "pg_policy",
            "pg_replication_origin",
            "pg_default_acl",
            "pg_init_privs",
            "pg_seclabel",
            "pg_shseclabel",
            "pg_collation",
            "pg_partitioned_table",
            "pg_range",
            "pg_transform",
            "pg_sequence",
            "pg_publication",
            "pg_publication_rel",
            "pg_subscription_rel",
            "pg_largeobject"
    };
    public static final String[] DM_DATA_DICTIONARY = {"HISTOGRAMS_TABLE",
            "##TMP_TBL_FOR_DBMS_LOB_BLOB",
            "##TMP_TBL_FOR_DBMS_LOB_CLOB",
            "<ADT_1>",
            "ALL_ALL_TABLES",
            "ALL_ARGUMENTS",
            "ALL_COL_COMMENTS",
            "ALL_COL_PRIVS",
            "ALL_CONSTRAINTS",
            "ALL_CONS_COLUMNS",
            "ALL_DB_LINKS",
            "ALL_DEPENDENCIES",
            "ALL_DIRECTORIES",
            "ALL_ENCRYPTED_COLUMNS",
            "ALL_INDEXES",
            "ALL_IND_COLUMNS",
            "ALL_JSON_COLUMNS",
            "ALL_OBJECTS",
            "ALL_PART_KEY_COLUMNS",
            "ALL_PART_TABLES",
            "ALL_POLICIES",
            "ALL_POLICY_CONTEXTS",
            "ALL_POLICY_GROUPS",
            "ALL_PROCEDURES",
            "ALL_SEC_RELEVANT_COLS",
            "ALL_SEQUENCES",
            "ALL_SOURCE",
            "ALL_SOURCE_AE",
            "ALL_SUBPART_KEY_COLUMNS",
            "ALL_SYNONYMS",
            "ALL_TABLES",
            "ALL_TABLES_DIS_INFO",
            "ALL_TAB_COLS",
            "ALL_TAB_COLUMNS",
            "ALL_TAB_COMMENTS",
            "ALL_TAB_PARTITIONS",
            "ALL_TAB_PRIVS",
            "ALL_TAB_STATISTICS",
            "ALL_TAB_SUBPARTITIONS",
            "ALL_TRIGGERS",
            "ALL_TRIGGER_COLS",
            "ALL_USERS",
            "ALL_VIEWS",
            "BASE_TRIGGER_COLS",
            "CY_SYNONYM",
            "DBA_ARGUMENTS",
            "DBA_COL_COMMENTS",
            "DBA_COL_PRIVS",
            "DBA_CONSTRAINTS",
            "DBA_CONS_COLUMNS",
            "DBA_DATA_FILES",
            "DBA_DB_LINKS",
            "DBA_DEPENDENCIES",
            "DBA_DIRECTORIES",
            "DBA_ENCRYPTED_COLUMNS",
            "DBA_FREE_SPACE",
            "DBA_INDEXES",
            "DBA_IND_COLUMNS",
            "DBA_JSON_COLUMNS",
            "DBA_OBJECTS",
            "DBA_PART_KEY_COLUMNS",
            "DBA_PART_TABLES",
            "DBA_POLICIES",
            "DBA_POLICY_CONTEXTS",
            "DBA_POLICY_GROUPS",
            "DBA_PROCEDURES",
            "DBA_PROXIES",
            "DBA_ROLES",
            "DBA_ROLE_PRIVS",
            "DBA_SEC_RELEVANT_COLS",
            "DBA_SEGMENTS",
            "DBA_SEQUENCES",
            "DBA_SOURCE",
            "DBA_SOURCE_AE",
            "DBA_SYNONYMS",
            "DBA_SYS_PRIVS",
            "DBA_TABLES",
            "DBA_TABLESPACES",
            "DBA_TAB_COLS",
            "DBA_TAB_COLUMNS",
            "DBA_TAB_COMMENTS",
            "DBA_TAB_PARTITIONS",
            "DBA_TAB_PRIVS",
            "DBA_TAB_STATISTICS",
            "DBA_TAB_SUBPARTITIONS",
            "DBA_TRIGGERS",
            "DBA_TRIGGER_COLS",
            "DBA_USERS",
            "DBA_VIEWS",
            "DBMS_ADVANCED_REWRITE",
            "DBMS_ALERT",
            "DBMS_ALERT_INFO",
            "DBMS_APPLICATION_INFO",
            "DBMS_AQ",
            "DBMS_AQADM",
            "DBMS_AUTO_TASK_ADMIN",
            "DBMS_AW",
            "DBMS_BINARY",
            "DBMS_CRYPTO",
            "DBMS_ERRLOG",
            "DBMS_LOB",
            "DBMS_LOCK",
            "DBMS_LOGMNR",
            "DBMS_METADATA",
            "DBMS_MVIEW",
            "DBMS_OBFUSCATION_TOOLKIT",
            "DBMS_OUTPUT",
            "DBMS_PAGE",
            "DBMS_PIPE",
            "DBMS_RANDOM",
            "DBMS_RLS",
            "DBMS_SESSION",
            "DBMS_SPACE",
            "DBMS_SQL",
            "DBMS_SQLTUNE",
            "DBMS_STATS",
            "DBMS_TRANSACTION",
            "DBMS_UTILITY",
            "DBMS_XMLGEN",
            "DM_NAME_LIST_T",
            "DM_SQL_TXT",
            "DUAL",
            "ET",
            "ODCICONST",
            "ORA_DICT_OBJ_NAME",
            "ORA_DICT_OBJ_OWNER",
            "ORA_DICT_OBJ_TYPE",
            "ORA_NAME_LIST_T",
            "ORA_SQL_TXT",
            "PLAN_TABLE",
            "POLICIES",
            "POLICY_COLS",
            "POLICY_CONTEXTS",
            "POLICY_GROUPS",
            "PROXY_INFO$",
            "PROXY_USERS",
            "SESSION_PRIVS",
            "SESSION_ROLES",
            "SP_ARCH_BAKSET_REMOVE_BATCH",
            "SP_DB_BAKSET_REMOVE_BATCH",
            "SP_GET_EP_COUNT",
            "SP_GET_LAST_NOT_EMPTY_SUBTABLE_NUM",
            "SP_LOB_INFO",
            "SP_STAT_ON",
            "SP_STAT_ON_TABLE_COLS",
            "SP_TABLEDEF",
            "SP_TAB_BAKSET_REMOVE_BATCH",
            "SP_TS_BAKSET_REMOVE_BATCH",
            "SP_VIEWDEF",
            "SQL_TXT",
            "SYSACCHISTORIES",
            "SYSAUDIT",
            "SYSAUDITRULES",
            "SYSAUDITSQLSEQ",
            "SYSAUTH$",
            "SYSCLASSES",
            "SYSCOLCYT",
            "SYSCOLINFOS",
            "SYSCOLUMNCOMMENTS",
            "SYSCOLUMNS",
            "SYSCONS",
            "SYSCONTEXTINDEXES",
            "SYSCONTEXTLIBS",
            "SYSDEPENDENCIES",
            "SYSDISTABLEINFO",
            "SYSDUAL",
            "SYSFCOLDEFAULT",
            "SYSFREQROOTS",
            "SYSGRANTS",
            "SYSHPARTTABLEINFO",
            "SYSINDEXES",
            "SYSINJECTHINT",
            "SYSMACCOMPS",
            "SYSMACGRPS",
            "SYSMACLABELS",
            "SYSMACLVLS",
            "SYSMACOBJ",
            "SYSMACPLYS",
            "SYSMACTABPLY",
            "SYSMACUSRPLY",
            "SYSMSTATS",
            "SYSOBJECTS",
            "SYSOBJINFOS",
            "SYSOPENHISTORY",
            "SYSPROFILES",
            "SYSPWDCHGS",
            "SYSRESOURCES",
            "SYSSTATPREFS",
            "SYSSTATS",
            "SYSSTATTABLEIDU",
            "SYSTABLECOMMENTS",
            "SYSTEXTS",
            "SYSTUNINGEXECUTION",
            "SYSTUNINGREPORT",
            "SYSTUNINGTASK",
            "SYSTYPEINFOS",
            "SYSUSER$",
            "SYSUSERINI",
            "SYSUSERINI$",
            "SYSUSERPROFILES",
            "SYSUSERS",
            "SYS_REWRITE_EQUIVALENCES",
            "TRO",
            "TROF",
            "USER_ALL_TABLES",
            "USER_ARGUMENTS",
            "USER_COL_COMMENTS",
            "USER_COL_PRIVS",
            "USER_CONSTRAINTS",
            "USER_CONS_COLUMNS",
            "USER_DB_LINKS",
            "USER_DEPENDENCIES",
            "USER_ENCRYPTED_COLUMNS",
            "USER_FREE_SPACE",
            "USER_INDEXES",
            "USER_IND_COLUMNS",
            "USER_IND_PARTITIONS",
            "USER_IND_SUBPARTITIONS",
            "USER_JSON_COLUMNS",
            "USER_MVIEWS",
            "USER_OBJECTS",
            "USER_PART_KEY_COLUMNS",
            "USER_PART_TABLES",
            "USER_POLICIES",
            "USER_POLICY_CONTEXTS",
            "USER_POLICY_GROUPS",
            "USER_PROCEDURES",
            "USER_PROXIES",
            "USER_ROLE_PRIVS",
            "USER_SEC_RELEVANT_COLS",
            "USER_SEGMENTS",
            "USER_SEQUENCES",
            "USER_SOURCE",
            "USER_SOURCE_AE",
            "USER_SUBPART_KEY_COLUMNS",
            "USER_SYNONYMS",
            "USER_SYS_PRIVS",
            "USER_TABLES",
            "USER_TABLESPACES",
            "USER_TAB_COLS",
            "USER_TAB_COLUMNS",
            "USER_TAB_COMMENTS",
            "USER_TAB_PARTITIONS",
            "USER_TAB_PRIVS",
            "USER_TAB_STATISTICS",
            "USER_TAB_SUBPARTITIONS",
            "USER_TRIGGERS",
            "USER_TRIGGER_COLS",
            "USER_TYPES",
            "USER_USERS",
            "USER_VIEWS",
            "UTL_COMPRESS",
            "UTL_ENCODE",
            "UTL_FILE",
            "UTL_HTTP",
            "UTL_I18N",
            "UTL_INADDR",
            "UTL_MAIL",
            "UTL_MATCH",
            "UTL_RAW",
            "UTL_SMTP",
            "UTL_TCP",
            "UTL_URL",
            "V$RESOURCE_LIMIT",
            "XCOL",
            "XCSR"};
    public static final String[] ORACLE_DATA_DICTIONARY = {"DBA_CONS_COLUMNS",
            "DBA_LOG_GROUP_COLUMNS",
            "DBA_LOBS",
            "DBA_CATALOG",
            "DBA_CLUSTERS",
            "DBA_CLU_COLUMNS",
            "DBA_COL_COMMENTS",
            "DBA_COL_PRIVS",
            "DBA_ENCRYPTED_COLUMNS",
            "DBA_INDEXES",
            "DBA_IND_COLUMNS",
            "DBA_IND_EXPRESSIONS",
            "DBA_JOIN_IND_COLUMNS",
            "DBA_OBJECTS",
            "DBA_OBJECTS_AE",
            "DBA_ROLLBACK_SEGS",
            "DBA_ROLE_PRIVS",
            "DBA_SEQUENCES",
            "DBA_SYNONYMS",
            "DBA_TABLES",
            "DBA_OBJECT_TABLES",
            "DBA_ALL_TABLES",
            "DBA_TAB_COLS",
            "DBA_TAB_COLUMNS",
            "DBA_TAB_COMMENTS",
            "DBA_TAB_PRIVS",
            "DBA_VIEWS",
            "DBA_VIEWS_AE",
            "DBA_CONSTRAINTS",
            "DBA_LOG_GROUPS",
            "DBA_CLUSTER_HASH_EXPRESSIONS",
            "DBA_UPDATABLE_COLUMNS",
            "DBA_UNUSED_COL_TABS",
            "DBA_PARTIAL_DROP_TABS",
            "DBA_RESUMABLE",
            "DBA_EDITIONING_VIEWS",
            "DBA_EDITIONING_VIEWS_AE",
            "DBA_EDITIONING_VIEW_COLS",
            "DBA_EDITIONING_VIEW_COLS_AE",
            "DBA_EDITIONS",
            "DBA_EDITION_COMMENTS",
            "DBA_LIBRARIES",
            "DBA_PROCEDURES",
            "DBA_STORED_SETTINGS",
            "DBA_PLSQL_OBJECT_SETTINGS",
            "DBA_ARGUMENTS",
            "DBA_ASSEMBLIES",
            "DBA_IDENTIFIERS",
            "DBA_DB_LINKS",
            "DBA_RECYCLEBIN",
            "DBA_2PC_PENDING",
            "DBA_2PC_NEIGHBORS",
            "DBA_PROFILES",
            "DBA_USERS",
            "DBA_OBJ_AUDIT_OPTS",
            "DBA_STMT_AUDIT_OPTS",
            "DBA_PRIV_AUDIT_OPTS",
            "DBA_AUDIT_TRAIL",
            "DBA_AUDIT_SESSION",
            "DBA_AUDIT_STATEMENT",
            "DBA_AUDIT_OBJECT",
            "DBA_AUDIT_EXISTS",
            "DBA_ROLES",
            "DBA_SYS_PRIVS",
            "DBA_PROXIES",
            "DBA_CONNECT_ROLE_GRANTEES",
            "DBA_TYPES",
            "DBA_COLL_TYPES",
            "DBA_TYPE_ATTRS",
            "DBA_TYPE_METHODS",
            "DBA_METHOD_PARAMS",
            "DBA_METHOD_RESULTS",
            "DBA_SQLJ_TYPES",
            "DBA_TYPE_VERSIONS",
            "DBA_PENDING_CONV_TABLES",
            "DBA_SQLJ_TYPE_ATTRS",
            "DBA_SQLJ_TYPE_METHODS",
            "DBA_OLDIMAGE_COLUMNS",
            "DBA_NESTED_TABLE_COLS",
            "DBA_DIRECTORIES",
            "DBA_REFS",
            "DBA_NESTED_TABLES",
            "DBA_VARRAYS",
            "DBA_OBJ_COLATTRS",
            "DBA_CONS_OBJ_COLUMNS",
            "DBA_OPERATORS",
            "DBA_OPBINDINGS",
            "DBA_OPANCILLARY",
            "DBA_OPARGUMENTS",
            "DBA_OPERATOR_COMMENTS",
            "DBA_INDEXTYPES",
            "DBA_INDEXTYPE_COMMENTS",
            "DBA_INDEXTYPE_ARRAYTYPES",
            "DBA_INDEXTYPE_OPERATORS",
            "DBA_SECONDARY_OBJECTS",
            "DBA_SOURCE_TABLES",
            "DBA_PUBLISHED_COLUMNS",
            "DBA_SUBSCRIPTIONS",
            "DBA_SUBSCRIBED_TABLES",
            "DBA_SUBSCRIBED_COLUMNS",
            "DBA_EXP_OBJECTS",
            "DBA_EXP_VERSION",
            "DBA_EXP_FILES",
            "DBA_EXTERNAL_TABLES",
            "DBA_EXTERNAL_LOCATIONS",
            "DBA_MINING_MODELS",
            "DBA_MINING_MODEL_ATTRIBUTES",
            "DBA_MINING_MODEL_SETTINGS",
            "DBA_MINING_MODEL_TABLES",
            "DBA_TAB_COL_STATISTICS",
            "DBA_TAB_HISTOGRAMS",
            "DBA_ASSOCIATIONS",
            "DBA_USTATS",
            "DBA_TAB_MODIFICATIONS",
            "DBA_OPTSTAT_OPERATIONS",
            "DBA_TAB_STATS_HISTORY",
            "DBA_TAB_STAT_PREFS",
            "DBA_TAB_PENDING_STATS",
            "DBA_IND_PENDING_STATS",
            "DBA_COL_PENDING_STATS",
            "DBA_TAB_HISTGRM_PENDING_STATS",
            "DBA_DIMENSIONS",
            "DBA_DIM_LEVELS",
            "DBA_DIM_LEVEL_KEY",
            "DBA_DIM_ATTRIBUTES",
            "DBA_DIM_HIERARCHIES",
            "DBA_DIM_CHILD_OF",
            "DBA_DIM_JOIN_KEY",
            "DBA_SUMMARIES",
            "DBA_MVIEW_ANALYSIS",
            "DBA_MVIEW_AGGREGATES",
            "DBA_MVIEW_DETAIL_RELATIONS",
            "DBA_MVIEW_KEYS",
            "DBA_MVIEW_JOINS",
            "DBA_MVIEW_COMMENTS",
            "DBA_REWRITE_EQUIVALENCES",
            "DBA_MVIEW_DETAIL_PARTITION",
            "DBA_MVIEW_DETAIL_SUBPARTITION",
            "DBA_TSTZ_TAB_COLS",
            "DBA_TSTZ_TABLES",
            "DBA_ERRORS",
            "DBA_ERRORS_AE",
            "DBA_SOURCE_AE",
            "DBA_SOURCE",
            "DBA_TRIGGERS",
            "DBA_INTERNAL_TRIGGERS",
            "DBA_TRIGGER_COLS",
            "DBA_DEPENDENCIES",
            "DBA_OBJECT_SIZE",
            "DBA_TRIGGER_ORDERING",
            "DBA_JOBS_RUNNING",
            "DBA_JOBS",
            "DBA_SEGMENTS",
            "DBA_SEGMENTS_OLD",
            "DBA_EXTENTS",
            "DBA_UNDO_EXTENTS",
            "DBA_LMT_USED_EXTENTS",
            "DBA_DMT_USED_EXTENTS",
            "DBA_FREE_SPACE",
            "DBA_LMT_FREE_SPACE",
            "DBA_DMT_FREE_SPACE",
            "DBA_FREE_SPACE_COALESCED",
            "DBA_DATA_FILES",
            "DBA_TABLESPACES",
            "DBA_TEMP_FILES",
            "DBA_TABLESPACE_GROUPS",
            "DBA_TABLESPACE_USAGE_METRICS",
            "DBA_TS_QUOTAS",
            "DBA_TEMP_FREE_SPACE",
            "DBA_CONTEXT",
            "DBA_GLOBAL_CONTEXT",
            "DBA_RULE_SETS",
            "DBA_RULESETS",
            "DBA_RULES",
            "DBA_RULE_SET_RULES",
            "DBA_EVALUATION_CONTEXTS",
            "DBA_EVALUATION_CONTEXT_TABLES",
            "DBA_EVALUATION_CONTEXT_VARS",
            "DBA_SNAPSHOTS",
            "DBA_SNAPSHOT_LOGS",
            "DBA_RCHILD",
            "DBA_RGROUP",
            "DBA_REFRESH",
            "DBA_REFRESH_CHILDREN",
            "DBA_REGISTERED_SNAPSHOTS",
            "DBA_MVIEWS",
            "DBA_MVIEW_REFRESH_TIMES",
            "DBA_MVIEW_LOGS",
            "DBA_BASE_TABLE_MVIEWS",
            "DBA_REGISTERED_MVIEWS",
            "DBA_MVIEW_LOG_FILTER_COLS",
            "DBA_POLICIES",
            "DBA_POLICY_GROUPS",
            "DBA_POLICY_CONTEXTS",
            "DBA_SEC_RELEVANT_COLS",
            "DBA_AUDIT_POLICIES",
            "DBA_AUDIT_POLICY_COLUMNS",
            "DBA_FGA_AUDIT_TRAIL",
            "DBA_COMMON_AUDIT_TRAIL",
            "DBA_AUDIT_MGMT_CONFIG_PARAMS",
            "DBA_AUDIT_MGMT_LAST_ARCH_TS",
            "DBA_AUDIT_MGMT_CLEANUP_JOBS",
            "DBA_AUDIT_MGMT_CLEAN_EVENTS",
            "DBA_TSM_SOURCE",
            "DBA_TSM_DESTINATION",
            "DBA_TSM_HISTORY",
            "DBA_CHANGE_NOTIFICATION_REGS",
            "DBA_CQ_NOTIFICATION_QUERIES",
            "DBA_CPOOL_INFO",
            "DBA_SSCR_CAPTURE",
            "DBA_SSCR_RESTORE",
            "DBA_SUBSCR_REGISTRATIONS",
            "DBA_QUEUE_TABLES",
            "DBA_QUEUES",
            "DBA_RESOURCE_INCARNATIONS",
            "DBA_RSRC_PLANS",
            "DBA_RSRC_CONSUMER_GROUPS",
            "DBA_RSRC_CATEGORIES",
            "DBA_RSRC_PLAN_DIRECTIVES",
            "DBA_RSRC_CONSUMER_GROUP_PRIVS",
            "DBA_RSRC_MANAGER_SYSTEM_PRIVS",
            "DBA_RSRC_GROUP_MAPPINGS",
            "DBA_RSRC_MAPPING_PRIORITY",
            "DBA_RSRC_STORAGE_POOL_MAPPING",
            "DBA_RSRC_CAPABILITY",
            "DBA_RSRC_INSTANCE_CAPABILITY",
            "DBA_RSRC_IO_CALIBRATE",
            "DBA_OUTLINES",
            "DBA_OUTLINE_HINTS",
            "DBA_DATAPUMP_JOBS",
            "DBA_DATAPUMP_SESSIONS",
            "DBA_FEATURE_USAGE_STATISTICS",
            "DBA_HIGH_WATER_MARK_STATISTICS",
            "DBA_CPU_USAGE_STATISTICS",
            "DBA_ALERT_ARGUMENTS",
            "DBA_ENABLED_TRACES",
            "DBA_ENABLED_AGGREGATIONS",
            "DBA_HIST_DATABASE_INSTANCE",
            "DBA_HIST_SNAPSHOT",
            "DBA_HIST_SNAP_ERROR",
            "DBA_HIST_COLORED_SQL",
            "DBA_HIST_BASELINE_METADATA",
            "DBA_HIST_BASELINE_TEMPLATE",
            "DBA_HIST_WR_CONTROL",
            "DBA_HIST_DATAFILE",
            "DBA_HIST_FILESTATXS",
            "DBA_HIST_TEMPFILE",
            "DBA_HIST_TEMPSTATXS",
            "DBA_HIST_COMP_IOSTAT",
            "DBA_HIST_SQLSTAT",
            "DBA_HIST_SQLTEXT",
            "DBA_HIST_SQL_SUMMARY",
            "DBA_HIST_SQL_PLAN",
            "DBA_HIST_SQL_BIND_METADATA",
            "DBA_HIST_OPTIMIZER_ENV",
            "DBA_HIST_EVENT_NAME",
            "DBA_HIST_SYSTEM_EVENT",
            "DBA_HIST_BG_EVENT_SUMMARY",
            "DBA_HIST_WAITSTAT",
            "DBA_HIST_ENQUEUE_STAT",
            "DBA_HIST_LATCH_NAME",
            "DBA_HIST_LATCH",
            "DBA_HIST_LATCH_CHILDREN",
            "DBA_HIST_LATCH_PARENT",
            "DBA_HIST_LATCH_MISSES_SUMMARY",
            "DBA_HIST_EVENT_HISTOGRAM",
            "DBA_HIST_MUTEX_SLEEP",
            "DBA_HIST_LIBRARYCACHE",
            "DBA_HIST_DB_CACHE_ADVICE",
            "DBA_HIST_BUFFER_POOL_STAT",
            "DBA_HIST_ROWCACHE_SUMMARY",
            "DBA_HIST_SGA",
            "DBA_HIST_SGASTAT",
            "DBA_HIST_PGASTAT",
            "DBA_HIST_PROCESS_MEM_SUMMARY",
            "DBA_HIST_RESOURCE_LIMIT",
            "DBA_HIST_SHARED_POOL_ADVICE",
            "DBA_HIST_STREAMS_POOL_ADVICE",
            "DBA_HIST_SQL_WORKAREA_HSTGRM",
            "DBA_HIST_PGA_TARGET_ADVICE",
            "DBA_HIST_SGA_TARGET_ADVICE",
            "DBA_HIST_MEMORY_TARGET_ADVICE",
            "DBA_HIST_MEMORY_RESIZE_OPS",
            "DBA_HIST_INSTANCE_RECOVERY",
            "DBA_HIST_JAVA_POOL_ADVICE",
            "DBA_HIST_THREAD",
            "DBA_HIST_STAT_NAME",
            "DBA_HIST_SYSSTAT",
            "DBA_HIST_SYS_TIME_MODEL",
            "DBA_HIST_OSSTAT_NAME",
            "DBA_HIST_OSSTAT",
            "DBA_HIST_PARAMETER_NAME",
            "DBA_HIST_PARAMETER",
            "DBA_HIST_UNDOSTAT",
            "DBA_HIST_SEG_STAT",
            "DBA_HIST_SEG_STAT_OBJ",
            "DBA_HIST_METRIC_NAME",
            "DBA_HIST_SYSMETRIC_HISTORY",
            "DBA_HIST_SYSMETRIC_SUMMARY",
            "DBA_HIST_SESSMETRIC_HISTORY",
            "DBA_HIST_FILEMETRIC_HISTORY",
            "DBA_HIST_WAITCLASSMET_HISTORY",
            "DBA_HIST_DLM_MISC",
            "DBA_HIST_CR_BLOCK_SERVER",
            "DBA_HIST_CURRENT_BLOCK_SERVER",
            "DBA_HIST_INST_CACHE_TRANSFER",
            "DBA_HIST_PLAN_OPERATION_NAME",
            "DBA_HIST_PLAN_OPTION_NAME",
            "DBA_HIST_SQLCOMMAND_NAME",
            "DBA_HIST_TOPLEVELCALL_NAME",
            "DBA_HIST_ACTIVE_SESS_HISTORY",
            "DBA_HIST_TABLESPACE_STAT",
            "DBA_HIST_LOG",
            "DBA_HIST_MTTR_TARGET_ADVICE",
            "DBA_HIST_TBSPC_SPACE_USAGE",
            "DBA_HIST_SERVICE_NAME",
            "DBA_HIST_SERVICE_STAT",
            "DBA_HIST_SERVICE_WAIT_CLASS",
            "DBA_HIST_SESS_TIME_STATS",
            "DBA_HIST_STREAMS_CAPTURE",
            "DBA_HIST_STREAMS_APPLY_SUM",
            "DBA_HIST_BUFFERED_QUEUES",
            "DBA_HIST_BUFFERED_SUBSCRIBERS",
            "DBA_HIST_RULE_SET",
            "DBA_HIST_PERSISTENT_QUEUES",
            "DBA_HIST_PERSISTENT_SUBS",
            "DBA_HIST_IOSTAT_FUNCTION",
            "DBA_HIST_IOSTAT_FUNCTION_NAME",
            "DBA_HIST_IOSTAT_FILETYPE",
            "DBA_HIST_IOSTAT_FILETYPE_NAME",
            "DBA_HIST_IOSTAT_DETAIL",
            "DBA_HIST_RSRC_CONSUMER_GROUP",
            "DBA_HIST_RSRC_PLAN",
            "DBA_HIST_CLUSTER_INTERCON",
            "DBA_HIST_MEM_DYNAMIC_COMP",
            "DBA_HIST_IC_CLIENT_STATS",
            "DBA_HIST_IC_DEVICE_STATS",
            "DBA_HIST_INTERCONNECT_PINGS",
            "DBA_HIST_DISPATCHER",
            "DBA_HIST_SHARED_SERVER_SUMMARY",
            "DBA_HIST_DYN_REMASTER_STATS",
            "DBA_SQL_PROFILES",
            "DBA_SQL_PLAN_BASELINES",
            "DBA_SQL_PATCHES",
            "DBA_REGISTERED_SNAPSHOT_GROUPS",
            "DBA_REGISTERED_MVIEW_GROUPS",
            "DBA_REPGROUP_PRIVILEGES",
            "DBA_REPGROUP",
            "DBA_REPSITES",
            "DBA_REPSCHEMA",
            "DBA_REPOBJECT",
            "DBA_REPCOLUMN",
            "DBA_REPPROP",
            "DBA_REPKEY_COLUMNS",
            "DBA_REPGENOBJECTS",
            "DBA_REPGENERATED",
            "DBA_REPCATLOG",
            "DBA_REPDDL",
            "DBA_REPPRIORITY_GROUP",
            "DBA_REPPRIORITY",
            "DBA_REPCOLUMN_GROUP",
            "DBA_REPGROUPED_COLUMN",
            "DBA_REPCONFLICT",
            "DBA_REPRESOLUTION_METHOD",
            "DBA_REPRESOLUTION",
            "DBA_REPRESOLUTION_STATISTICS",
            "DBA_REPRESOL_STATS_CONTROL",
            "DBA_REPPARAMETER_COLUMN",
            "DBA_REPAUDIT_ATTRIBUTE",
            "DBA_REPAUDIT_COLUMN",
            "DBA_REPFLAVORS",
            "DBA_REPFLAVOR_OBJECTS",
            "DBA_REPFLAVOR_COLUMNS",
            "DBA_TEMPLATE_REFGROUPS",
            "DBA_TEMPLATE_TARGETS",
            "DBA_REPCAT_EXCEPTIONS",
            "DBA_REPEXTENSIONS",
            "DBA_REPSITES_NEW",
            "DBA_USERS_WITH_DEFPWD",
            "DBA_WARNING_SETTINGS",
            "DBA_STAT_EXTENSIONS",
            "DBA_TAB_STATISTICS",
            "DBA_IND_STATISTICS",
            "DBA_PENDING_TRANSACTIONS",
            "DBA_AWS",
            "DBA_AW_PS",
            "DBA_CUBES",
            "DBA_CUBE_DIMENSIONALITY",
            "DBA_CUBE_MEASURES",
            "DBA_CUBE_DIMENSIONS",
            "DBA_CUBE_HIERARCHIES",
            "DBA_CUBE_HIER_LEVELS",
            "DBA_CUBE_DIM_LEVELS",
            "DBA_CUBE_ATTRIBUTES",
            "DBA_CUBE_ATTR_VISIBILITY",
            "DBA_CUBE_DIM_MODELS",
            "DBA_CUBE_CALCULATED_MEMBERS",
            "DBA_CUBE_VIEWS",
            "DBA_CUBE_VIEW_COLUMNS",
            "DBA_CUBE_DIM_VIEWS",
            "DBA_CUBE_DIM_VIEW_COLUMNS",
            "DBA_CUBE_HIER_VIEWS",
            "DBA_CUBE_HIER_VIEW_COLUMNS",
            "DBA_MEASURE_FOLDERS",
            "DBA_MEASURE_FOLDER_CONTENTS",
            "DBA_CUBE_BUILD_PROCESSES",
            "DBA_QUEUE_SUBSCRIBERS",
            "DBA_SCHEDULER_PROGRAMS",
            "DBA_SCHEDULER_DESTS",
            "DBA_SCHEDULER_EXTERNAL_DESTS",
            "DBA_SCHEDULER_DB_DESTS",
            "DBA_SCHEDULER_JOB_DESTS",
            "DBA_SCHEDULER_JOBS",
            "DBA_SCHEDULER_JOB_ROLES",
            "DBA_SCHEDULER_JOB_CLASSES",
            "DBA_SCHEDULER_WINDOWS",
            "DBA_SCHEDULER_PROGRAM_ARGS",
            "DBA_SCHEDULER_JOB_ARGS",
            "DBA_SCHEDULER_JOB_LOG",
            "DBA_SCHEDULER_JOB_RUN_DETAILS",
            "DBA_SCHEDULER_WINDOW_LOG",
            "DBA_SCHEDULER_WINDOW_DETAILS",
            "DBA_SCHEDULER_WINDOW_GROUPS",
            "DBA_SCHEDULER_WINGROUP_MEMBERS",
            "DBA_SCHEDULER_GROUP_MEMBERS",
            "DBA_SCHEDULER_GROUPS",
            "DBA_SCHEDULER_SCHEDULES",
            "DBA_SCHEDULER_REMOTE_DATABASES",
            "DBA_SCHEDULER_REMOTE_JOBSTATE",
            "DBA_SCHEDULER_GLOBAL_ATTRIBUTE",
            "DBA_SCHEDULER_CHAINS",
            "DBA_SCHEDULER_CHAIN_RULES",
            "DBA_SCHEDULER_CHAIN_STEPS",
            "DBA_SCHEDULER_RUNNING_CHAINS",
            "DBA_SCHEDULER_CREDENTIALS",
            "DBA_SCHEDULER_FILE_WATCHERS",
            "DBA_SCHEDULER_NOTIFICATIONS",
            "DBA_EXPORT_OBJECTS",
            "DBA_LOGSTDBY_UNSUPPORTED_TABLE",
            "DBA_LOGSTDBY_UNSUPPORTED",
            "DBA_LOGSTDBY_NOT_UNIQUE",
            "DBA_LOGSTDBY_PARAMETERS",
            "DBA_LOGSTDBY_PROGRESS",
            "DBA_LOGSTDBY_LOG",
            "DBA_LOGSTDBY_SKIP_TRANSACTION",
            "DBA_LOGSTDBY_SKIP",
            "DBA_LOGSTDBY_EVENTS",
            "DBA_LOGSTDBY_HISTORY",
            "DBA_LOGSTDBY_EDS_TABLES",
            "DBA_LOGSTDBY_EDS_SUPPORTED",
            "DBA_OUTSTANDING_ALERTS",
            "DBA_ALERT_HISTORY",
            "DBA_THRESHOLDS",
            "DBA_TABLESPACE_THRESHOLDS",
            "DBA_AUTOTASK_OPERATION",
            "DBA_AUTOTASK_TASK",
            "DBA_AUTOTASK_SCHEDULE",
            "DBA_AUTOTASK_CLIENT_JOB",
            "DBA_AUTOTASK_WINDOW_CLIENTS",
            "DBA_AUTOTASK_WINDOW_HISTORY",
            "DBA_AUTOTASK_CLIENT_HISTORY",
            "DBA_AUTOTASK_JOB_HISTORY",
            "DBA_AUTOTASK_CLIENT",
            "DBA_HIST_BASELINE",
            "DBA_HIST_BASELINE_DETAILS",
            "DBA_HIST_SQLBIND",
            "DBA_TUNE_MVIEW",
            "DBA_FLASHBACK_ARCHIVE",
            "DBA_FLASHBACK_ARCHIVE_TS",
            "DBA_FLASHBACK_ARCHIVE_TABLES",
            "DBA_CAPTURE",
            "DBA_STREAMS_SPLIT_MERGE",
            "DBA_STREAMS_SPLIT_MERGE_HIST",
            "DBA_CAPTURE_PARAMETERS",
            "DBA_CAPTURE_PREPARED_DATABASE",
            "DBA_CAPTURE_PREPARED_SCHEMAS",
            "DBA_CAPTURE_PREPARED_TABLES",
            "DBA_SYNC_CAPTURE_PREPARED_TABS",
            "DBA_CAPTURE_EXTRA_ATTRIBUTES",
            "DBA_REGISTERED_ARCHIVED_LOG",
            "DBA_SYNC_CAPTURE",
            "DBA_APPLY",
            "DBA_APPLY_PARAMETERS",
            "DBA_APPLY_INSTANTIATED_OBJECTS",
            "DBA_APPLY_INSTANTIATED_SCHEMAS",
            "DBA_APPLY_INSTANTIATED_GLOBAL",
            "DBA_APPLY_KEY_COLUMNS",
            "DBA_APPLY_CONFLICT_COLUMNS",
            "DBA_APPLY_TABLE_COLUMNS",
            "DBA_APPLY_DML_HANDLERS",
            "DBA_APPLY_PROGRESS",
            "DBA_APPLY_ERROR",
            "DBA_APPLY_ENQUEUE",
            "DBA_APPLY_EXECUTE",
            "DBA_APPLY_SPILL_TXN",
            "DBA_XSTREAM_OUTBOUND",
            "DBA_XSTREAM_INBOUND",
            "DBA_APPLY_CHANGE_HANDLERS",
            "DBA_PROPAGATION",
            "DBA_FILE_GROUPS",
            "DBA_FILE_GROUP_VERSIONS",
            "DBA_FILE_GROUP_EXPORT_INFO",
            "DBA_FILE_GROUP_FILES",
            "DBA_FILE_GROUP_TABLESPACES",
            "DBA_FILE_GROUP_TABLES",
            "DBA_STREAMS_TP_DATABASE",
            "DBA_STREAMS_TP_COMPONENT",
            "DBA_STREAMS_TP_COMPONENT_LINK",
            "DBA_STREAMS_TP_COMPONENT_STAT",
            "DBA_STREAMS_TP_PATH_STAT",
            "DBA_STREAMS_TP_PATH_BOTTLENECK",
            "DBA_STREAMS_MESSAGE_CONSUMERS",
            "DBA_STREAMS_GLOBAL_RULES",
            "DBA_STREAMS_SCHEMA_RULES",
            "DBA_STREAMS_TABLE_RULES",
            "DBA_STREAMS_MESSAGE_RULES",
            "DBA_STREAMS_RULES",
            "DBA_SYNC_CAPTURE_TABLES",
            "DBA_XSTREAM_RULES",
            "DBA_STREAMS_TRANSFORM_FUNCTION",
            "DBA_STREAMS_ADMINISTRATOR",
            "DBA_STREAMS_TRANSFORMATIONS",
            "DBA_STREAMS_RENAME_SCHEMA",
            "DBA_STREAMS_RENAME_TABLE",
            "DBA_STREAMS_DELETE_COLUMN",
            "DBA_STREAMS_KEEP_COLUMNS",
            "DBA_STREAMS_RENAME_COLUMN",
            "DBA_STREAMS_ADD_COLUMN",
            "DBA_RECOVERABLE_SCRIPT",
            "DBA_RECOVERABLE_SCRIPT_HIST",
            "DBA_RECOVERABLE_SCRIPT_PARAMS",
            "DBA_RECOVERABLE_SCRIPT_BLOCKS",
            "DBA_RECOVERABLE_SCRIPT_ERRORS",
            "DBA_COMPARISON",
            "DBA_COMPARISON_COLUMNS",
            "DBA_COMPARISON_SCAN",
            "DBA_COMPARISON_SCAN_SUMMARY",
            "DBA_COMPARISON_SCAN_VALUES",
            "DBA_COMPARISON_ROW_DIF",
            "DBA_STREAMS_COLUMNS",
            "DBA_STREAMS_UNSUPPORTED",
            "DBA_STREAMS_NEWLY_SUPPORTED",
            "DBA_JAVA_POLICY",
            "DBA_JAVA_CLASSES",
            "DBA_JAVA_LAYOUTS",
            "DBA_JAVA_IMPLEMENTS",
            "DBA_JAVA_INNERS",
            "DBA_JAVA_FIELDS",
            "DBA_JAVA_METHODS",
            "DBA_JAVA_ARGUMENTS",
            "DBA_JAVA_THROWS",
            "DBA_JAVA_DERIVATIONS",
            "DBA_JAVA_RESOLVERS",
            "DBA_JAVA_COMPILER_OPTIONS",
            "DBA_EPG_DAD_AUTHORIZATION",
            "DBA_XML_TABLES",
            "DBA_XML_TAB_COLS",
            "DBA_XML_VIEWS",
            "DBA_XML_VIEW_COLS",
            "DBA_XML_SCHEMAS",
            "DBA_XML_INDEXES",
            "DBA_NETWORK_ACLS",
            "DBA_NETWORK_ACL_PRIVILEGES",
            "DBA_WALLET_ACLS",
            "DBA_XDS_OBJECTS",
            "DBA_XDS_INSTANCE_SETS",
            "DBA_XDS_ATTRIBUTE_SECS",
            "DBA_AW_PROP",
            "DBA_AW_OBJ",
            "DBA_TRANSFORMATIONS",
            "DBA_ADVISOR_PARAMETERS",
            "DBA_PARALLEL_EXECUTE_TASKS",
            "DBA_ADVISOR_RATIONALE",
            "DBA_ADVISOR_ACTIONS",
            "DBA_QUEUE_PUBLISHERS",
            "DBA_SQLSET_BINDS",
            "DBA_ADDM_FINDINGS",
            "DBA_PARALLEL_EXECUTE_CHUNKS",
            "DBA_EXPORT_PATHS",
            "DBA_QUEUE_SCHEDULES",
            "DBA_WORKLOAD_CONNECTION_MAP",
            "DBA_ADVISOR_SQLW_COLVOL",
            "DBA_ADVISOR_RECOMMENDATIONS",
            "DBA_ADVISOR_SQLA_COLVOL",
            "DBA_PART_TABLES",
            "DBA_ADVISOR_DEF_PARAMETERS",
            "DBA_ADVISOR_FINDINGS",
            "DBA_ADVISOR_DIR_INSTANCES",
            "DBA_SQLTUNE_RATIONALE_PLAN",
            "DBA_TAB_SUBPARTITIONS",
            "DBA_DBFS_HS_FIXED_PROPERTIES",
            "DBA_WAITERS",
            "DBA_LOCK_INTERNAL",
            "DBA_ADVISOR_DIR_DEFINITIONS",
            "DBA_DML_LOCKS",
            "DBA_REGISTRY_DEPENDENCIES",
            "DBA_IAS_OBJECTS",
            "DBA_IND_SUBPARTITIONS",
            "DBA_WORKLOAD_REPLAYS",
            "DBA_PART_COL_STATISTICS",
            "DBA_ADVISOR_SQLA_WK_MAP",
            "DBA_BLOCKERS",
            "DBA_SQLTUNE_PLANS",
            "DBA_REGISTRY",
            "DBA_ADVISOR_LOG",
            "DBA_REGISTRY_HISTORY",
            "DBA_ADVISOR_SQLA_REC_SUM",
            "DBA_ADVISOR_EXECUTION_TYPES",
            "DBA_SQLSET_STATEMENTS",
            "DBA_ADVISOR_DEFINITIONS",
            "DBA_IAS_GEN_STMTS_EXP",
            "DBA_WORKLOAD_REPLAY_FILTER_SET",
            "DBA_ADVISOR_SQLA_TABLES",
            "DBA_ADVISOR_USAGE",
            "DBA_WORKLOAD_CAPTURES",
            "DBA_LOGMNR_PURGED_LOG",
            "DBA_ADVISOR_PARAMETERS_PROJ",
            "DBA_ADVISOR_SQLW_SUM",
            "DBA_KGLLOCK",
            "DBA_IND_PARTITIONS",
            "DBA_SQLSET_REFERENCES",
            "DBA_SUBPART_KEY_COLUMNS",
            "DBA_ADVISOR_SQLA_WK_SUM",
            "DBA_ADVISOR_OBJECT_TYPES",
            "DBA_ADDM_TASK_DIRECTIVES",
            "DBA_ADVISOR_SQLW_STMTS",
            "DBA_IAS_TEMPLATES",
            "DBA_SQLSET_PLANS",
            "DBA_IAS_POSTGEN_STMTS",
            "DBA_ADVISOR_TEMPLATES",
            "DBA_ADVISOR_SQLW_TEMPLATES",
            "DBA_ADVISOR_FDG_BREAKDOWN",
            "DBA_ADDM_SYSTEM_DIRECTIVES",
            "DBA_IAS_PREGEN_STMTS",
            "DBA_LOCK",
            "DBA_SQLSET",
            "DBA_TAB_PARTITIONS",
            "DBA_SUBPART_HISTOGRAMS",
            "DBA_ADDM_FDG_BREAKDOWN",
            "DBA_IAS_CONSTRAINT_EXP",
            "DBA_FLASHBACK_TXN_STATE",
            "DBA_LOB_SUBPARTITIONS",
            "DBA_KEEPSIZES",
            "DBA_AQ_AGENT_PRIVS",
            "DBA_ADVISOR_COMMANDS",
            "DBA_DBFS_HS_PROPERTIES",
            "DBA_SQLTUNE_BINDS",
            "DBA_JAVA_NCOMPS",
            "DBA_REPCAT",
            "DBA_ADVISOR_EXECUTIONS",
            "DBA_REGISTRY_PROGRESS",
            "DBA_PART_LOBS",
            "DBA_IAS_OBJECTS_BASE",
            "DBA_SERVER_REGISTRY",
            "DBA_ATTRIBUTE_TRANSFORMATIONS",
            "DBA_REGISTRY_LOG",
            "DBA_IAS_SITES",
            "DBA_PART_INDEXES",
            "DBA_ADVISOR_SQLA_TABVOL",
            "DBA_WORKLOAD_FILTERS",
            "DBA_IAS_OBJECTS_EXP",
            "DBA_ADDM_INSTANCES",
            "DBA_ADVISOR_JOURNAL",
            "DBA_HIST_ASH_SNAPSHOT",
            "DBA_ADVISOR_EXEC_PARAMETERS",
            "DBA_ADVISOR_FINDING_NAMES",
            "DBA_ADVISOR_SQLW_TABLES",
            "DBA_DBFS_HS_COMMANDS",
            "DBA_ADVISOR_SQLW_TABVOL",
            "DBA_LOGMNR_SESSION",
            "DBA_DDL_LOCKS",
            "DBA_SUBPART_COL_STATISTICS",
            "DBA_WORKLOAD_REPLAY_DIVERGENCE",
            "DBA_ADVISOR_OBJECTS",
            "DBA_SUBPARTITION_TEMPLATES",
            "DBA_LOGMNR_LOG",
            "DBA_REGISTRY_HIERARCHY",
            "DBA_ADVISOR_SQLW_PARAMETERS",
            "DBA_ADVISOR_TASKS",
            "DBA_LOB_TEMPLATES",
            "DBA_REGISTRY_DATABASE",
            "DBA_ANALYZE_OBJECTS",
            "DBA_SQLTUNE_STATISTICS",
            "DBA_SQL_MONITOR_USAGE",
            "DBA_ADVISOR_SQLSTATS",
            "DBA_AQ_AGENTS",
            "DBA_PART_HISTOGRAMS",
            "DBA_PART_KEY_COLUMNS",
            "DBA_ADVISOR_DIR_TASK_INST",
            "DBA_LOB_PARTITIONS",
            "DBA_ADDM_TASKS",
            "DBA_ADVISOR_SQLA_WK_STMTS",
            "DBA_IAS_GEN_STMTS",
            "DBA_FLASHBACK_TXN_REPORT",
            "DBA_INVALID_OBJECTS",
            "DBA_DBFS_HS",
            "DBA_ADVISOR_SQLW_JOURNAL",
            "DBA_ADVISOR_SQLPLANS",
            "USER_ADDM_FDG_BREAKDOWN",
            "USER_ADDM_FINDINGS",
            "USER_ADDM_INSTANCES",
            "USER_ADDM_TASKS",
            "USER_ADDM_TASK_DIRECTIVES",
            "USER_ADVISOR_ACTIONS",
            "USER_ADVISOR_DIR_TASK_INST",
            "USER_ADVISOR_EXECUTIONS",
            "USER_ADVISOR_EXEC_PARAMETERS",
            "USER_ADVISOR_FDG_BREAKDOWN",
            "USER_ADVISOR_FINDINGS",
            "USER_ADVISOR_JOURNAL",
            "USER_ADVISOR_LOG",
            "USER_ADVISOR_OBJECTS",
            "USER_ADVISOR_PARAMETERS",
            "USER_ADVISOR_RATIONALE",
            "USER_ADVISOR_RECOMMENDATIONS",
            "USER_ADVISOR_SQLA_COLVOL",
            "USER_ADVISOR_SQLA_REC_SUM",
            "USER_ADVISOR_SQLA_TABLES",
            "USER_ADVISOR_SQLA_TABVOL",
            "USER_ADVISOR_SQLA_WK_MAP",
            "USER_ADVISOR_SQLA_WK_STMTS",
            "USER_ADVISOR_SQLA_WK_SUM",
            "USER_ADVISOR_SQLPLANS",
            "USER_ADVISOR_SQLSTATS",
            "USER_ADVISOR_SQLW_COLVOL",
            "USER_ADVISOR_SQLW_JOURNAL",
            "USER_ADVISOR_SQLW_PARAMETERS",
            "USER_ADVISOR_SQLW_STMTS",
            "USER_ADVISOR_SQLW_SUM",
            "USER_ADVISOR_SQLW_TABLES",
            "USER_ADVISOR_SQLW_TABVOL",
            "USER_ADVISOR_SQLW_TEMPLATES",
            "USER_ADVISOR_TASKS",
            "USER_ADVISOR_TEMPLATES",
            "USER_ALL_TABLES",
            "USER_AQ_AGENT_PRIVS",
            "USER_ARGUMENTS",
            "USER_ASSEMBLIES",
            "USER_ASSOCIATIONS",
            "USER_ATTRIBUTE_TRANSFORMATIONS",
            "USER_AUDIT_OBJECT",
            "USER_AUDIT_POLICIES",
            "USER_AUDIT_POLICY_COLUMNS",
            "USER_AUDIT_SESSION",
            "USER_AUDIT_STATEMENT",
            "USER_AUDIT_TRAIL",
            "USER_AWS",
            "USER_AW_OBJ",
            "USER_AW_PROP",
            "USER_AW_PS",
            "USER_BASE_TABLE_MVIEWS",
            "USER_CATALOG",
            "USER_CHANGE_NOTIFICATION_REGS",
            "USER_CLUSTERS",
            "USER_CLUSTER_HASH_EXPRESSIONS",
            "USER_CLU_COLUMNS",
            "USER_COLL_TYPES",
            "USER_COL_COMMENTS",
            "USER_COL_PENDING_STATS",
            "USER_COL_PRIVS",
            "USER_COL_PRIVS_MADE",
            "USER_COL_PRIVS_RECD",
            "USER_COMPARISON",
            "USER_COMPARISON_COLUMNS",
            "USER_COMPARISON_ROW_DIF",
            "USER_COMPARISON_SCAN",
            "USER_COMPARISON_SCAN_SUMMARY",
            "USER_COMPARISON_SCAN_VALUES",
            "USER_CONSTRAINTS",
            "USER_CONS_COLUMNS",
            "USER_CONS_OBJ_COLUMNS",
            "USER_CQ_NOTIFICATION_QUERIES",
            "USER_CUBES",
            "USER_CUBE_ATTRIBUTES",
            "USER_CUBE_ATTR_VISIBILITY",
            "USER_CUBE_BUILD_PROCESSES",
            "USER_CUBE_CALCULATED_MEMBERS",
            "USER_CUBE_DIMENSIONALITY",
            "USER_CUBE_DIMENSIONS",
            "USER_CUBE_DIM_LEVELS",
            "USER_CUBE_DIM_MODELS",
            "USER_CUBE_DIM_VIEWS",
            "USER_CUBE_DIM_VIEW_COLUMNS",
            "USER_CUBE_HIERARCHIES",
            "USER_CUBE_HIER_LEVELS",
            "USER_CUBE_HIER_VIEWS",
            "USER_CUBE_HIER_VIEW_COLUMNS",
            "USER_CUBE_MEASURES",
            "USER_CUBE_VIEWS",
            "USER_CUBE_VIEW_COLUMNS",
            "USER_DATAPUMP_JOBS",
            "USER_DBFS_HS",
            "USER_DBFS_HS_COMMANDS",
            "USER_DBFS_HS_FILES",
            "USER_DBFS_HS_FIXED_PROPERTIES",
            "USER_DBFS_HS_PROPERTIES",
            "USER_DB_LINKS",
            "USER_DEPENDENCIES",
            "USER_DIMENSIONS",
            "USER_DIM_ATTRIBUTES",
            "USER_DIM_CHILD_OF",
            "USER_DIM_HIERARCHIES",
            "USER_DIM_JOIN_KEY",
            "USER_DIM_LEVELS",
            "USER_DIM_LEVEL_KEY",
            "USER_EDITIONING_VIEWS",
            "USER_EDITIONING_VIEWS_AE",
            "USER_EDITIONING_VIEW_COLS",
            "USER_EDITIONING_VIEW_COLS_AE",
            "USER_ENCRYPTED_COLUMNS",
            "USER_EPG_DAD_AUTHORIZATION",
            "USER_ERRORS",
            "USER_ERRORS_AE",
            "USER_EVALUATION_CONTEXTS",
            "USER_EVALUATION_CONTEXT_TABLES",
            "USER_EVALUATION_CONTEXT_VARS",
            "USER_EXTENTS",
            "USER_EXTERNAL_LOCATIONS",
            "USER_EXTERNAL_TABLES",
            "USER_FILE_GROUPS",
            "USER_FILE_GROUP_EXPORT_INFO",
            "USER_FILE_GROUP_FILES",
            "USER_FILE_GROUP_TABLES",
            "USER_FILE_GROUP_TABLESPACES",
            "USER_FILE_GROUP_VERSIONS",
            "USER_FLASHBACK_ARCHIVE",
            "USER_FLASHBACK_ARCHIVE_TABLES",
            "USER_FLASHBACK_TXN_REPORT",
            "USER_FLASHBACK_TXN_STATE",
            "USER_FREE_SPACE",
            "USER_IDENTIFIERS",
            "USER_INDEXES",
            "USER_INDEXTYPES",
            "USER_INDEXTYPE_ARRAYTYPES",
            "USER_INDEXTYPE_COMMENTS",
            "USER_INDEXTYPE_OPERATORS",
            "USER_IND_COLUMNS",
            "USER_IND_EXPRESSIONS",
            "USER_IND_PARTITIONS",
            "USER_IND_PENDING_STATS",
            "USER_IND_STATISTICS",
            "USER_IND_SUBPARTITIONS",
            "USER_INTERNAL_TRIGGERS",
            "USER_JAVA_ARGUMENTS",
            "USER_JAVA_CLASSES",
            "USER_JAVA_COMPILER_OPTIONS",
            "USER_JAVA_DERIVATIONS",
            "USER_JAVA_FIELDS",
            "USER_JAVA_IMPLEMENTS",
            "USER_JAVA_INNERS",
            "USER_JAVA_LAYOUTS",
            "USER_JAVA_METHODS",
            "USER_JAVA_NCOMPS",
            "USER_JAVA_POLICY",
            "USER_JAVA_RESOLVERS",
            "USER_JAVA_THROWS",
            "USER_JOBS",
            "USER_JOIN_IND_COLUMNS",
            "USER_LIBRARIES",
            "USER_LOBS",
            "USER_LOB_PARTITIONS",
            "USER_LOB_SUBPARTITIONS",
            "USER_LOB_TEMPLATES",
            "USER_LOG_GROUPS",
            "USER_LOG_GROUP_COLUMNS",
            "USER_MEASURE_FOLDERS",
            "USER_MEASURE_FOLDER_CONTENTS",
            "USER_METHOD_PARAMS",
            "USER_METHOD_RESULTS",
            "USER_MINING_MODELS",
            "USER_MINING_MODEL_ATTRIBUTES",
            "USER_MINING_MODEL_SETTINGS",
            "USER_MVIEWS",
            "USER_MVIEW_AGGREGATES",
            "USER_MVIEW_ANALYSIS",
            "USER_MVIEW_COMMENTS",
            "USER_MVIEW_DETAIL_PARTITION",
            "USER_MVIEW_DETAIL_RELATIONS",
            "USER_MVIEW_DETAIL_SUBPARTITION",
            "USER_MVIEW_JOINS",
            "USER_MVIEW_KEYS",
            "USER_MVIEW_LOGS",
            "USER_MVIEW_REFRESH_TIMES",
            "USER_NESTED_TABLES",
            "USER_NESTED_TABLE_COLS",
            "USER_NETWORK_ACL_PRIVILEGES",
            "USER_OBJECTS",
            "USER_OBJECTS_AE",
            "USER_OBJECT_SIZE",
            "USER_OBJECT_TABLES",
            "USER_OBJ_AUDIT_OPTS",
            "USER_OBJ_COLATTRS",
            "USER_OLDIMAGE_COLUMNS",
            "USER_OPANCILLARY",
            "USER_OPARGUMENTS",
            "USER_OPBINDINGS",
            "USER_OPERATORS",
            "USER_OPERATOR_COMMENTS",
            "USER_OUTLINES",
            "USER_OUTLINE_HINTS",
            "USER_PARALLEL_EXECUTE_CHUNKS",
            "USER_PARALLEL_EXECUTE_TASKS",
            "USER_PARTIAL_DROP_TABS",
            "USER_PART_COL_STATISTICS",
            "USER_PART_HISTOGRAMS",
            "USER_PART_INDEXES",
            "USER_PART_KEY_COLUMNS",
            "USER_PART_LOBS",
            "USER_PART_TABLES",
            "USER_PASSWORD_LIMITS",
            "USER_PENDING_CONV_TABLES",
            "USER_PLSQL_OBJECT_SETTINGS",
            "USER_POLICIES",
            "USER_POLICY_CONTEXTS",
            "USER_POLICY_GROUPS",
            "USER_PROCEDURES",
            "USER_PROXIES",
            "USER_PUBLISHED_COLUMNS",
            "USER_QUEUES",
            "USER_QUEUE_PUBLISHERS",
            "USER_QUEUE_SCHEDULES",
            "USER_QUEUE_SUBSCRIBERS",
            "USER_QUEUE_TABLES",
            "USER_RECYCLEBIN",
            "USER_REFRESH",
            "USER_REFRESH_CHILDREN",
            "USER_REFS",
            "USER_REGISTERED_MVIEWS",
            "USER_REGISTERED_SNAPSHOTS",
            "USER_REGISTRY",
            "USER_REPAUDIT_ATTRIBUTE",
            "USER_REPAUDIT_COLUMN",
            "USER_REPCAT",
            "USER_REPCATLOG",
            "USER_REPCOLUMN",
            "USER_REPCOLUMN_GROUP",
            "USER_REPCONFLICT",
            "USER_REPDDL",
            "USER_REPFLAVORS",
            "USER_REPFLAVOR_COLUMNS",
            "USER_REPFLAVOR_OBJECTS",
            "USER_REPGENERATED",
            "USER_REPGENOBJECTS",
            "USER_REPGROUP",
            "USER_REPGROUPED_COLUMN",
            "USER_REPGROUP_PRIVILEGES",
            "USER_REPKEY_COLUMNS",
            "USER_REPOBJECT",
            "USER_REPPARAMETER_COLUMN",
            "USER_REPPRIORITY",
            "USER_REPPRIORITY_GROUP",
            "USER_REPPROP",
            "USER_REPRESOLUTION",
            "USER_REPRESOLUTION_METHOD",
            "USER_REPRESOLUTION_STATISTICS",
            "USER_REPRESOL_STATS_CONTROL",
            "USER_REPSCHEMA",
            "USER_REPSITES",
            "USER_RESOURCE_LIMITS",
            "USER_RESUMABLE",
            "USER_REWRITE_EQUIVALENCES",
            "USER_ROLE_PRIVS",
            "USER_RSRC_CONSUMER_GROUP_PRIVS",
            "USER_RSRC_MANAGER_SYSTEM_PRIVS",
            "USER_RULES",
            "USER_RULESETS",
            "USER_RULE_SETS",
            "USER_RULE_SET_RULES",
            "USER_SCHEDULER_CHAINS",
            "USER_SCHEDULER_CHAIN_RULES",
            "USER_SCHEDULER_CHAIN_STEPS",
            "USER_SCHEDULER_CREDENTIALS",
            "USER_SCHEDULER_DB_DESTS",
            "USER_SCHEDULER_DESTS",
            "USER_SCHEDULER_FILE_WATCHERS",
            "USER_SCHEDULER_GROUPS",
            "USER_SCHEDULER_GROUP_MEMBERS",
            "USER_SCHEDULER_JOBS",
            "USER_SCHEDULER_JOB_ARGS",
            "USER_SCHEDULER_JOB_DESTS",
            "USER_SCHEDULER_JOB_LOG",
            "USER_SCHEDULER_JOB_RUN_DETAILS",
            "USER_SCHEDULER_NOTIFICATIONS",
            "USER_SCHEDULER_PROGRAMS",
            "USER_SCHEDULER_PROGRAM_ARGS",
            "USER_SCHEDULER_REMOTE_JOBSTATE",
            "USER_SCHEDULER_RUNNING_CHAINS",
            "USER_SCHEDULER_SCHEDULES",
            "USER_SECONDARY_OBJECTS",
            "USER_SEC_RELEVANT_COLS",
            "USER_SEGMENTS",
            "USER_SEQUENCES",
            "USER_SNAPSHOTS",
            "USER_SNAPSHOT_LOGS",
            "USER_SOURCE",
            "USER_SOURCE_AE",
            "USER_SOURCE_TABLES",
            "USER_SQLJ_TYPES",
            "USER_SQLJ_TYPE_ATTRS",
            "USER_SQLJ_TYPE_METHODS",
            "USER_SQLSET",
            "USER_SQLSET_BINDS",
            "USER_SQLSET_PLANS",
            "USER_SQLSET_REFERENCES",
            "USER_SQLSET_STATEMENTS",
            "USER_SQLTUNE_BINDS",
            "USER_SQLTUNE_PLANS",
            "USER_SQLTUNE_RATIONALE_PLAN",
            "USER_SQLTUNE_STATISTICS",
            "USER_STAT_EXTENSIONS",
            "USER_STORED_SETTINGS",
            "USER_SUBPARTITION_TEMPLATES",
            "USER_SUBPART_COL_STATISTICS",
            "USER_SUBPART_HISTOGRAMS",
            "USER_SUBPART_KEY_COLUMNS",
            "USER_SUBSCRIBED_COLUMNS",
            "USER_SUBSCRIBED_TABLES",
            "USER_SUBSCRIPTIONS",
            "USER_SUBSCR_REGISTRATIONS",
            "USER_SUMMARIES",
            "USER_SYNONYMS",
            "USER_SYS_PRIVS",
            "USER_TABLES",
            "USER_TABLESPACES",
            "USER_TAB_COLS",
            "USER_TAB_COLUMNS",
            "USER_TAB_COL_STATISTICS",
            "USER_TAB_COMMENTS",
            "USER_TAB_HISTGRM_PENDING_STATS",
            "USER_TAB_HISTOGRAMS",
            "USER_TAB_MODIFICATIONS",
            "USER_TAB_PARTITIONS",
            "USER_TAB_PENDING_STATS",
            "USER_TAB_PRIVS",
            "USER_TAB_PRIVS_MADE",
            "USER_TAB_PRIVS_RECD",
            "USER_TAB_STATISTICS",
            "USER_TAB_STATS_HISTORY",
            "USER_TAB_STAT_PREFS",
            "USER_TAB_SUBPARTITIONS",
            "USER_TRANSFORMATIONS",
            "USER_TRIGGERS",
            "USER_TRIGGER_COLS",
            "USER_TS",
            "USER_TSTZ_TABLES",
            "USER_TSTZ_TAB_COLS",
            "USER_TS_QUOTAS",
            "USER_TUNE_MVIEW",
            "USER_TYPES",
            "USER_TYPE_ATTRS",
            "USER_TYPE_METHODS",
            "USER_TYPE_VERSIONS",
            "USER_UNUSED_COL_TABS",
            "USER_UPDATABLE_COLUMNS",
            "USER_USERS",
            "USER_USTATS",
            "USER_VARRAYS",
            "USER_VIEWS",
            "USER_VIEWS_AE",
            "USER_WARNING_SETTINGS",
            "USER_XDS_ATTRIBUTE_SECS",
            "USER_XDS_INSTANCE_SETS",
            "USER_XDS_OBJECTS",
            "USER_XML_COLUMN_NAMES",
            "USER_XML_INDEXES",
            "USER_XML_SCHEMAS",
            "USER_XML_TABLES",
            "USER_XML_TAB_COLS",
            "USER_XML_VIEWS",
            "USER_XML_VIEW_COLS",
            "ALL_XML_SCHEMAS",
            "ALL_XML_SCHEMAS2",
            "ALL_CONS_COLUMNS",
            "ALL_LOG_GROUP_COLUMNS",
            "ALL_LOBS",
            "ALL_CATALOG",
            "ALL_CLUSTERS",
            "ALL_COL_COMMENTS",
            "ALL_COL_PRIVS",
            "ALL_COL_PRIVS_MADE",
            "ALL_COL_PRIVS_RECD",
            "ALL_ENCRYPTED_COLUMNS",
            "ALL_INDEXES",
            "ALL_IND_COLUMNS",
            "ALL_IND_EXPRESSIONS",
            "ALL_JOIN_IND_COLUMNS",
            "ALL_OBJECTS",
            "ALL_OBJECTS_AE",
            "ALL_SEQUENCES",
            "ALL_SYNONYMS",
            "ALL_TABLES",
            "ALL_OBJECT_TABLES",
            "ALL_ALL_TABLES",
            "ALL_TAB_COLS",
            "ALL_TAB_COLUMNS",
            "ALL_TAB_COMMENTS",
            "ALL_TAB_PRIVS",
            "ALL_TAB_PRIVS_MADE",
            "ALL_TAB_PRIVS_RECD",
            "ALL_VIEWS",
            "ALL_VIEWS_AE",
            "ALL_CONSTRAINTS",
            "ALL_LOG_GROUPS",
            "ALL_CLUSTER_HASH_EXPRESSIONS",
            "ALL_UPDATABLE_COLUMNS",
            "ALL_UNUSED_COL_TABS",
            "ALL_PARTIAL_DROP_TABS",
            "ALL_EDITIONING_VIEWS",
            "ALL_EDITIONING_VIEWS_AE",
            "ALL_EDITIONING_VIEW_COLS",
            "ALL_EDITIONING_VIEW_COLS_AE",
            "ALL_EDITIONS",
            "ALL_EDITION_COMMENTS",
            "ALL_LIBRARIES",
            "ALL_PROCEDURES",
            "ALL_STORED_SETTINGS",
            "ALL_PLSQL_OBJECT_SETTINGS",
            "ALL_ARGUMENTS",
            "ALL_ASSEMBLIES",
            "ALL_IDENTIFIERS",
            "ALL_DB_LINKS",
            "ALL_USERS",
            "ALL_DEF_AUDIT_OPTS",
            "ALL_TYPES",
            "ALL_COLL_TYPES",
            "ALL_TYPE_ATTRS",
            "ALL_TYPE_METHODS",
            "ALL_METHOD_PARAMS",
            "ALL_METHOD_RESULTS",
            "ALL_SQLJ_TYPES",
            "ALL_TYPE_VERSIONS",
            "ALL_PENDING_CONV_TABLES",
            "ALL_SQLJ_TYPE_ATTRS",
            "ALL_SQLJ_TYPE_METHODS",
            "ALL_NESTED_TABLE_COLS",
            "ALL_DIRECTORIES",
            "ALL_REFS",
            "ALL_NESTED_TABLES",
            "ALL_VARRAYS",
            "ALL_OBJ_COLATTRS",
            "ALL_CONS_OBJ_COLUMNS",
            "ALL_OPERATORS",
            "ALL_OPBINDINGS",
            "ALL_OPANCILLARY",
            "ALL_OPARGUMENTS",
            "ALL_OPERATOR_COMMENTS",
            "ALL_INDEXTYPES",
            "ALL_INDEXTYPE_COMMENTS",
            "ALL_INDEXTYPE_ARRAYTYPES",
            "ALL_INDEXTYPE_OPERATORS",
            "ALL_SECONDARY_OBJECTS",
            "ALL_CHANGE_SOURCES",
            "ALL_CHANGE_SETS",
            "ALL_CHANGE_TABLES",
            "ALL_CHANGE_PROPAGATIONS",
            "ALL_CHANGE_PROPAGATION_SETS",
            "ALL_SUMDELTA",
            "ALL_SUMMAP",
            "ALL_EXTERNAL_TABLES",
            "ALL_EXTERNAL_LOCATIONS",
            "ALL_MINING_MODELS",
            "ALL_MINING_MODEL_ATTRIBUTES",
            "ALL_MINING_MODEL_SETTINGS",
            "ALL_TAB_COL_STATISTICS",
            "ALL_TAB_HISTOGRAMS",
            "ALL_ASSOCIATIONS",
            "ALL_USTATS",
            "ALL_TAB_MODIFICATIONS",
            "ALL_TAB_STATS_HISTORY",
            "ALL_TAB_STAT_PREFS",
            "ALL_TAB_PENDING_STATS",
            "ALL_IND_PENDING_STATS",
            "ALL_COL_PENDING_STATS",
            "ALL_TAB_HISTGRM_PENDING_STATS",
            "ALL_DIMENSIONS",
            "ALL_DIM_LEVELS",
            "ALL_DIM_LEVEL_KEY",
            "ALL_DIM_ATTRIBUTES",
            "ALL_DIM_HIERARCHIES",
            "ALL_DIM_CHILD_OF",
            "ALL_DIM_JOIN_KEY",
            "ALL_SUMMARIES",
            "ALL_MVIEW_ANALYSIS",
            "ALL_MVIEW_AGGREGATES",
            "ALL_MVIEW_DETAIL_RELATIONS",
            "ALL_MVIEW_KEYS",
            "ALL_MVIEW_JOINS",
            "ALL_MVIEW_COMMENTS",
            "ALL_REFRESH_DEPENDENCIES",
            "ALL_REWRITE_EQUIVALENCES",
            "ALL_MVIEW_DETAIL_PARTITION",
            "ALL_MVIEW_DETAIL_SUBPARTITION",
            "ALL_TSTZ_TAB_COLS",
            "ALL_TSTZ_TABLES",
            "ALL_ERRORS",
            "ALL_ERRORS_AE",
            "ALL_SOURCE_AE",
            "ALL_SOURCE",
            "ALL_TRIGGERS",
            "ALL_INTERNAL_TRIGGERS",
            "ALL_TRIGGER_COLS",
            "ALL_DEPENDENCIES",
            "ALL_TRIGGER_ORDERING",
            "ALL_CONTEXT",
            "ALL_RULE_SETS",
            "ALL_RULESETS",
            "ALL_RULES",
            "ALL_RULE_SET_RULES",
            "ALL_EVALUATION_CONTEXTS",
            "ALL_EVALUATION_CONTEXT_TABLES",
            "ALL_EVALUATION_CONTEXT_VARS",
            "ALL_SNAPSHOTS",
            "ALL_SNAPSHOT_LOGS",
            "ALL_REFRESH",
            "ALL_REFRESH_CHILDREN",
            "ALL_REGISTERED_SNAPSHOTS",
            "ALL_MVIEWS",
            "ALL_MVIEW_REFRESH_TIMES",
            "ALL_MVIEW_LOGS",
            "ALL_BASE_TABLE_MVIEWS",
            "ALL_REGISTERED_MVIEWS",
            "ALL_POLICIES",
            "ALL_POLICY_GROUPS",
            "ALL_POLICY_CONTEXTS",
            "ALL_SEC_RELEVANT_COLS",
            "ALL_AUDIT_POLICIES",
            "ALL_AUDIT_POLICY_COLUMNS",
            "ALL_QUEUE_TABLES",
            "ALL_QUEUES",
            "ALL_DEQUEUE_QUEUES",
            "ALL_INT_DEQUEUE_QUEUES",
            "ALL_REPGROUP_PRIVILEGES",
            "ALL_REPGROUP",
            "ALL_REPSITES",
            "ALL_REPSCHEMA",
            "ALL_REPOBJECT",
            "ALL_REPCOLUMN",
            "ALL_REPPROP",
            "ALL_REPKEY_COLUMNS",
            "ALL_REPGENOBJECTS",
            "ALL_REPGENERATED",
            "ALL_REPCATLOG",
            "ALL_REPDDL",
            "ALL_REPPRIORITY_GROUP",
            "ALL_REPPRIORITY",
            "ALL_REPCOLUMN_GROUP",
            "ALL_REPGROUPED_COLUMN",
            "ALL_REPCONFLICT",
            "ALL_REPRESOLUTION_METHOD",
            "ALL_REPRESOLUTION",
            "ALL_REPRESOLUTION_STATISTICS",
            "ALL_REPRESOL_STATS_CONTROL",
            "ALL_REPPARAMETER_COLUMN",
            "ALL_REPAUDIT_ATTRIBUTE",
            "ALL_REPAUDIT_COLUMN",
            "ALL_REPFLAVORS",
            "ALL_REPFLAVOR_OBJECTS",
            "ALL_REPFLAVOR_COLUMNS",
            "ALL_WARNING_SETTINGS",
            "ALL_STAT_EXTENSIONS",
            "ALL_TAB_STATISTICS",
            "ALL_IND_STATISTICS",
            "ALL_AWS",
            "ALL_AW_PS",
            "ALL_CUBES",
            "ALL_CUBE_DIMENSIONALITY",
            "ALL_CUBE_MEASURES",
            "ALL_CUBE_DIMENSIONS",
            "ALL_CUBE_HIERARCHIES",
            "ALL_CUBE_HIER_LEVELS",
            "ALL_CUBE_DIM_LEVELS",
            "ALL_CUBE_ATTRIBUTES",
            "ALL_CUBE_ATTR_VISIBILITY",
            "ALL_CUBE_DIM_MODELS",
            "ALL_CUBE_CALCULATED_MEMBERS",
            "ALL_CUBE_VIEWS",
            "ALL_CUBE_VIEW_COLUMNS",
            "ALL_CUBE_DIM_VIEWS",
            "ALL_CUBE_DIM_VIEW_COLUMNS",
            "ALL_CUBE_HIER_VIEWS",
            "ALL_CUBE_HIER_VIEW_COLUMNS",
            "ALL_MEASURE_FOLDERS",
            "ALL_MEASURE_FOLDER_CONTENTS",
            "ALL_CUBE_BUILD_PROCESSES",
            "ALL_QUEUE_SUBSCRIBERS",
            "ALL_SCHEDULER_PROGRAMS",
            "ALL_SCHEDULER_DESTS",
            "ALL_SCHEDULER_EXTERNAL_DESTS",
            "ALL_SCHEDULER_DB_DESTS",
            "ALL_SCHEDULER_JOB_DESTS",
            "ALL_SCHEDULER_JOBS",
            "ALL_SCHEDULER_JOB_CLASSES",
            "ALL_SCHEDULER_WINDOWS",
            "ALL_SCHEDULER_PROGRAM_ARGS",
            "ALL_SCHEDULER_JOB_ARGS",
            "ALL_SCHEDULER_JOB_LOG",
            "ALL_SCHEDULER_JOB_RUN_DETAILS",
            "ALL_SCHEDULER_WINDOW_LOG",
            "ALL_SCHEDULER_WINDOW_DETAILS",
            "ALL_SCHEDULER_WINDOW_GROUPS",
            "ALL_SCHEDULER_WINGROUP_MEMBERS",
            "ALL_SCHEDULER_GROUP_MEMBERS",
            "ALL_SCHEDULER_GROUPS",
            "ALL_SCHEDULER_SCHEDULES",
            "ALL_SCHEDULER_REMOTE_JOBSTATE",
            "ALL_SCHEDULER_GLOBAL_ATTRIBUTE",
            "ALL_SCHEDULER_CHAINS",
            "ALL_SCHEDULER_CHAIN_RULES",
            "ALL_SCHEDULER_CHAIN_STEPS",
            "ALL_SCHEDULER_RUNNING_CHAINS",
            "ALL_SCHEDULER_CREDENTIALS",
            "ALL_SCHEDULER_FILE_WATCHERS",
            "ALL_SCHEDULER_NOTIFICATIONS",
            "ALL_CAPTURE",
            "ALL_CAPTURE_PARAMETERS",
            "ALL_CAPTURE_PREPARED_DATABASE",
            "ALL_CAPTURE_PREPARED_SCHEMAS",
            "ALL_CAPTURE_PREPARED_TABLES",
            "ALL_SYNC_CAPTURE_PREPARED_TABS",
            "ALL_CAPTURE_EXTRA_ATTRIBUTES",
            "ALL_SYNC_CAPTURE",
            "ALL_APPLY",
            "ALL_APPLY_PARAMETERS",
            "ALL_APPLY_KEY_COLUMNS",
            "ALL_APPLY_CONFLICT_COLUMNS",
            "ALL_APPLY_TABLE_COLUMNS",
            "ALL_APPLY_DML_HANDLERS",
            "ALL_APPLY_PROGRESS",
            "ALL_APPLY_ERROR",
            "ALL_APPLY_ENQUEUE",
            "ALL_APPLY_EXECUTE",
            "ALL_XSTREAM_OUTBOUND",
            "ALL_XSTREAM_INBOUND",
            "ALL_APPLY_CHANGE_HANDLERS",
            "ALL_PROPAGATION",
            "ALL_FILE_GROUPS",
            "ALL_FILE_GROUP_VERSIONS",
            "ALL_FILE_GROUP_EXPORT_INFO",
            "ALL_FILE_GROUP_FILES",
            "ALL_FILE_GROUP_TABLESPACES",
            "ALL_FILE_GROUP_TABLES",
            "ALL_STREAMS_MESSAGE_CONSUMERS",
            "ALL_STREAMS_GLOBAL_RULES",
            "ALL_STREAMS_SCHEMA_RULES",
            "ALL_STREAMS_TABLE_RULES",
            "ALL_STREAMS_MESSAGE_RULES",
            "ALL_STREAMS_RULES",
            "ALL_SYNC_CAPTURE_TABLES",
            "ALL_XSTREAM_RULES",
            "ALL_STREAMS_TRANSFORM_FUNCTION",
            "ALL_STREAMS_COLUMNS",
            "ALL_STREAMS_UNSUPPORTED",
            "ALL_STREAMS_NEWLY_SUPPORTED",
            "ALL_JAVA_CLASSES",
            "ALL_JAVA_LAYOUTS",
            "ALL_JAVA_IMPLEMENTS",
            "ALL_JAVA_INNERS",
            "ALL_JAVA_FIELDS",
            "ALL_JAVA_METHODS",
            "ALL_JAVA_ARGUMENTS",
            "ALL_JAVA_THROWS",
            "ALL_JAVA_DERIVATIONS",
            "ALL_JAVA_RESOLVERS",
            "ALL_JAVA_NCOMPS",
            "ALL_JAVA_COMPILER_OPTIONS",
            "ALL_XML_TABLES",
            "ALL_XML_TAB_COLS",
            "ALL_XML_VIEWS",
            "ALL_XML_VIEW_COLS",
            "ALL_XML_INDEXES",
            "ALL_XDS_OBJECTS",
            "ALL_XDS_INSTANCE_SETS",
            "ALL_XDS_ATTRIBUTE_SECS",
            "ALL_XSC_SECURITY_CLASS",
            "ALL_XSC_SECURITY_CLASS_STATUS",
            "ALL_XSC_SECURITY_CLASS_DEP",
            "ALL_XSC_PRIVILEGE",
            "ALL_XSC_AGGREGATE_PRIVILEGE",
            "ALL_AW_PROP",
            "ALL_AW_OBJ",
            "ALL_AW_PROP_NAME",
            "ALL_AW_AC",
            "ALL_LOB_SUBPARTITIONS",
            "ALL_PART_INDEXES",
            "ALL_LOB_TEMPLATES",
            "ALL_PART_HISTOGRAMS",
            "ALL_SQLSET_BINDS",
            "ALL_SERVICES",
            "ALL_REPCAT",
            "ALL_LOB_PARTITIONS",
            "ALL_SUBPARTITION_TEMPLATES",
            "ALL_TRANSFORMATIONS",
            "ALL_PART_LOBS",
            "ALL_SUBPART_HISTOGRAMS",
            "ALL_SUBPART_KEY_COLUMNS",
            "ALL_TAB_SUBPARTITIONS",
            "ALL_REGISTRY_BANNERS",
            "ALL_PART_KEY_COLUMNS",
            "ALL_PART_TABLES",
            "ALL_IND_SUBPARTITIONS",
            "ALL_SUBPART_COL_STATISTICS",
            "ALL_QUEUE_SCHEDULES",
            "ALL_SQLSET_PLANS",
            "ALL_PART_COL_STATISTICS",
            "ALL_SQLSET",
            "ALL_SQLSET_REFERENCES",
            "ALL_QUEUE_PUBLISHERS",
            "ALL_SCHEDULER_REMOTE_DATABASES",
            "ALL_IND_PARTITIONS",
            "ALL_TAB_PARTITIONS",
            "ALL_ATTRIBUTE_TRANSFORMATIONS",
            "ALL_SQLSET_STATEMENTS",
            "ALL_PROBE_OBJECTS",
            "ALL_AW_AC_10G",
            "ALL$OLAP2_AWS",
            "AUDIT_ACTIONS",
            "COLUMN_PRIVILEGES",
            "DATABASE_COMPATIBLE_LEVEL",
            "DBMS_ALERT_INFO",
            "DBMS_LOCK_ALLOCATED",
            "DICTIONARY",
            "DICT_COLUMNS",
            "DUAL",
            "GLOBAL_NAME",
            "INDEX_HISTOGRAM",
            "INDEX_STATS",
            "NLS_DATABASE_PARAMETERS",
            "NLS_INSTANCE_PARAMETERS",
            "NLS_SESSION_PARAMETERS",
            "RESOURCE_COST",
            "ROLE_ROLE_PRIVS",
            "ROLE_SYS_PRIVS",
            "ROLE_TAB_PRIVS",
            "SESSION_PRIVS",
            "SESSION_ROLES",
            "TABLE_PRIVILEGES",
            "V$PARALLEL_DEGREE_LIMIT_MTH",
            "V$PARAMETER",
            "V$PARAMETER2",
            "V$PARAMETER_VALID_VALUES",
            "V$PERSISTENT_PUBLISHERS",
            "V$PERSISTENT_QMN_CACHE",
            "V$PERSISTENT_QUEUES",
            "V$PERSISTENT_SUBSCRIBERS",
            "V$PGASTAT",
            "V$PGA_TARGET_ADVICE",
            "V$PGA_TARGET_ADVICE_HISTOGRAM",
            "V$POLICY_HISTORY",
            "V$PQ_SESSTAT",
            "V$PQ_SLAVE",
            "V$PQ_SYSSTAT",
            "V$PQ_TQSTAT",
            "V$PROCESS",
            "V$PROCESS_GROUP",
            "V$PROCESS_MEMORY",
            "V$PROCESS_MEMORY_DETAIL",
            "V$PROCESS_MEMORY_DETAIL_PROG",
            "V$PROPAGATION_RECEIVER",
            "V$PROPAGATION_SENDER",
            "V$PROXY_ARCHIVEDLOG",
            "V$PROXY_ARCHIVELOG_DETAILS",
            "V$PROXY_ARCHIVELOG_SUMMARY",
            "V$PROXY_COPY_DETAILS",
            "V$PROXY_COPY_SUMMARY",
            "V$PROXY_DATAFILE",
            "V$PWFILE_USERS",
            "V$PX_BUFFER_ADVICE",
            "V$PX_INSTANCE_GROUP",
            "V$PX_PROCESS",
            "V$PX_PROCESS_SYSSTAT",
            "V$PX_SESSION",
            "V$PX_SESSTAT",
            "V$QMON_COORDINATOR_STATS",
            "V$QMON_SERVER_STATS",
            "V$QMON_TASKS",
            "V$QMON_TASK_STATS",
            "V$QUEUE",
            "V$QUEUEING_MTH",
            "V$RECOVERY_AREA_USAGE",
            "V$RECOVERY_FILE_DEST",
            "V$RECOVERY_FILE_STATUS",
            "V$RECOVERY_LOG",
            "V$RECOVERY_PROGRESS",
            "V$RECOVERY_STATUS",
            "V$RECOVER_FILE",
            "V$REDO_DEST_RESP_HISTOGRAM",
            "V$REPLPROP",
            "V$REPLQUEUE",
            "V$REQDIST",
            "V$RESERVED_WORDS",
            "V$RESOURCE",
            "V$RESOURCE_LIMIT",
            "V$RESTORE_POINT",
            "V$RESULT_CACHE_DEPENDENCY",
            "V$RESULT_CACHE_MEMORY",
            "V$RESULT_CACHE_OBJECTS",
            "V$RESULT_CACHE_STATISTICS",
            "V$RESUMABLE",
            "V$RFS_THREAD",
            "V$RMAN_BACKUP_JOB_DETAILS",
            "V$RMAN_BACKUP_SUBJOB_DETAILS",
            "V$RMAN_BACKUP_TYPE",
            "V$RMAN_COMPRESSION_ALGORITHM",
            "V$RMAN_CONFIGURATION",
            "V$RMAN_ENCRYPTION_ALGORITHMS",
            "V$RMAN_OUTPUT",
            "V$RMAN_STATUS",
            "V$ROLLNAME",
            "V$ROLLSTAT",
            "V$ROWCACHE",
            "V$ROWCACHE_PARENT",
            "V$ROWCACHE_SUBORDINATE",
            "V$RSRCMGRMETRIC",
            "V$RSRCMGRMETRIC_HISTORY",
            "V$RSRC_CONSUMER_GROUP",
            "V$RSRC_CONSUMER_GROUP_CPU_MTH",
            "V$RSRC_CONS_GROUP_HISTORY",
            "V$RSRC_PLAN",
            "V$RSRC_PLAN_CPU_MTH",
            "V$RSRC_PLAN_HISTORY",
            "V$RSRC_SESSION_INFO",
            "V$RULE",
            "V$RULE_SET",
            "V$RULE_SET_AGGREGATE_STATS",
            "V$SYSAUX_OCCUPANTS",
            "V$SYSMETRIC",
            "V$SYSMETRIC_HISTORY",
            "V$SYSMETRIC_SUMMARY",
            "V$SYSSTAT",
            "V$SYSTEM_CURSOR_CACHE",
            "V$SYSTEM_EVENT",
            "V$SYSTEM_FIX_CONTROL",
            "V$SYSTEM_PARAMETER",
            "V$SYSTEM_PARAMETER2",
            "V$SYSTEM_WAIT_CLASS",
            "V$SYS_OPTIMIZER_ENV",
            "V$SYS_TIME_MODEL",
            "V$TABLESPACE",
            "V$TEMPFILE",
            "V$TEMPORARY_LOBS",
            "V$TEMPSEG_USAGE",
            "V$TEMPSTAT",
            "V$TEMP_CACHE_TRANSFER",
            "V$TEMP_EXTENT_MAP",
            "V$TEMP_EXTENT_POOL",
            "V$TEMP_PING",
            "V$TEMP_SPACE_HEADER",
            "V$THREAD",
            "V$THRESHOLD_TYPES",
            "V$TIMER",
            "V$TIMEZONE_FILE",
            "V$TIMEZONE_NAMES",
            "V$TOPLEVELCALL",
            "V$TRANSACTION",
            "V$TRANSACTION_ENQUEUE",
            "V$TRANSPORTABLE_PLATFORM",
            "V$TSM_SESSIONS",
            "V$TYPE_SIZE",
            "V$UNDOSTAT",
            "V$UNUSABLE_BACKUPFILE_DETAILS",
            "V$VERSION",
            "V$VPD_POLICY",
            "V$WAITCLASSMETRIC",
            "V$WAITCLASSMETRIC_HISTORY",
            "V$WAITSTAT",
            "V$WAIT_CHAINS",
            "V$WALLET",
            "V$WLM_PCMETRIC",
            "V$WLM_PCMETRIC_HISTORY",
            "V$WLM_PC_STATS",
            "V$WORKLOAD_REPLAY_THREAD",
            "V$XML_AUDIT_TRAIL",
            "V$_LOCK",
            "V$DLM_ALL_LOCKS",
            "V$DLM_CONVERT_LOCAL",
            "V$DLM_CONVERT_REMOTE",
            "V$DLM_LATCH",
            "V$DLM_LOCKS",
            "V$DLM_MISC",
            "V$DLM_RESS",
            "V$DLM_TRAFFIC_CONTROLLER",
            "V$DNFS_CHANNELS",
            "V$DNFS_FILES",
            "V$DNFS_SERVERS",
            "V$DNFS_STATS",
            "V$DYNAMIC_REMASTER_STATS",
            "V$EMON",
            "V$ENABLEDPRIVS",
            "V$ENCRYPTED_TABLESPACES",
            "V$ENCRYPTION_WALLET",
            "V$ENQUEUE_LOCK",
            "V$ENQUEUE_STAT",
            "V$ENQUEUE_STATISTICS",
            "V$EVENTMETRIC",
            "V$EVENT_HISTOGRAM",
            "V$EVENT_NAME",
            "V$EXECUTION",
            "V$FAST_START_SERVERS",
            "V$FAST_START_TRANSACTIONS",
            "V$FILEMETRIC",
            "V$FILEMETRIC_HISTORY",
            "V$FILESPACE_USAGE",
            "V$FILESTAT",
            "V$FILE_CACHE_TRANSFER",
            "V$FILE_HISTOGRAM",
            "V$FILE_OPTIMIZED_HISTOGRAM",
            "V$FILE_PING",
            "V$FIXED_TABLE",
            "V$FIXED_VIEW_DEFINITION",
            "V$FLASHBACK_DATABASE_LOG",
            "V$FLASHBACK_DATABASE_LOGFILE",
            "V$FLASHBACK_DATABASE_STAT",
            "V$FLASHBACK_TXN_GRAPH",
            "V$FLASHBACK_TXN_MODS",
            "V$FLASH_RECOVERY_AREA_USAGE",
            "V$FOREIGN_ARCHIVED_LOG",
            "V$FS_FAILOVER_HISTOGRAM",
            "V$FS_FAILOVER_STATS",
            "V$GCSHVMASTER_INFO",
            "V$GCSPFMASTER_INFO",
            "V$GC_ELEMENT",
            "V$GC_ELEMENTS_WITH_COLLISIONS",
            "V$GES_BLOCKING_ENQUEUE",
            "V$GES_ENQUEUE",
            "V$GLOBALCONTEXT",
            "V$GLOBAL_BLOCKED_LOCKS",
            "V$GLOBAL_TRANSACTION",
            "V$HM_CHECK",
            "V$HM_CHECK_PARAM",
            "V$HM_FINDING",
            "V$HM_INFO",
            "V$HM_RECOMMENDATION",
            "V$HM_RUN",
            "V$HS_AGENT",
            "V$HS_PARAMETER",
            "V$HS_SESSION",
            "V$HVMASTER_INFO",
            "V$INCMETER_CONFIG",
            "V$INCMETER_INFO",
            "V$INCMETER_SUMMARY",
            "V$INDEXED_FIXED_COLUMN",
            "V$INSTANCE",
            "V$INSTANCE_CACHE_TRANSFER",
            "V$INSTANCE_LOG_GROUP",
            "V$INSTANCE_RECOVERY",
            "V$IOFUNCMETRIC",
            "V$IOFUNCMETRIC_HISTORY",
            "V$IOSTAT_CONSUMER_GROUP",
            "V$IOSTAT_FILE",
            "V$IOSTAT_FUNCTION",
            "V$IOSTAT_FUNCTION_DETAIL",
            "V$IOSTAT_NETWORK",
            "V$IO_CALIBRATION_STATUS",
            "V$IR_FAILURE",
            "V$IR_FAILURE_SET",
            "V$IR_MANUAL_CHECKLIST",
            "V$IR_REPAIR",
            "V$JAVAPOOL",
            "V$JAVA_LIBRARY_CACHE_MEMORY",
            "V$JAVA_POOL_ADVICE",
            "V$LATCH",
            "V$LATCHHOLDER",
            "V$LATCHNAME",
            "V$LATCH_CHILDREN",
            "V$LATCH_MISSES",
            "V$LATCH_PARENT",
            "V$LIBCACHE_LOCKS",
            "V$LIBRARYCACHE",
            "V$LIBRARY_CACHE_MEMORY",
            "V$LICENSE",
            "V$LISTENER_NETWORK",
            "V$LOADISTAT",
            "V$LOADPSTAT",
            "V$LOBSTAT",
            "V$LOCK",
            "V$LOCKED_OBJECT",
            "V$LOCKS_WITH_COLLISIONS",
            "V$LOCK_ACTIVITY",
            "V$LOCK_ELEMENT",
            "V$LOCK_TYPE",
            "V$LOG",
            "V$LOGFILE",
            "V$LOGHIST",
            "V$LOGMNR_CALLBACK",
            "V$LOGMNR_CONTENTS",
            "V$LOGMNR_DICTIONARY",
            "V$LOGMNR_DICTIONARY_LOAD",
            "V$LOGMNR_LATCH",
            "V$LOGMNR_LOGFILE",
            "V$LOGMNR_LOGS",
            "V$LOGMNR_PARAMETERS",
            "V$LOGMNR_PROCESS",
            "V$LOGMNR_REGION",
            "V$LOGMNR_SESSION",
            "V$LOGMNR_STATS",
            "V$LOGMNR_TRANSACTION",
            "V$LOGSTDBY",
            "V$LOGSTDBY_PROCESS",
            "V$LOGSTDBY_PROGRESS",
            "V$LOGSTDBY_STATE",
            "V$LOGSTDBY_STATS",
            "V$LOGSTDBY_TRANSACTION",
            "V$LOG_HISTORY",
            "V$MANAGED_STANDBY",
            "V$MAP_COMP_LIST",
            "V$MAP_ELEMENT",
            "V$MAP_EXT_ELEMENT",
            "V$MAP_FILE",
            "V$MAP_FILE_EXTENT",
            "V$MAP_FILE_IO_STACK",
            "V$MAP_LIBRARY",
            "V$MAP_SUBELEMENT",
            "V$MAX_ACTIVE_SESS_TARGET_MTH",
            "V$MEMORY_CURRENT_RESIZE_OPS",
            "V$MEMORY_DYNAMIC_COMPONENTS",
            "V$MEMORY_RESIZE_OPS",
            "V$MEMORY_TARGET_ADVICE",
            "V$METRIC",
            "V$METRICGROUP",
            "V$METRICNAME",
            "V$METRIC_HISTORY",
            "V$MTTR_TARGET_ADVICE",
            "V$MUTEX_SLEEP",
            "V$MUTEX_SLEEP_HISTORY",
            "V$MVREFRESH",
            "V$MYSTAT",
            "V$NFS_CLIENTS",
            "V$NFS_LOCKS",
            "V$NFS_OPEN_FILES",
            "V$NLS_PARAMETERS",
            "V$NLS_VALID_VALUES",
            "V$OBJECT_DEPENDENCY",
            "V$OBJECT_PRIVILEGE",
            "V$OBSOLETE_PARAMETER",
            "V$OFFLINE_RANGE",
            "V$OPEN_CURSOR",
            "V$OPTION",
            "V$OSSTAT",
            "GV$SCHEDULER_RUNNING_JOBS",
            "GV$SECUREFILE_TIMER",
            "GV$SEGMENT_STATISTICS",
            "GV$SEGSTAT",
            "GV$SEGSTAT_NAME",
            "GV$SERVICEMETRIC",
            "GV$SERVICEMETRIC_HISTORY",
            "GV$SERVICES",
            "GV$SERVICE_EVENT",
            "GV$SERVICE_STATS",
            "GV$SERVICE_WAIT_CLASS",
            "GV$SERV_MOD_ACT_STATS",
            "GV$SESSION",
            "GV$SESSION_BLOCKERS",
            "GV$SESSION_CONNECT_INFO",
            "GV$SESSION_CURSOR_CACHE",
            "GV$SESSION_EVENT",
            "GV$SESSION_LONGOPS",
            "GV$SESSION_OBJECT_CACHE",
            "GV$SESSION_WAIT",
            "GV$SESSION_WAIT_CLASS",
            "GV$SESSION_WAIT_HISTORY",
            "GV$SESSMETRIC",
            "GV$SESSTAT",
            "GV$SESS_IO",
            "GV$SESS_TIME_MODEL",
            "GV$SES_OPTIMIZER_ENV",
            "GV$SGA",
            "GV$SGAINFO",
            "GV$SGASTAT",
            "GV$SGA_CURRENT_RESIZE_OPS",
            "GV$SGA_DYNAMIC_COMPONENTS",
            "GV$SGA_DYNAMIC_FREE_MEMORY",
            "GV$SGA_RESIZE_OPS",
            "GV$SGA_TARGET_ADVICE",
            "GV$SHARED_POOL_ADVICE",
            "GV$SHARED_POOL_RESERVED",
            "GV$SHARED_SERVER",
            "GV$SHARED_SERVER_MONITOR",
            "GV$SORT_SEGMENT",
            "GV$SORT_USAGE",
            "GV$SPPARAMETER",
            "GV$SQL",
            "GV$SQLAREA",
            "GV$SQLAREA_PLAN_HASH",
            "GV$SQLCOMMAND",
            "GV$SQLFN_ARG_METADATA",
            "GV$SQLFN_METADATA",
            "GV$SQLPA_METRIC",
            "GV$SQLSTATS",
            "GV$SQLSTATS_PLAN_HASH",
            "GV$SQLTEXT",
            "GV$SQLTEXT_WITH_NEWLINES",
            "GV$SQL_BIND_CAPTURE",
            "GV$SQL_BIND_DATA",
            "GV$SQL_BIND_METADATA",
            "GV$SQL_CS_HISTOGRAM",
            "GV$SQL_CS_SELECTIVITY",
            "GV$SQL_CS_STATISTICS",
            "GV$SQL_CURSOR",
            "GV$SQL_FEATURE",
            "GV$SQL_FEATURE_DEPENDENCY",
            "GV$SQL_FEATURE_HIERARCHY",
            "GV$SQL_HINT",
            "GV$SQL_JOIN_FILTER",
            "GV$SQL_MONITOR",
            "GV$SQL_OPTIMIZER_ENV",
            "GV$SQL_PLAN",
            "GV$SQL_PLAN_MONITOR",
            "GV$SQL_PLAN_STATISTICS",
            "GV$SQL_PLAN_STATISTICS_ALL",
            "GV$SQL_REDIRECTION",
            "GV$SQL_SHARED_CURSOR",
            "GV$SQL_SHARED_MEMORY",
            "GV$SQL_WORKAREA",
            "GV$SQL_WORKAREA_ACTIVE",
            "GV$SQL_WORKAREA_HISTOGRAM",
            "GV$SSCR_SESSIONS",
            "GV$STANDBY_LOG",
            "GV$STATISTICS_LEVEL",
            "GV$STATNAME",
            "GV$STREAMS_APPLY_COORDINATOR",
            "GV$STREAMS_APPLY_READER",
            "GV$STREAMS_APPLY_SERVER",
            "GV$STREAMS_CAPTURE",
            "GV$STREAMS_MESSAGE_TRACKING",
            "GV$STREAMS_POOL_ADVICE",
            "GV$STREAMS_POOL_STATISTICS",
            "GV$STREAMS_TRANSACTION",
            "GV$SUBCACHE",
            "GV$SUBSCR_REGISTRATION_STATS",
            "GV$FAST_START_SERVERS",
            "GV$FAST_START_TRANSACTIONS",
            "GV$FILEMETRIC",
            "GV$FILEMETRIC_HISTORY",
            "GV$FILESPACE_USAGE",
            "GV$FILESTAT",
            "GV$FILE_CACHE_TRANSFER",
            "GV$FILE_HISTOGRAM",
            "GV$FILE_OPTIMIZED_HISTOGRAM",
            "GV$FILE_PING",
            "GV$FIXED_TABLE",
            "GV$FIXED_VIEW_DEFINITION",
            "GV$FLASHBACK_DATABASE_LOG",
            "GV$FLASHBACK_DATABASE_LOGFILE",
            "GV$FLASHBACK_DATABASE_STAT",
            "GV$FOREIGN_ARCHIVED_LOG",
            "GV$FS_FAILOVER_HISTOGRAM",
            "GV$FS_FAILOVER_STATS",
            "GV$GCSHVMASTER_INFO",
            "GV$GCSPFMASTER_INFO",
            "GV$GC_ELEMENT",
            "GV$GC_ELEMENTS_WITH_COLLISIONS",
            "GV$GES_BLOCKING_ENQUEUE",
            "GV$GES_ENQUEUE",
            "GV$GLOBALCONTEXT",
            "GV$GLOBAL_BLOCKED_LOCKS",
            "GV$GLOBAL_TRANSACTION",
            "GV$HM_CHECK",
            "GV$HM_CHECK_PARAM",
            "GV$HM_FINDING",
            "GV$HM_INFO",
            "GV$HM_RECOMMENDATION",
            "GV$HM_RUN",
            "GV$HS_AGENT",
            "GV$HS_PARAMETER",
            "GV$HS_SESSION",
            "GV$HVMASTER_INFO",
            "GV$INCMETER_CONFIG",
            "GV$INCMETER_INFO",
            "GV$INCMETER_SUMMARY",
            "GV$INDEXED_FIXED_COLUMN",
            "GV$INSTANCE",
            "GV$INSTANCE_CACHE_TRANSFER",
            "GV$INSTANCE_LOG_GROUP",
            "GV$INSTANCE_RECOVERY",
            "GV$IOFUNCMETRIC",
            "GV$IOFUNCMETRIC_HISTORY",
            "GV$IOSTAT_CONSUMER_GROUP",
            "GV$IOSTAT_FILE",
            "GV$IOSTAT_FUNCTION",
            "GV$IOSTAT_FUNCTION_DETAIL",
            "GV$IOSTAT_NETWORK",
            "GV$IO_CALIBRATION_STATUS",
            "GV$IR_FAILURE",
            "GV$IR_FAILURE_SET",
            "GV$IR_MANUAL_CHECKLIST",
            "GV$IR_REPAIR",
            "GV$JAVAPOOL",
            "GV$JAVA_LIBRARY_CACHE_MEMORY",
            "GV$JAVA_POOL_ADVICE",
            "GV$LATCH",
            "GV$LATCHHOLDER",
            "GV$LATCHNAME",
            "GV$LATCH_CHILDREN",
            "GV$LATCH_MISSES",
            "GV$LATCH_PARENT",
            "GV$LIBCACHE_LOCKS",
            "GV$LIBRARYCACHE",
            "GV$LIBRARY_CACHE_MEMORY",
            "GV$LICENSE",
            "GV$LISTENER_NETWORK",
            "GV$LOADISTAT",
            "GV$LOADPSTAT",
            "GV$LOBSTAT",
            "GV$LOCK",
            "GV$LOCKED_OBJECT",
            "GV$LOCKS_WITH_COLLISIONS",
            "GV$LOCK_ACTIVITY",
            "GV$LOCK_ELEMENT",
            "GV$LOCK_TYPE",
            "GV$LOG",
            "GV$LOGFILE",
            "GV$LOGHIST",
            "GV$LOGMNR_CALLBACK",
            "GV$LOGMNR_CONTENTS",
            "GV$LOGMNR_DICTIONARY",
            "GV$LOGMNR_DICTIONARY_LOAD",
            "GV$LOGMNR_LATCH",
            "GV$LOGMNR_LOGFILE",
            "GV$LOGMNR_LOGS",
            "GV$LOGMNR_PARAMETERS",
            "GV$LOGMNR_PROCESS",
            "GV$LOGMNR_REGION",
            "GV$LOGMNR_SESSION",
            "GV$LOGMNR_STATS",
            "GV$LOGMNR_TRANSACTION",
            "GV$LOGSTDBY",
            "GV$LOGSTDBY_PROCESS",
            "GV$LOGSTDBY_PROGRESS",
            "GV$LOGSTDBY_STATE",
            "GV$LOGSTDBY_STATS",
            "GV$LOGSTDBY_TRANSACTION",
            "GV$LOG_HISTORY",
            "GV$MANAGED_STANDBY",
            "GV$MAP_COMP_LIST",
            "GV$MAP_ELEMENT",
            "GV$MAP_EXT_ELEMENT",
            "GV$MAP_FILE",
            "GV$MAP_FILE_EXTENT",
            "GV$MAP_FILE_IO_STACK",
            "GV$MAP_LIBRARY",
            "GV$MAP_SUBELEMENT",
            "GV$MAX_ACTIVE_SESS_TARGET_MTH",
            "GV$MEMORY_CURRENT_RESIZE_OPS",
            "GV$MEMORY_DYNAMIC_COMPONENTS",
            "GV$MEMORY_RESIZE_OPS",
            "GV$MEMORY_TARGET_ADVICE",
            "GV$METRIC",
            "GV$METRICGROUP",
            "GV$METRICNAME",
            "GV$METRIC_HISTORY",
            "GV$MTTR_TARGET_ADVICE",
            "GV$MUTEX_SLEEP",
            "GV$MUTEX_SLEEP_HISTORY",
            "GV$MVREFRESH",
            "GV$MYSTAT",
            "GV$NFS_CLIENTS",
            "GV$NFS_LOCKS",
            "GV$NFS_OPEN_FILES",
            "GV$NLS_PARAMETERS",
            "GV$NLS_VALID_VALUES",
            "GV$OBJECT_DEPENDENCY",
            "GV$OBSOLETE_PARAMETER",
            "GV$OFFLINE_RANGE",
            "GV$OPEN_CURSOR",
            "GV$OPTION",
            "GV$OSSTAT",
            "GV$PARALLEL_DEGREE_LIMIT_MTH",
            "GV$PARAMETER",
            "GV$PARAMETER2",
            "GV$PARAMETER_VALID_VALUES",
            "GV$PERSISTENT_PUBLISHERS",
            "GV$PERSISTENT_QMN_CACHE",
            "GV$PERSISTENT_QUEUES",
            "GV$PERSISTENT_SUBSCRIBERS",
            "GV$PGASTAT",
            "GV$PGA_TARGET_ADVICE",
            "GV$PGA_TARGET_ADVICE_HISTOGRAM",
            "GV$POLICY_HISTORY",
            "GV$PQ_SESSTAT",
            "GV$PQ_SLAVE",
            "GV$PQ_SYSSTAT",
            "GV$PQ_TQSTAT",
            "GV$PROCESS",
            "GV$PROCESS_GROUP",
            "GV$PROCESS_MEMORY",
            "GV$PROCESS_MEMORY_DETAIL",
            "GV$PROCESS_MEMORY_DETAIL_PROG",
            "GV$PROPAGATION_RECEIVER",
            "GV$PROPAGATION_SENDER",
            "GV$PROXY_ARCHIVEDLOG",
            "GV$PROXY_DATAFILE",
            "GV$PWFILE_USERS",
            "GV$PX_BUFFER_ADVICE",
            "GV$PX_INSTANCE_GROUP",
            "GV$PX_PROCESS",
            "GV$PX_PROCESS_SYSSTAT",
            "GV$PX_SESSION",
            "GV$PX_SESSTAT",
            "GV$QMON_COORDINATOR_STATS",
            "GV$QMON_SERVER_STATS",
            "GV$QMON_TASKS",
            "GV$QMON_TASK_STATS",
            "GV$QUEUE",
            "GV$QUEUEING_MTH",
            "GV$RECOVERY_FILE_STATUS",
            "GV$RECOVERY_LOG",
            "GV$RECOVERY_PROGRESS",
            "GV$RECOVERY_STATUS",
            "GV$RECOVER_FILE",
            "GV$REDO_DEST_RESP_HISTOGRAM",
            "GV$REPLPROP",
            "GV$REPLQUEUE",
            "GV$REQDIST",
            "GV$RESERVED_WORDS",
            "GV$RESOURCE",
            "GV$RESOURCE_LIMIT",
            "GV$RESTORE_POINT",
            "GV$RESULT_CACHE_DEPENDENCY",
            "GV$RESULT_CACHE_MEMORY",
            "GV$RESULT_CACHE_OBJECTS",
            "GV$RESULT_CACHE_STATISTICS",
            "GV$RESUMABLE",
            "GV$RFS_THREAD",
            "GV$RMAN_COMPRESSION_ALGORITHM",
            "GV$RMAN_CONFIGURATION",
            "GV$RMAN_OUTPUT",
            "GV$ROLLSTAT",
            "GV$ROWCACHE",
            "GV$ROWCACHE_PARENT",
            "GV$ROWCACHE_SUBORDINATE",
            "GV$RSRCMGRMETRIC",
            "GV$RSRCMGRMETRIC_HISTORY",
            "GV$RSRC_CONSUMER_GROUP",
            "GV$RSRC_CONSUMER_GROUP_CPU_MTH",
            "GV$RSRC_CONS_GROUP_HISTORY",
            "GV$RSRC_PLAN",
            "GV$RSRC_PLAN_CPU_MTH",
            "GV$RSRC_PLAN_HISTORY",
            "GV$RSRC_SESSION_INFO",
            "GV$RULE",
            "GV$RULE_SET",
            "GV$RULE_SET_AGGREGATE_STATS",
            "GV$BACKUP",
            "GV$BACKUP_ASYNC_IO",
            "GV$BACKUP_CORRUPTION",
            "GV$BACKUP_DATAFILE",
            "GV$BACKUP_DEVICE",
            "GV$BACKUP_PIECE",
            "GV$BACKUP_REDOLOG",
            "GV$BACKUP_SET",
            "GV$BACKUP_SPFILE",
            "GV$BACKUP_SYNC_IO",
            "GV$BGPROCESS",
            "GV$BH",
            "GV$BLOCKING_QUIESCE",
            "GV$BSP",
            "GV$BUFFERED_PUBLISHERS",
            "GV$BUFFERED_QUEUES",
            "GV$BUFFERED_SUBSCRIBERS",
            "GV$BUFFER_POOL",
            "GV$BUFFER_POOL_STATISTICS",
            "GV$CALLTAG",
            "GV$CELL",
            "GV$CELL_CONFIG",
            "GV$CELL_REQUEST_TOTALS",
            "GV$CELL_STATE",
            "GV$CELL_THREAD_HISTORY",
            "GV$CIRCUIT",
            "GV$CLASS_CACHE_TRANSFER",
            "GV$CLASS_PING",
            "GV$CLIENT_STATS",
            "GV$CLUSTER_INTERCONNECTS",
            "GV$CONFIGURED_INTERCONNECTS",
            "GV$CONTEXT",
            "GV$CONTROLFILE",
            "GV$CONTROLFILE_RECORD_SECTION",
            "GV$COPY_CORRUPTION",
            "GV$CORRUPT_XID_LIST",
            "GV$CPOOL_CC_INFO",
            "GV$CPOOL_CC_STATS",
            "GV$CPOOL_CONN_INFO",
            "GV$CPOOL_STATS",
            "GV$CR_BLOCK_SERVER",
            "GV$CURRENT_BLOCK_SERVER",
            "GV$DATABASE",
            "GV$DATABASE_BLOCK_CORRUPTION",
            "GV$DATABASE_INCARNATION",
            "GV$DATAFILE",
            "GV$DATAFILE_COPY",
            "GV$DATAFILE_HEADER",
            "GV$DATAGUARD_CONFIG",
            "GV$DATAGUARD_STATS",
            "GV$DATAGUARD_STATUS",
            "GV$DATAPUMP_JOB",
            "GV$DATAPUMP_SESSION",
            "GV$DBFILE",
            "GV$DBLINK",
            "GV$DB_CACHE_ADVICE",
            "GV$DB_OBJECT_CACHE",
            "GV$DB_PIPES",
            "GV$DELETED_OBJECT",
            "GV$DETACHED_SESSION",
            "GV$DIAG_INFO",
            "GV$DISPATCHER",
            "GV$DISPATCHER_CONFIG",
            "GV$DISPATCHER_RATE",
            "GV$DLM_ALL_LOCKS",
            "GV$DLM_CONVERT_LOCAL",
            "GV$DLM_CONVERT_REMOTE",
            "GV$DLM_LATCH",
            "GV$DLM_LOCKS",
            "GV$DLM_MISC",
            "GV$DLM_RESS",
            "GV$DLM_TRAFFIC_CONTROLLER",
            "GV$DNFS_CHANNELS",
            "GV$DNFS_FILES",
            "GV$DNFS_SERVERS",
            "GV$DNFS_STATS",
            "GV$DYNAMIC_REMASTER_STATS",
            "GV$EMON",
            "GV$ENABLEDPRIVS",
            "GV$ENCRYPTED_TABLESPACES",
            "GV$ENCRYPTION_WALLET",
            "GV$ENQUEUE_LOCK",
            "GV$ENQUEUE_STAT",
            "GV$ENQUEUE_STATISTICS",
            "GV$EVENTMETRIC",
            "GV$EVENT_HISTOGRAM",
            "GV$EVENT_NAME",
            "GV$EXECUTION",
            "DBA_APPLY_OBJECT_DEPENDENCIES",
            "DBA_APPLY_VALUE_DEPENDENCIES",
            "V$ASH_INFO",
            "V$ASM_ACFSSNAPSHOTS",
            "V$ASM_ACFSVOLUMES",
            "V$ASM_ALIAS",
            "V$ASM_ATTRIBUTE",
            "V$ASM_CLIENT",
            "V$ASM_DISK",
            "V$ASM_DISKGROUP",
            "V$ASM_DISKGROUP_STAT",
            "V$ASM_DISK_IOSTAT",
            "V$ASM_DISK_STAT",
            "V$ASM_FILE",
            "V$ASM_FILESYSTEM",
            "V$ASM_OPERATION",
            "V$ASM_TEMPLATE",
            "V$ASM_USER",
            "V$ASM_USERGROUP",
            "V$ASM_USERGROUP_MEMBER",
            "V$ASM_VOLUME",
            "V$ASM_VOLUME_STAT",
            "V$AW_AGGREGATE_OP",
            "V$AW_ALLOCATE_OP",
            "V$AW_CALC",
            "V$AW_LONGOPS",
            "V$AW_OLAP",
            "V$AW_SESSION_INFO",
            "V$BACKUP",
            "V$BACKUP_ARCHIVELOG_DETAILS",
            "V$BACKUP_ARCHIVELOG_SUMMARY",
            "V$BACKUP_ASYNC_IO",
            "V$BACKUP_CONTROLFILE_DETAILS",
            "V$BACKUP_CONTROLFILE_SUMMARY",
            "V$BACKUP_COPY_DETAILS",
            "V$BACKUP_COPY_SUMMARY",
            "V$BACKUP_CORRUPTION",
            "V$BACKUP_DATAFILE",
            "V$BACKUP_DATAFILE_DETAILS",
            "V$BACKUP_DATAFILE_SUMMARY",
            "V$BACKUP_DEVICE",
            "V$BACKUP_FILES",
            "V$BACKUP_PIECE",
            "V$BACKUP_PIECE_DETAILS",
            "V$BACKUP_REDOLOG",
            "V$BACKUP_SET",
            "V$BACKUP_SET_DETAILS",
            "V$BACKUP_SET_SUMMARY",
            "V$BACKUP_SPFILE",
            "V$BACKUP_SPFILE_DETAILS",
            "V$BACKUP_SPFILE_SUMMARY",
            "V$BACKUP_SYNC_IO",
            "V$BGPROCESS",
            "V$BH",
            "V$BLOCKING_QUIESCE",
            "V$BLOCK_CHANGE_TRACKING",
            "V$BSP",
            "V$BUFFERED_PUBLISHERS",
            "V$BUFFERED_QUEUES",
            "V$BUFFERED_SUBSCRIBERS",
            "V$BUFFER_POOL",
            "V$BUFFER_POOL_STATISTICS",
            "V$CALLTAG",
            "V$CELL",
            "V$CELL_CONFIG",
            "V$CELL_REQUEST_TOTALS",
            "V$CELL_STATE",
            "V$CELL_THREAD_HISTORY",
            "V$CIRCUIT",
            "V$CLASS_CACHE_TRANSFER",
            "V$CLASS_PING",
            "V$CLIENT_STATS",
            "V$CLUSTER_INTERCONNECTS",
            "V$CONFIGURED_INTERCONNECTS",
            "V$CONTEXT",
            "V$CONTROLFILE",
            "V$CONTROLFILE_RECORD_SECTION",
            "V$COPY_CORRUPTION",
            "V$CORRUPT_XID_LIST",
            "V$CPOOL_CC_INFO",
            "V$CPOOL_CC_STATS",
            "V$CPOOL_CONN_INFO",
            "V$CPOOL_STATS",
            "V$CR_BLOCK_SERVER",
            "V$CURRENT_BLOCK_SERVER",
            "V$DATABASE",
            "V$DATABASE_BLOCK_CORRUPTION",
            "V$DATABASE_INCARNATION",
            "V$DATAFILE",
            "V$DATAFILE_COPY",
            "V$DATAFILE_HEADER",
            "V$DATAGUARD_CONFIG",
            "V$DATAGUARD_STATS",
            "V$DATAGUARD_STATUS",
            "V$DATAPUMP_JOB",
            "V$DATAPUMP_SESSION",
            "V$DBFILE",
            "V$DBLINK",
            "V$DB_CACHE_ADVICE",
            "V$DB_OBJECT_CACHE",
            "V$DB_PIPES",
            "V$DB_TRANSPORTABLE_PLATFORM",
            "V$DELETED_OBJECT",
            "V$DETACHED_SESSION",
            "ALL_OLAP2_AWS",
            "DBA_LOCKS",
            "DBA_HISTOGRAMS",
            "ALL_SUBSCRIBED_COLUMNS",
            "ALL_SUBSCRIBED_TABLES",
            "ALL_SUBSCRIPTIONS",
            "USER_SNAPSHOT_REFRESH_TIMES",
            "USER_SQLSET_DEFINITIONS",
            "V$DIAG_ADR_CONTROL",
            "V$DIAG_ADR_INVALIDATION",
            "V$DIAG_ALERT_EXT",
            "V$DIAG_AMS_XACTION",
            "V$DIAG_CRITICAL_ERROR",
            "V$DIAG_DDE_USER_ACTION",
            "V$DIAG_DDE_USER_ACTION_DEF",
            "V$DIAG_DDE_USR_ACT_PARAM",
            "V$DIAG_DDE_USR_ACT_PARAM_DEF",
            "V$DIAG_DDE_USR_INC_ACT_MAP",
            "V$DIAG_DDE_USR_INC_TYPE",
            "V$DIAG_DIAGV_INCIDENT",
            "V$DIAG_DIR_EXT",
            "V$DIAG_EM_DIAG_JOB",
            "V$DIAG_EM_TARGET_INFO",
            "V$DIAG_EM_USER_ACTIVITY",
            "V$DIAG_HM_FDG_SET",
            "V$DIAG_HM_FINDING",
            "V$DIAG_HM_INFO",
            "V$DIAG_HM_MESSAGE",
            "V$DIAG_HM_RECOMMENDATION",
            "V$DIAG_HM_RUN",
            "V$DIAG_INCCKEY",
            "V$DIAG_INCIDENT",
            "V$DIAG_INCIDENT_FILE",
            "V$DIAG_INC_METER_CONFIG",
            "V$DIAG_INC_METER_IMPT_DEF",
            "V$DIAG_INC_METER_INFO",
            "V$DIAG_INC_METER_PK_IMPTS",
            "V$DIAG_INC_METER_SUMMARY",
            "V$DIAG_INFO",
            "V$DIAG_IPS_CONFIGURATION",
            "V$DIAG_IPS_FILE_COPY_LOG",
            "V$DIAG_IPS_FILE_METADATA",
            "V$DIAG_IPS_PACKAGE",
            "V$DIAG_IPS_PACKAGE_FILE",
            "V$DIAG_IPS_PACKAGE_HISTORY",
            "V$DIAG_IPS_PACKAGE_INCIDENT",
            "V$DIAG_IPS_PKG_UNPACK_HIST",
            "V$DIAG_IPS_REMOTE_PACKAGE",
            "V$DIAG_PICKLEERR",
            "V$DIAG_PROBLEM",
            "V$DIAG_RELMD_EXT",
            "V$DIAG_SWEEPERR",
            "V$DIAG_VEM_USER_ACTLOG",
            "V$DIAG_VEM_USER_ACTLOG1",
            "V$DIAG_VHM_RUN",
            "V$DIAG_VIEW",
            "V$DIAG_VIEWCOL",
            "V$DIAG_VINCIDENT",
            "V$DIAG_VINCIDENT_FILE",
            "V$DIAG_VINC_METER_INFO",
            "V$DIAG_VIPS_FILE_COPY_LOG",
            "V$DIAG_VIPS_FILE_METADATA",
            "V$DIAG_VIPS_PACKAGE_FILE",
            "V$DIAG_VIPS_PACKAGE_HISTORY",
            "V$DIAG_VIPS_PACKAGE_MAIN_INT",
            "V$DIAG_VIPS_PACKAGE_SIZE",
            "V$DIAG_VIPS_PKG_FILE",
            "V$DIAG_VIPS_PKG_INC_CAND",
            "V$DIAG_VIPS_PKG_INC_DTL",
            "V$DIAG_VIPS_PKG_INC_DTL1",
            "V$DIAG_VIPS_PKG_MAIN_PROBLEM",
            "V$DIAG_VNOT_EXIST_INCIDENT",
            "V$DIAG_VPROBLEM",
            "V$DIAG_VPROBLEM1",
            "V$DIAG_VPROBLEM2",
            "V$DIAG_VPROBLEM_BUCKET",
            "V$DIAG_VPROBLEM_BUCKET1",
            "V$DIAG_VPROBLEM_BUCKET_COUNT",
            "V$DIAG_VPROBLEM_INT",
            "V$DIAG_VPROBLEM_LASTINC",
            "V$DIAG_VSHOWCATVIEW",
            "V$DIAG_VSHOWINCB",
            "V$DIAG_VSHOWINCB_I",
            "V$DIAG_VTEST_EXISTS",
            "V$DIAG_V_ACTINC",
            "V$DIAG_V_ACTPROB",
            "V$DIAG_V_INCCOUNT",
            "V$DIAG_V_INCFCOUNT",
            "V$DIAG_V_INC_METER_INFO_PROB",
            "V$DIAG_V_IPSPRBCNT",
            "V$DIAG_V_IPSPRBCNT1",
            "V$DIAG_V_NFCINC",
            "V$DIAG_V_SWPERRCOUNT",
            "V$DISPATCHER",
            "V$DISPATCHER_CONFIG",
            "V$DISPATCHER_RATE",
            "GV$ACCESS",
            "GV$ACTIVE_INSTANCES",
            "GV$ACTIVE_SERVICES",
            "GV$ACTIVE_SESSION_HISTORY",
            "GV$ACTIVE_SESS_POOL_MTH",
            "GV$ADVISOR_PROGRESS",
            "GV$ALERT_TYPES",
            "GV$AQ",
            "GV$AQ1",
            "GV$ARCHIVE",
            "GV$ARCHIVED_LOG",
            "GV$ARCHIVE_DEST",
            "GV$ARCHIVE_DEST_STATUS",
            "GV$ARCHIVE_GAP",
            "GV$ARCHIVE_PROCESSES",
            "GV$ASH_INFO",
            "GV$ASM_ACFSSNAPSHOTS",
            "GV$ASM_ACFSVOLUMES",
            "GV$ASM_ALIAS",
            "GV$ASM_ATTRIBUTE",
            "GV$ASM_CLIENT",
            "GV$ASM_DISK",
            "GV$ASM_DISKGROUP",
            "GV$ASM_DISKGROUP_STAT",
            "GV$ASM_DISK_IOSTAT",
            "GV$ASM_DISK_STAT",
            "GV$ASM_FILE",
            "GV$ASM_FILESYSTEM",
            "GV$ASM_OPERATION",
            "GV$ASM_TEMPLATE",
            "GV$ASM_USER",
            "GV$ASM_USERGROUP",
            "GV$ASM_USERGROUP_MEMBER",
            "GV$ASM_VOLUME",
            "GV$ASM_VOLUME_STAT",
            "GV$AW_AGGREGATE_OP",
            "GV$AW_ALLOCATE_OP",
            "GV$AW_CALC",
            "GV$AW_LONGOPS",
            "GV$AW_OLAP",
            "GV$AW_SESSION_INFO",
            "USER_HISTOGRAMS",
            "DICT",
            "GV$SYSAUX_OCCUPANTS",
            "GV$SYSMETRIC",
            "GV$SYSMETRIC_HISTORY",
            "GV$SYSMETRIC_SUMMARY",
            "GV$SYSSTAT",
            "GV$SYSTEM_CURSOR_CACHE",
            "GV$SYSTEM_EVENT",
            "GV$SYSTEM_PARAMETER",
            "GV$SYSTEM_PARAMETER2",
            "GV$SYSTEM_WAIT_CLASS",
            "GV$SYS_OPTIMIZER_ENV",
            "GV$SYS_TIME_MODEL",
            "GV$TABLESPACE",
            "GV$TEMPFILE",
            "GV$TEMPORARY_LOBS",
            "GV$TEMPSEG_USAGE",
            "GV$TEMPSTAT",
            "GV$TEMP_CACHE_TRANSFER",
            "GV$TEMP_EXTENT_MAP",
            "GV$TEMP_EXTENT_POOL",
            "GV$TEMP_PING",
            "GV$TEMP_SPACE_HEADER",
            "GV$THREAD",
            "GV$THRESHOLD_TYPES",
            "GV$TIMER",
            "GV$TIMEZONE_FILE",
            "GV$TIMEZONE_NAMES",
            "GV$TOPLEVELCALL",
            "GV$TRANSACTION",
            "GV$TRANSACTION_ENQUEUE",
            "GV$TSM_SESSIONS",
            "GV$TYPE_SIZE",
            "GV$UNDOSTAT",
            "GV$VERSION",
            "GV$VPD_POLICY",
            "GV$WAITCLASSMETRIC",
            "GV$WAITCLASSMETRIC_HISTORY",
            "GV$WAITSTAT",
            "GV$WALLET",
            "GV$WLM_PCMETRIC",
            "GV$WLM_PCMETRIC_HISTORY",
            "GV$WLM_PC_STATS",
            "GV$WORKLOAD_REPLAY_THREAD",
            "GV$XML_AUDIT_TRAIL",
            "GV$_LOCK",
            "V$SCHEDULER_RUNNING_JOBS",
            "V$SECUREFILE_TIMER",
            "V$SEGMENT_STATISTICS",
            "V$SEGSTAT",
            "V$SEGSTAT_NAME",
            "V$SERVICEMETRIC",
            "V$SERVICEMETRIC_HISTORY",
            "V$SERVICES",
            "V$SERVICE_EVENT",
            "V$SERVICE_STATS",
            "V$SERVICE_WAIT_CLASS",
            "V$SERV_MOD_ACT_STATS",
            "V$SESSION",
            "V$SESSION_BLOCKERS",
            "V$SESSION_CONNECT_INFO",
            "V$SESSION_CURSOR_CACHE",
            "V$SESSION_EVENT",
            "V$SESSION_FIX_CONTROL",
            "V$SESSION_LONGOPS",
            "V$SESSION_OBJECT_CACHE",
            "V$SESSION_WAIT",
            "V$SESSION_WAIT_CLASS",
            "V$SESSION_WAIT_HISTORY",
            "V$SESSMETRIC",
            "V$SESSTAT",
            "V$SESS_IO",
            "V$SESS_TIME_MODEL",
            "V$SES_OPTIMIZER_ENV",
            "V$SGA",
            "V$SGAINFO",
            "V$SGASTAT",
            "V$SGA_CURRENT_RESIZE_OPS",
            "V$SGA_DYNAMIC_COMPONENTS",
            "V$SGA_DYNAMIC_FREE_MEMORY",
            "V$SGA_RESIZE_OPS",
            "V$SGA_TARGET_ADVICE",
            "V$SHARED_POOL_ADVICE",
            "V$SHARED_POOL_RESERVED",
            "V$SHARED_SERVER",
            "V$SHARED_SERVER_MONITOR",
            "V$SORT_SEGMENT",
            "V$SORT_USAGE",
            "V$SPPARAMETER",
            "V$SQL",
            "V$SQLAREA",
            "V$SQLAREA_PLAN_HASH",
            "V$SQLCOMMAND",
            "V$SQLFN_ARG_METADATA",
            "V$SQLFN_METADATA",
            "V$SQLPA_METRIC",
            "V$SQLSTATS",
            "V$SQLSTATS_PLAN_HASH",
            "V$SQLTEXT",
            "V$SQLTEXT_WITH_NEWLINES",
            "V$SQL_BIND_CAPTURE",
            "V$SQL_BIND_DATA",
            "V$SQL_BIND_METADATA",
            "V$SQL_CS_HISTOGRAM",
            "V$SQL_CS_SELECTIVITY",
            "V$SQL_CS_STATISTICS",
            "V$SQL_CURSOR",
            "V$SQL_FEATURE",
            "V$SQL_FEATURE_DEPENDENCY",
            "V$SQL_FEATURE_HIERARCHY",
            "V$SQL_HINT",
            "V$SQL_JOIN_FILTER",
            "V$SQL_MONITOR",
            "V$SQL_OPTIMIZER_ENV",
            "V$SQL_PLAN",
            "V$SQL_PLAN_MONITOR",
            "V$SQL_PLAN_STATISTICS",
            "V$SQL_PLAN_STATISTICS_ALL",
            "V$SQL_REDIRECTION",
            "V$SQL_SHARED_CURSOR",
            "V$SQL_SHARED_MEMORY",
            "V$SQL_WORKAREA",
            "V$SQL_WORKAREA_ACTIVE",
            "V$SQL_WORKAREA_HISTOGRAM",
            "V$SSCR_SESSIONS",
            "V$STANDBY_EVENT_HISTOGRAM",
            "V$STANDBY_LOG",
            "V$STATISTICS_LEVEL",
            "V$STATNAME",
            "V$STREAMS_APPLY_COORDINATOR",
            "V$STREAMS_APPLY_READER",
            "V$STREAMS_APPLY_SERVER",
            "V$STREAMS_CAPTURE",
            "V$STREAMS_MESSAGE_TRACKING",
            "V$STREAMS_POOL_ADVICE",
            "V$STREAMS_POOL_STATISTICS",
            "V$STREAMS_TRANSACTION",
            "V$SUBCACHE",
            "V$SUBSCR_REGISTRATION_STATS",
            "DBA_SNAPSHOT_LOG_FILTER_COLS",
            "DBA_SNAPSHOT_REFRESH_TIMES",
            "DBA_SQLSET_DEFINITIONS",
            "SYN",
            "TABS",
            "ALL_SNAPSHOT_REFRESH_TIMES",
            "ALL_SOURCE_TABLES",
            "ALL_HISTOGRAMS",
            "ALL_JOBS",
            "LOGSTDBY_UNSUPPORTED_TABLES",
            "CAT",
            "CHANGE_PROPAGATIONS",
            "CHANGE_PROPAGATION_SETS",
            "CHANGE_SETS",
            "CHANGE_SOURCES",
            "CHANGE_TABLES",
            "CLIENT_RESULT_CACHE_STATS$",
            "CLU",
            "COLS",
            "RECYCLEBIN",
            "V$ACCESS",
            "V$ACTIVE_INSTANCES",
            "V$ACTIVE_SERVICES",
            "V$ACTIVE_SESSION_HISTORY",
            "V$ACTIVE_SESS_POOL_MTH",
            "V$ADVISOR_PROGRESS",
            "V$ALERT_TYPES",
            "V$AQ",
            "V$AQ1",
            "V$ARCHIVE",
            "V$ARCHIVED_LOG",
            "V$ARCHIVE_DEST",
            "V$ARCHIVE_DEST_STATUS",
            "V$ARCHIVE_GAP",
            "V$ARCHIVE_PROCESSES",
            "SEQ",
            "SM$VERSION",
            "IND",
            "ALL_OUTLINES",
            "ALL_OUTLINE_HINTS",
            "ALL_PUBLISHED_COLUMNS",
            "OBJ"};
}

package com.dc.summer.parser.sql;

import com.dc.summer.parser.sql.exception.SQLParserException;
import com.dc.summer.parser.sql.model.SQLParser;
import com.dc.type.DatabaseType;
import com.dc.utils.io.FindClassUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public abstract class SQLParserBuilder {

    private static final List<SQLParserBuilder> SQL_PARSER_BUILDER_LIST;

    protected SQLParserBuilder() {
    }

    static {
        SQL_PARSER_BUILDER_LIST = FindClassUtils
                .getAbstractClass(SQLParserBuilder.class, SQLParserBuilder.class.getPackage().getName())
                .stream()
                // first use GSP
                .sorted(Comparator.comparing(Class::getSimpleName))
                .map(achieveClass -> {
                    try {
                        return achieveClass.getDeclaredConstructor().newInstance();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList());
    }

    public static SQLParser build(String sql, DatabaseType databaseType) {
        for (SQLParserBuilder sqlParserBuilder : SQL_PARSER_BUILDER_LIST) {
            SQLParser sqlParser = null;
            try {
                sqlParser = sqlParserBuilder.create(sql, databaseType);
            } catch (SQLParserException e) {
                log.error("解析失败，尝试使用其他解析器.", e);
                continue;
            }
            if (sqlParser != null) {
                return sqlParser;
            }
        }
        return null;
    }

    protected SQLParser create(String sql, DatabaseType databaseType) throws SQLParserException {
        return null;
    }

}

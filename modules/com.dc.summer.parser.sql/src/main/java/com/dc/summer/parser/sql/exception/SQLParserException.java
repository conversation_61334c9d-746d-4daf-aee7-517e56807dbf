package com.dc.summer.parser.sql.exception;

public class SQLParserException extends Exception {
    public SQLParserException() {
    }

    public SQLParserException(String message) {
        super(message);
    }

    public SQLParserException(String message, Throwable cause) {
        super(message, cause);
    }

    public SQLParserException(Throwable cause) {
        super(cause);
    }

    public SQLParserException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}

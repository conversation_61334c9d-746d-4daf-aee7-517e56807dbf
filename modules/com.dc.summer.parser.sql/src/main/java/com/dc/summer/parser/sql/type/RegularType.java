package com.dc.summer.parser.sql.type;

public enum RegularType {

    PARTITION(1, "PARTITION"),
    STORAGE_DB(2, "storagedb"),
    OVERWRITE(3, "OVERWRITE"),
    DISTRIBUTE_BY_HASH(4, "DISTRIBUTE BY HASH"),
    MERGE_INTO_WHERE(5, "MERGE INTO WHERE"),
    DOUBLE_COLON(6, "::"),
    OFFSET(7, "OFFSET"),
    STRUCT(8, "STRUCT"),
    OVERWRITE_TO_INTO(9, "OVERWRITE转换为INTO"),
    FETCH_FIRST_ROW_WITH_TIES(10, "FETCH FIRST ROW WITH TIES"),
    PARTITIONS(11, "PARTITIONS"),
    LIMIT(12, "LIMIT"),
    ROW_ARCHIVAL(13, "ROW ARCHIVAL"),
    EXCEPT(14, "EXCEPT"),
    MINUS(15, "MINUS"),
    START_TRANSACTION(16, "移除 Procedure 中的 START TRANSACTION;"),
    REMOVE_SQL_LINE_COMMENTS(17, "移除标准的单行注释符号"),
    TDPG_EXECUTE_DIRECT_ON_DN(18, "TDPG的EXECUTE DIRECT ON (dn001) 'select语句'"),
    DORIS_REMOVE_PROPERTIES_OP(19, "doris数据库移除properties option"),
    DORIS_DELETE_REMOVE_PARTITION(20, "doris数据库delete移除partition"),
    DORIS_SELECT_REMOVE_ALLEXCEPT(21, "doris数据库select移除 all except()"),

    OTHER(999, "OTHER"),

    ;

    private final Integer value;
    private final String name;

    RegularType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}

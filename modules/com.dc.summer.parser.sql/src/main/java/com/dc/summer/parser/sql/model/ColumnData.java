package com.dc.summer.parser.sql.model;

import lombok.Data;
import lombok.ToString;

import java.util.Objects;

@Data
@ToString
public class ColumnData {

    private String catalogName;

    private String schemaName;

    private String tableName;

    private String columnName;

    private String columnAlias;

    private boolean withinFunc; // 是否是表达式

    private boolean constant; // 是否是常量

    private boolean canDesensitize = true; // 是否能脱敏

    private String columnComment; // 字段注释

    private ColumnData columnDataInner; // 同名字段来自不同表，查一下字段注释

    private boolean isTable;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ColumnData that = (ColumnData) o;
        return withinFunc == that.withinFunc && constant == that.constant && canDesensitize == that.canDesensitize && Objects.equals(catalogName, that.catalogName) && Objects.equals(schemaName, that.schemaName) && Objects.equals(tableName, that.tableName) && Objects.equals(columnName, that.columnName) && Objects.equals(columnAlias, that.columnAlias);
    }

    @Override
    public int hashCode() {
        return Objects.hash(catalogName, schemaName, tableName, columnName, columnAlias, withinFunc, constant, canDesensitize);
    }
}

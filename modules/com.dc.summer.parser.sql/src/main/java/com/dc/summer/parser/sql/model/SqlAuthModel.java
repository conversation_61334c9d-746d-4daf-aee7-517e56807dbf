package com.dc.summer.parser.sql.model;

import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.type.DatabaseType;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
@NoArgsConstructor
@AllArgsConstructor
public class SqlAuthModel {

    private String catalogName; // 3层数据库的databaseName，如pg系列的最顶层name
    private String catalogUniqueKey;
    private String schemaName; // 2层数据库的databaseName，如mysql系列的最顶层name；3层数据库的schemaName(如pg系列的中间层name)或databaseName(如sqlserver的最顶层name)
    private String schemaUniqueKey;
    private String frameworkName; // 3层数据库的frameworkName，如sqlserver的中间层name
    private String name; // 表名或对象名，最底层name

    private String operation; // 操作类型，大写
    private String ddlSubdivideOperation; // ddl操作细分
    private Integer dbType; // 数据库类型
    private String type; // 类型 如 table、view、function ...
    private boolean isSys; // 是否在系统schema
    private String charset; // schema字符集
    private boolean isTable; // 是不是表
    private boolean hasOperationAuth; // 有操作权限
    private boolean hasFunctionAuth; // 有函数权限
    private boolean hasExportAuth; // 有导出权限
    private boolean hasCountTotalAuth; // 有查询总行数权限
    private boolean canBackup; // 是可以备份的表
    private boolean falseSchema; // 对象所属schema查询不到，使用的窗口默认schema

    private String dblinkName; // 跨库查询的dblink的名字

    private String originSchemaName; // 不去除引号的schema名字
    private String originName; // 不去除引号的名字
    private String newName;  // rename 的新表名，更新私有表用

    private boolean isNeedChangeMessageForPT; //需要改变提示信息，为了私有表需求
    private String[] split; // 内部值是原始的拼接字符串，如果需要使用函数的真实表名，使用 staFuncForObjectName。

    private String authSchemaNameForPackage; //授权时，对象(包、类)对应的schema名称
    private String authSchemaIdForPackage; //需要上面对应的id。

    private String staFuncForObjectName;

    private boolean isBuiltinFunction;

    private boolean customExportOperation;

    public String getCatalogName() {
        return catalogName;
    }

    public void setCatalogName(String catalogName) {
        this.catalogName = catalogName;
    }

    public String getCatalogUniqueKey() {
        return catalogUniqueKey;
    }

    public void setCatalogUniqueKey(String catalogUniqueKey) {
        this.catalogUniqueKey = catalogUniqueKey;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public String getFullSchemaName() {
        if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType) && StringUtils.isNotBlank(catalogName)) {
            return catalogName + "." + schemaName;
        }
        return schemaName;
    }

    public String getSchemaNameForNoAuthMessage() {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(dblinkName)) {
            sb.append(dblinkName).append(".");
        }
        if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType) && StringUtils.isNotBlank(catalogName)) {
            sb.append(catalogName).append(".");
        }
        sb.append(schemaName);
        return sb.toString();
    }

    public String getOldSchemaNameForDataMask() {
        if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType) && StringUtils.isNotBlank(catalogName)) {
            return catalogName;
        } else {
            return schemaName;
        }
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    public String getSchemaUniqueKey() {
        return schemaUniqueKey;
    }

    public void setSchemaUniqueKey(String schemaUniqueKey) {
        this.schemaUniqueKey = schemaUniqueKey;
    }

    public String getFrameworkName() {
        return frameworkName;
    }

    public void setFrameworkName(String frameworkName) {
        this.frameworkName = frameworkName;
    }

    public String getName() {
        return name;
    }

    public String getFullName() {
        if (DatabaseType.SQL_SERVER.getValue().equals(dbType) && StringUtils.isNoneBlank(frameworkName, name)) {
            return frameworkName + "." + name;
        }
        return name;
    }

    public String getOldNameForDataMask() {
        if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType) && StringUtils.isNotBlank(schemaName)) {
            return schemaName + "." + name;
        } else if (DatabaseType.SQL_SERVER.getValue().equals(dbType) && StringUtils.isNotBlank(frameworkName)) {
            return frameworkName + "." + name;
        } else {
            return name;
        }
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getDdlSubdivideOperation() {
        return ddlSubdivideOperation;
    }

    public void setDdlSubdivideOperation(String ddlSubdivideOperation) {
        this.ddlSubdivideOperation = ddlSubdivideOperation;
    }

    public Integer getDbType() {
        return dbType;
    }

    public void setDbType(Integer dbType) {
        this.dbType = dbType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isSys() {
        return isSys;
    }

    public void setSys(boolean sys) {
        isSys = sys;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public boolean isTable() {
        return isTable;
    }

    public void setTable(boolean table) {
        isTable = table;
    }

    public boolean isHasOperationAuth() {
        return hasOperationAuth;
    }

    public void setHasOperationAuth(boolean hasOperationAuth) {
        this.hasOperationAuth = hasOperationAuth;
    }

    public boolean isHasFunctionAuth() {
        return hasFunctionAuth;
    }

    public void setHasFunctionAuth(boolean hasFunctionAuth) {
        this.hasFunctionAuth = hasFunctionAuth;
    }

    public boolean isHasExportAuth() {
        return hasExportAuth;
    }

    public void setHasExportAuth(boolean hasExportAuth) {
        this.hasExportAuth = hasExportAuth;
    }

    public boolean isHasCountTotalAuth() {
        return hasCountTotalAuth;
    }

    public void setHasCountTotalAuth(boolean hasCountTotalAuth) {
        this.hasCountTotalAuth = hasCountTotalAuth;
    }

    public boolean isCanBackup() {
        return canBackup;
    }

    public void setCanBackup(boolean canBackup) {
        this.canBackup = canBackup;
    }

    public boolean isFalseSchema() {
        return falseSchema;
    }

    public void setFalseSchema(boolean falseSchema) {
        this.falseSchema = falseSchema;
    }

    public String getDblinkName() {
        return dblinkName;
    }

    public void setDblinkName(String dblinkName) {
        this.dblinkName = dblinkName;
    }

    public String getOriginSchemaName() {
        return originSchemaName;
    }

    public void setOriginSchemaName(String originSchemaName) {
        this.originSchemaName = originSchemaName;
    }

    public String getOriginName() {
        return originName;
    }

    public void setOriginName(String originName) {
        this.originName = originName;
    }

    public String getNewName() {
        return newName;
    }

    public SqlAuthModel setNewName(String newName) {
        this.newName = newName;
        return this;
    }

    public boolean isNeedChangeMessageForPT() {
        return isNeedChangeMessageForPT;
    }

    public void setNeedChangeMessageForPT(boolean needChangeMessageForPT) {
        isNeedChangeMessageForPT = needChangeMessageForPT;
    }

    public String[] getSplit(){
        return split;
    }

    public void setSplit(String[] split){
        this.split = split;
    }

    public String getAuthSchemaNameForPackage() {
        return authSchemaNameForPackage;
    }

    public void setAuthSchemaNameForPackage(String authSchemaNameForPackage) {
        this.authSchemaNameForPackage = authSchemaNameForPackage;
    }

    public String getAuthSchemaIdForPackage() {
        return authSchemaIdForPackage;
    }

    public void setAuthSchemaIdForPackage(String authSchemaIdForPackage){
        this.authSchemaIdForPackage = authSchemaIdForPackage;
    }

    public String getStaFuncForObjectName() {
        return staFuncForObjectName;
    }

    public void setStaFuncForObjectName(String staFuncForObjectName) {
        this.staFuncForObjectName = staFuncForObjectName;
    }

    public boolean isBuiltinFunction() {
        return isBuiltinFunction;
    }

    public void setBuiltinFunction(boolean builtinFunction) {
        isBuiltinFunction = builtinFunction;
    }

    public boolean isCustomExportOperation() {
        return customExportOperation;
    }

    public void setCustomExportOperation(boolean customExportOperation) {
        this.customExportOperation = customExportOperation;
    }

    public static SqlAuthModel ofFuncInStatistics(String catalogName, String catalogUniqueKey, String schemaName, String schemaUniqueKey, String frameworkName, String name,
                                                  Integer dbType, String charset, String[] split, String staFuncForObjectName) {

        SqlAuthModel sqlAuthModel = new SqlAuthModel(); //生产一个 统计函数 的sqlAuthModel

        sqlAuthModel.setCatalogName(catalogName);
        sqlAuthModel.setCatalogUniqueKey(catalogUniqueKey);
        sqlAuthModel.setSchemaName(schemaName);
        sqlAuthModel.setSchemaUniqueKey(schemaUniqueKey);
        sqlAuthModel.setFrameworkName(frameworkName);
        sqlAuthModel.setName(name);
        sqlAuthModel.setOperation(SqlConstant.STATISTICS);
        sqlAuthModel.setDdlSubdivideOperation("");
        sqlAuthModel.setDbType(dbType);
        sqlAuthModel.setType(SqlConstant.FUNCTION);
        sqlAuthModel.setCharset(charset);
        sqlAuthModel.setSplit(split); //方法参数暂未用到，而是自己手动构造 (又恢复了)
        sqlAuthModel.setStaFuncForObjectName(staFuncForObjectName);

        return sqlAuthModel;
    }

    public static SqlAuthModel ofEmptyTableForSelect(String catalogName, String catalogUniqueKey, String schemaName, String schemaUniqueKey, String frameworkName,
                                                     Integer dbType, String charset, String[] split) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setCatalogName(catalogName);
        sqlAuthModel.setCatalogUniqueKey(catalogUniqueKey);
        sqlAuthModel.setSchemaName(schemaName);
        sqlAuthModel.setSchemaUniqueKey(schemaUniqueKey);
        sqlAuthModel.setFrameworkName(frameworkName);
        sqlAuthModel.setName("");
        sqlAuthModel.setOperation(SqlConstant.KEY_SELECT);
        sqlAuthModel.setDdlSubdivideOperation("");
        sqlAuthModel.setDbType(dbType);
        sqlAuthModel.setType(SqlConstant.KEY_TABLE);
        sqlAuthModel.setCharset(charset);
        sqlAuthModel.setSplit(split);
        sqlAuthModel.setTable(true);

        return sqlAuthModel;
    }

    public String getRealObjectName() {
        return SqlConstant.STATISTICS.equalsIgnoreCase(operation) ? staFuncForObjectName : getFullName();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SqlAuthModel that = (SqlAuthModel) o;
        return isSys == that.isSys && isTable == that.isTable && hasOperationAuth == that.hasOperationAuth && hasFunctionAuth == that.hasFunctionAuth && hasExportAuth == that.hasExportAuth && hasCountTotalAuth == that.hasCountTotalAuth && canBackup == that.canBackup && falseSchema == that.falseSchema && Objects.equals(catalogName, that.catalogName) && Objects.equals(catalogUniqueKey, that.catalogUniqueKey) && Objects.equals(schemaName, that.schemaName) && Objects.equals(schemaUniqueKey, that.schemaUniqueKey) && Objects.equals(frameworkName, that.frameworkName) && Objects.equals(name, that.name) && Objects.equals(operation, that.operation) && Objects.equals(ddlSubdivideOperation, that.ddlSubdivideOperation) && Objects.equals(dbType, that.dbType) && Objects.equals(type, that.type) && Objects.equals(charset, that.charset);
    }

    @Override
    public int hashCode() {
        return Objects.hash(catalogName, catalogUniqueKey, schemaName, schemaUniqueKey, frameworkName, name, operation, ddlSubdivideOperation, dbType, type, isSys, charset, isTable, hasOperationAuth, hasFunctionAuth, hasExportAuth, hasCountTotalAuth, canBackup, falseSchema);
    }
}

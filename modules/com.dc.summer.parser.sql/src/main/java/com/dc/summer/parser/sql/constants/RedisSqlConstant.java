package com.dc.summer.parser.sql.constants;


public class RedisSqlConstant {

    // type
    public static final String KEY_TABLE_OR_VIEW = "TABLE_OR_VIEW";

    // keyword

    // symbol

    // operation
    public static final String KEY_APPEND = "APPEND";
    public static final String KEY_ASKING = "ASKING";
    public static final String KEY_AUTH = "AUTH";
    public static final String KEY_BGREWRITEAOF = "BGREWRITEAOF";
    public static final String KEY_BGSAVE = "BGSAVE";
    public static final String KEY_BITCOUNT = "BITCOUNT";
    public static final String KEY_BITFIELD = "BITFIELD";
    public static final String KEY_BLMOVE = "BLMOVE";
    public static final String KEY_BLPOP = "BLPOP";
    public static final String KEY_BRPOP = "BRPOP";
    public static final String KEY_BRPOPLPUSH = "BRPOPLPUSH";
    public static final String KEY_BZPOPMAX = "BZPOPMAX";
    public static final String KEY_BZPOPMIN = "BZPOPMIN";
    public static final String KEY_COMMAND = "COMMAND";
    public static final String KEY_COMMAND_COUNT = "COMMAND_COUNT";
    public static final String KEY_COMMAND_GETKEYS = "COMMAND_GETKEYS";
    public static final String KEY_COMMAND_INFO = "COMMAND_INFO";
    public static final String KEY_COMMAND_LIST = "COMMAND_LIST";
    public static final String KEY_CONFIG_GET = "CONFIG_GET";
    public static final String KEY_CONFIG_RESETSTAT = "CONFIG_RESETSTAT";
    public static final String KEY_CONFIG_REWRITE = "CONFIG_REWRITE";
    public static final String KEY_CONFIG_SET = "CONFIG_SET";
    public static final String KEY_COPY = "COPY";
    public static final String KEY_DBSIZE = "DBSIZE";
    public static final String KEY_DECR = "DECR";
    public static final String KEY_DECRBY = "DECRBY";
    public static final String KEY_DEL = "DEL";
    public static final String KEY_DISCARD = "DISCARD";
    public static final String KEY_DUMP = "DUMP";
    public static final String KEY_ECHO = "ECHO";
    public static final String KEY_EVAL = "EVAL";
    public static final String KEY_EXEC = "EXEC";
    public static final String KEY_EXISTS = "EXISTS";
    public static final String KEY_EXPIRE = "EXPIRE";
    public static final String KEY_EXPIREAT = "EXPIREAT";
    public static final String KEY_EXPIRETIME = "EXPIRETIME";
    public static final String KEY_FAILOVER = "FAILOVER";
    public static final String KEY_FLUSHALL = "FLUSHALL";
    public static final String KEY_FLUSHDB = "FLUSHDB";
    public static final String KEY_GET = "GET";
    public static final String KEY_GETDEL = "GETDEL";
    public static final String KEY_GETEX = "GETEX";
    public static final String KEY_GETRANGE = "GETRANGE";
    public static final String KEY_GETSET = "GETSET";
    public static final String KEY_HDEL = "HDEL";
    public static final String KEY_HEXISTS = "HEXISTS";
    public static final String KEY_HGET = "HGET";
    public static final String KEY_HGETALL = "HGETALL";
    public static final String KEY_HINCRBY = "HINCRBY";
    public static final String KEY_HINCRBYFLOAT = "HINCRBYFLOAT";
    public static final String KEY_HKEYS = "HKEYS";
    public static final String KEY_HLEN = "HLEN";
    public static final String KEY_HMGET = "HMGET";
    public static final String KEY_HMSET = "HMSET";
    public static final String KEY_HRANDFIELD = "HRANDFIELD";
    public static final String KEY_HSCAN = "HSCAN";
    public static final String KEY_HSET = "HSET";
    public static final String KEY_HSETNX = "HSETNX";
    public static final String KEY_HSTRLEN = "HSTRLEN";
    public static final String KEY_HVALS = "HVALS";
    public static final String KEY_INCR = "INCR";
    public static final String KEY_INCRBY = "INCRBY";
    public static final String KEY_INCRBYFLOAT = "INCRBYFLOAT";
    public static final String KEY_INFO = "INFO";
    public static final String KEY_KEYS = "KEYS";
    public static final String KEY_LCS = "LCS";
    public static final String KEY_LINDEX = "LINDEX";
    public static final String KEY_LINSERT = "LINSERT";
    public static final String KEY_LLEN = "LLEN";
    public static final String KEY_LMOVE = "LMOVE";
    public static final String KEY_LMPOP = "LMPOP";
    public static final String KEY_LPOP = "LPOP";
    public static final String KEY_LPOS = "LPOS";
    public static final String KEY_LPUSH = "LPUSH";
    public static final String KEY_LPUSHX = "LPUSHX";
    public static final String KEY_LRANGE = "LRANGE";
    public static final String KEY_LREM = "LREM";
    public static final String KEY_LSET = "LSET";
    public static final String KEY_LTRIM = "LTRIM";
    public static final String KEY_MGET = "MGET";
    public static final String KEY_MIGRATE = "MIGRATE";
    public static final String KEY_MOVE = "MOVE";
    public static final String KEY_MSET = "MSET";
    public static final String KEY_MSETNX = "MSETNX";
    public static final String KEY_OBJECT = "OBJECT";
    public static final String KEY_PERSIST = "PERSIST";
    public static final String KEY_PEXPIRE = "PEXPIRE";
    public static final String KEY_PEXPIREAT = "PEXPIREAT";
    public static final String KEY_PEXPIRETIME = "PEXPIRETIME";
    public static final String KEY_PING = "PING";
    public static final String KEY_PSETEX = "PSETEX";
    public static final String KEY_PTTL = "PTTL";
    public static final String KEY_PUBLISH = "PUBLISH";
    public static final String KEY_QUIT = "QUIT";
    public static final String KEY_RANDOMKEY = "RANDOMKEY";
    public static final String KEY_READONLY = "READONLY";
    public static final String KEY_READWRITE = "READWRITE";
    public static final String KEY_RENAME = "RENAME";
    public static final String KEY_RENAMENX = "RENAMENX";
    public static final String KEY_RESET = "RESET";
    public static final String KEY_RESTORE = "RESTORE";
    public static final String KEY_RPOP = "RPOP";
    public static final String KEY_RPOPLPUSH = "RPOPLPUSH";
    public static final String KEY_RPUSH = "RPUSH";
    public static final String KEY_RPUSHX = "RPUSHX";
    public static final String KEY_SADD = "SADD";
    public static final String KEY_SAVE = "SAVE";
    public static final String KEY_SCAN = "SCAN";
    public static final String KEY_SCARD = "SCARD";
    public static final String KEY_SDIFF = "SDIFF";
    public static final String KEY_SDIFFSTORE = "SDIFFSTORE";
    public static final String KEY_SELECT = "SELECT";
    public static final String KEY_SET = "SET";
    public static final String KEY_SETEX = "SETEX";
    public static final String KEY_SETNX = "SETNX";
    public static final String KEY_SETRANGE = "SETRANGE";
    public static final String KEY_SHUTDOWN = "SHUTDOWN";
    public static final String KEY_SINTER = "SINTER";
    public static final String KEY_SINTERCARD = "SINTERCARD";
    public static final String KEY_SINTERSTORE = "SINTERSTORE";
    public static final String KEY_SISMEMBER = "SISMEMBER";
    public static final String KEY_SMEMBERS = "SMEMBERS";
    public static final String KEY_SMISMEMBER = "SMISMEMBER";
    public static final String KEY_SMOVE = "SMOVE";
    public static final String KEY_SORT = "SORT";
    public static final String KEY_SORT_RO = "SORT_RO";
    public static final String KEY_SPOP = "SPOP";
    public static final String KEY_SRANDMEMBER = "SRANDMEMBER";
    public static final String KEY_SREM = "SREM";
    public static final String KEY_SSCAN = "SSCAN";
    public static final String KEY_STRLEN = "STRLEN";
    public static final String KEY_SUBSCRIBE = "SUBSCRIBE";
    public static final String KEY_SUBSTR = "SUBSTR";
    public static final String KEY_SUNION = "SUNION";
    public static final String KEY_SUNIONSTORE = "SUNIONSTORE";
    public static final String KEY_SYNC = "SYNC";
    public static final String KEY_TIME = "TIME";
    public static final String KEY_TOUCH = "TOUCH";
    public static final String KEY_TTL = "TTL";
    public static final String KEY_TYPE = "TYPE";
    public static final String KEY_UNLINK = "UNLINK";
    public static final String KEY_WAIT = "WAIT";
    public static final String KEY_WAITAOF = "WAITAOF";
    public static final String KEY_ZADD = "ZADD";
    public static final String KEY_ZCARD = "ZCARD";
    public static final String KEY_ZCOUNT = "ZCOUNT";
    public static final String KEY_ZDIFF = "ZDIFF";
    public static final String KEY_ZDIFFSTORE = "ZDIFFSTORE";
    public static final String KEY_ZINCRBY = "ZINCRBY";
    public static final String KEY_ZINTER = "ZINTER";
    public static final String KEY_ZINTERCARD = "ZINTERCARD";
    public static final String KEY_ZINTERSTORE = "ZINTERSTORE";
    public static final String KEY_ZLEXCOUNT = "ZLEXCOUNT";
    public static final String KEY_ZMPOP = "ZMPOP";
    public static final String KEY_ZMSCORE = "ZMSCORE";
    public static final String KEY_ZPOPMAX = "ZPOPMAX";
    public static final String KEY_ZPOPMIN = "ZPOPMIN";
    public static final String KEY_ZRANDMEMBER = "ZRANDMEMBER";
    public static final String KEY_ZRANGE = "ZRANGE";
    public static final String KEY_ZRANGEBYLEX = "ZRANGEBYLEX";
    public static final String KEY_ZRANGEBYSCORE = "ZRANGEBYSCORE";
    public static final String KEY_ZRANGESTORE = "ZRANGESTORE";
    public static final String KEY_ZRANK = "ZRANK";
    public static final String KEY_ZREM = "ZREM";
    public static final String KEY_ZREMRANGEBYLEX = "ZREMRANGEBYLEX";
    public static final String KEY_ZREMRANGEBYRANK = "ZREMRANGEBYRANK";
    public static final String KEY_ZREMRANGEBYSCORE = "ZREMRANGEBYSCORE";
    public static final String KEY_ZREVRANGE = "ZREVRANGE";
    public static final String KEY_ZREVRANGEBYLEX = "ZREVRANGEBYLEX";
    public static final String KEY_ZREVRANGEBYSCORE = "ZREVRANGEBYSCORE";
    public static final String KEY_ZREVRANK = "ZREVRANK";
    public static final String KEY_ZSCAN = "ZSCAN";
    public static final String KEY_ZSCORE = "ZSCORE";
    public static final String KEY_ZUNION = "ZUNION";
    public static final String KEY_ZUNIONSTORE = "ZUNIONSTORE";


    // special
    public static final String KEY_SELECT_DATABASE = "SELECT_DATABASE";

    public static final String[] CREATE_OPERATION = {
            KEY_SET, KEY_HSETNX, KEY_LPUSH, KEY_RPUSH, KEY_SADD, KEY_ZADD
    };
    public static final String[] ALTER_OPERATION = {
            KEY_EXPIRE, KEY_EXPIREAT, KEY_PEXPIRE, KEY_PEXPIREAT, KEY_PERSIST, KEY_RENAME, KEY_RENAMENX
    };
    public static final String[] DROP_OPERATION = {
            KEY_DEL
    };
    public static final String[] DELETE_OPERATION = {
            KEY_HDEL, KEY_LREM, KEY_SREM, KEY_ZREM
    };
    public static final String[] UPDATE_OPERATION = {
            KEY_LSET, KEY_HSET
    };
    public static final String[] SELECT_OPERATION = {
            KEY_PTTL, KEY_TTL, KEY_RANDOMKEY, KEY_TYPE, KEY_GET, KEY_HGET, KEY_LLEN, KEY_LRANGE, KEY_SSCAN, KEY_SCARD,
            KEY_ZSCAN, KEY_SRANDMEMBER, KEY_HSCAN, KEY_HGETALL, KEY_SMEMBERS, KEY_ZRANGE
    };
    public static final String[] REDIS_CAN_EDIT = {

    };

}

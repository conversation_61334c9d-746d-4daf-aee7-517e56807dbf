package com.dc.repository.mysql.mapper;
import com.dc.repository.mysql.model.DCJob;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface DCJobMapper {
    public void update(@Param("id") Integer id, @Param("status") Integer status);

    public void beginUpdate(@Param("id") Integer id, @Param("status") Integer status);

    public DCJob select(@Param("id") Integer id);

    public DCJob selectById(@Param("id") Integer id);

    public void updateById(@Param("id") Integer id, @Param("status") Integer status);
    public void beginUpdateById(@Param("id") Integer id, @Param("status") Integer status, @Param("log_id") Integer log_id);
    public void endUpdateById(@Param("id") Integer id, @Param("status") Integer status, @Param("extraInfo") String extra_info);
}

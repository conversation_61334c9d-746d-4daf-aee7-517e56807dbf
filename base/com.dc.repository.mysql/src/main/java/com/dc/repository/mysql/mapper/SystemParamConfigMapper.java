package com.dc.repository.mysql.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.SystemParamConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SystemParamConfigMapper extends BaseMapper<SystemParamConfig> {

    String getParamConfig(@Param("key")String key);

    List<SystemParamConfig> getSystemParamConfig();

    SystemParamConfig getSystemParamConfigByKey(String key);

}

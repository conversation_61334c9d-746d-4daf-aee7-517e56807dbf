package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.model.ResourceUser;
import com.dc.repository.mysql.model.Schema;
import org.apache.ibatis.annotations.Mapper;

import java.util.Map;
import java.util.Optional;

@Mapper
public interface ResourceUserMapper extends BaseMapper<ResourceUser> {

    ResourceUser getDirectConnectionAccount(Map<String, Object> map);

    default Optional<ResourceUser> getResourceUserByUniqueKey(String uniqueKey) {
        return Optional.ofNullable(
                selectOne(Wrappers.<ResourceUser>lambdaQuery()
                        .eq(ResourceUser::getUnique_key, uniqueKey)));
    }

}

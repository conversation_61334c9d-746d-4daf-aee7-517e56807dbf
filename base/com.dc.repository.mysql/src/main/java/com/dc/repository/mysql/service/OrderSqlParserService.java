package com.dc.repository.mysql.service;

import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderSchemaTask;
import com.dc.repository.mysql.model.OrderSqlParse;

import java.util.List;
import java.util.Map;

public interface OrderSqlParserService extends EasyBaseService<OrderSqlParse> {

    List<OrderSqlParse> querySqlParser(Map<String, Object> param);

    List<OrderSqlParse> querySqlParserExport(Map<String, Object> param);

    int queryCountSqlParser(Map<String, Object> param);

    void updateSqlParser(OrderSqlParse orderSqlParse);

    void batchUpdateSqlParse(List<OrderSqlParse> orderSqlParseList, boolean isSave);

    void updateAllPauseSqlParse(OrderSqlParse orderSqlParse);

    void renewOrderSqlParser(OrderSqlParse orderSqlParse);

    void removeOrderSqlParserByOrder(Order order);

    void removeOrderSqlParserByTaskId(OrderSchemaTask task);

}

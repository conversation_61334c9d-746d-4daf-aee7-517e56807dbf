package com.dc.repository.mysql.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dc.repository.mysql.mapper.DcMetadataMapper;
import com.dc.repository.mysql.model.DcMetadata;
import com.dc.repository.mysql.service.DcMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DcMetadataServiceImpl extends ServiceImpl<DcMetadataMapper, DcMetadata> implements DcMetadataService {

    @Override
    public boolean updateMetadataObject(DcMetadata dcMetadata) {
        return update(new UpdateWrapper<DcMetadata>().lambda()
                .set(DcMetadata::getLastActiveTime, dcMetadata.getLastActiveTime())
                .set(DcMetadata::getGmtModified, new Date())
                .eq(DcMetadata::getConnectIdHash, dcMetadata.getConnectIdHash())
                .eq(DcMetadata::getSchemaName, dcMetadata.getSchemaName())
                .eq(DcMetadata::getObjectName, dcMetadata.getObjectName())
                .eq(DcMetadata::getObjectType, dcMetadata.getObjectType())
        );
    }

    @Override
    public boolean updateMetaUser(DcMetadata dcMetadata) {
        return update(new UpdateWrapper<DcMetadata>().lambda()
                .set(DcMetadata::getLastActiveTime, dcMetadata.getLastActiveTime())
                .set(DcMetadata::getObjectStatus, dcMetadata.getObjectStatus())
                .set(DcMetadata::getGmtModified, new Date())
                .eq(DcMetadata::getConnectIdHash, dcMetadata.getConnectIdHash())
                .eq(DcMetadata::getObjectName, dcMetadata.getObjectName())
                .eq(DcMetadata::getObjectType, "users")
                .le(DcMetadata::getLastActiveTime, dcMetadata.getLastActiveTime())
        );
    }

    @Override
    public List<DcMetadata> findTableByName(Long connectIdHash, String schemaName, String tableName) {
        return getBaseMapper().selectList(Wrappers.<DcMetadata>lambdaQuery()
                .eq(DcMetadata::getConnectIdHash, connectIdHash)
                .apply("UPPER(schema_name) = UPPER({0})", schemaName)
                .eq(DcMetadata::getObjectName, tableName)
                .eq(DcMetadata::getObjectType, "TABLE"));
    }
}

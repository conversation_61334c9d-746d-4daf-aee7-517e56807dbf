package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.RcSql;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


@Mapper
public interface RcSqlMapper extends BaseMapper<RcSql> {

    int add(RcSql rcSql);

    List<RcSql> getRcSqlBySessionId(RcSql rcSql);

    int updateById(RcSql rcSql);

    RcSql getRcSqlById(@Param("id") Long id);

    List<RcSql> getRcSqlListByBatchId(@Param("rc_batch_id") Long rc_batch_id);

    List<Long> getRcSqlIdByBatchId(@Param("rc_batch_id") Long rc_batch_id);

    void deleteRcSqlById(@Param("id") Long id);

    List<Long> getRcSqlIdBySessionId(RcSql rcSql);

    List<Long> getRcSqlTableId(RcSql rcSql);

    List<RcSql> getRcSqlListById(Map<String, Object> map);

    List<RcSql> getExpiredRcSqlList(@Param("expired_day") Integer expiredDay);

    void deleteExpiredRcSql(@Param("id") Long id);
}

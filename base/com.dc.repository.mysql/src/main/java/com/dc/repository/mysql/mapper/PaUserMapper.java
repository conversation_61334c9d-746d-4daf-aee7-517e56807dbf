package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.PaUser;
import com.dc.repository.mysql.model.SysOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;


@Mapper
public interface PaUserMapper extends EasyBaseMapper<PaUser> {

    List<PaUser> getAllOrg();

    List<PaUser> getUsersByList(Map userMap);

    SysOrg getOrgByUsername(String username);

    void updateBatchById(List<PaUser> list);

    @Update("truncate table pa_user")
    void truncateTable();
}

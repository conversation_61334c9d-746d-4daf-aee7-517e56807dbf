package com.dc.repository.mysql.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.repository.mysql.mapper.DcJobColumnMapper;
import com.dc.repository.mysql.model.DcJobColumn;
import com.dc.repository.mysql.service.DcJobColumnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class DcJobColumnServiceImpl implements DcJobColumnService {

    private final DcJobColumnMapper dcJobColumnMapper;

    public DcJobColumnServiceImpl(DcJobColumnMapper dcJobColumnMapper) {
        this.dcJobColumnMapper = dcJobColumnMapper;
    }

    @Override
    public List<DcJobColumn> findByDcJobId(int dcJobId) {
        return dcJobColumnMapper.selectList(new QueryWrapper<DcJobColumn>().lambda().eq(DcJobColumn::getDcJobId, dcJobId));
    }
}

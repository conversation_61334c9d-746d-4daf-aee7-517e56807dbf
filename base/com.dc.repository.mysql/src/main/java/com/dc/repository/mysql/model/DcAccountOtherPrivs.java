package com.dc.repository.mysql.model;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("dc_account_other_privs")
public class DcAccountOtherPrivs {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id; // 主键

    @TableField("resource_id")
    private Integer resourceId; // 资源id

    @TableField("account_id")
    private Integer accountId; // 账号主键id

    @TableField("is_expire")
    private Byte isExpire; // 权限是否有过期

    @TableField("privilege_expire")
    private LocalDateTime privilegeExpire; // 权限截止时间

    @TableField("gmt_create")
    private LocalDateTime gmtCreate; // 创建时间

    @TableField("gmt_modified")
    private LocalDateTime gmtModified; // 更新时间

    @TableField("is_delete")
    private Byte isDelete; // 是否删除

    @TableField("`sql`")
    private String sql; // SQL 内容

}

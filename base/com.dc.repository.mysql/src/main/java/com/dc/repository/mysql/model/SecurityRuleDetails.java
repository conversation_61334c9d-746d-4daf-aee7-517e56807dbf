package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_security_rule_details")
public class SecurityRuleDetails {

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    @TableField("navigation_tpl_key")
    private String navigationTplKey;

    @TableField("details_tpl_key")
    private String detailsTplKey;

    private String value;

    @TableField("is_delete")
    private Integer isDelete;

    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("rule_set_id")
    private Integer ruleSetId;

    private String unit;

}

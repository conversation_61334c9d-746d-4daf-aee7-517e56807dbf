package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.PaUserChange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;


@Mapper
public interface PaUserChangeMapper extends EasyBaseMapper<PaUserChange> {

    List<PaUserChange> getUsersByList(Map paParam);

    @Update("truncate table pa_user_change")
    void truncateTable();
}

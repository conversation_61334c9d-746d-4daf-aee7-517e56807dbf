package com.dc.repository.mysql.config;

import com.alibaba.druid.pool.DruidAbstractDataSource;
import com.alibaba.druid.pool.DruidDataSource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.lookup.DataSourceLookupFailureException;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Configuration
@ConfigurationProperties(value = "spring.mysql")
public class MysqlConfig {

    @Getter
    @Setter
    private List<DruidDataSource> nodeServerConfig;

    @Bean
    public MysqlRoutingDataSource mysqlRoutingDataSource() {

        if (CollectionUtils.isEmpty(nodeServerConfig)) {
            return null;
        }

        MysqlRoutingDataSource mysqlRoutingDataSource = MysqlRoutingDataSource.build();

        nodeServerConfig = nodeServerConfig.stream().filter(
                        druidDataSource -> StringUtils.isNotBlank(druidDataSource.getUrl()) &&
                                StringUtils.isNotBlank(StringUtils.substringBetween(druidDataSource.getUrl(), "//", ":")))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(nodeServerConfig)) {

            log.info("MySQL 数据源模式 [{}] 。", nodeServerConfig.size() == 1 ? "SINGLE" : "CLUSTER");

            nodeServerConfig.forEach(this::setDefaultProperties);

            DruidDataSource master = nodeServerConfig.stream().filter(druidDataSource -> druidDataSource.getName().startsWith("master"))
                    .findFirst()
                    .orElse(nodeServerConfig.stream().findFirst().orElseThrow());
            log.info("MySQL 数据源默认 [{}] 。", master.getName());
            mysqlRoutingDataSource.setDefaultTargetDataSource(master);

            Map<Object, Object> nodes = nodeServerConfig.stream().collect(Collectors.toMap(DruidAbstractDataSource::getName, Function.identity()));
            log.info("MySQL 数据源节点 {}。", nodes.keySet());
            mysqlRoutingDataSource.setTargetDataSources(nodes);

            mysqlRoutingDataSource.afterPropertiesSet();
            return mysqlRoutingDataSource;

        } else {
            throw new DataSourceLookupFailureException("Invalid configuration [spring.mysql], please fill in correctly.");
        }

    }

    private void setDefaultProperties(DruidDataSource druidDataSource) {
        druidDataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
    }

}

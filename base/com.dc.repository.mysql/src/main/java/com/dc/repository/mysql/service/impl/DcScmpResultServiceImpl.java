package com.dc.repository.mysql.service.impl;

import com.dc.repository.mysql.mapper.DcScmpResultMapper;
import com.dc.repository.mysql.mapper.DcScmpResultSqlMapper;
import com.dc.repository.mysql.model.DcScmpResult;
import com.dc.repository.mysql.model.DcScmpResultSql;
import com.dc.repository.mysql.service.DcScmpResultService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DcScmpResultServiceImpl implements DcScmpResultService {

    @Resource
    private DcScmpResultMapper dcScmpResultMapper;

    @Resource
    private DcScmpResultSqlMapper dcScmpResultSqlMapper;

    @Transactional
    @Override
    public void insert(DcScmpResult result, List<DcScmpResultSql> resultSqls) {
        dcScmpResultMapper.insert(result);
        for (DcScmpResultSql resultSql : resultSqls) {
            resultSql.setResultId(result.getId());
        }
        dcScmpResultSqlMapper.insertBatchSomeColumn(resultSqls);
    }

}

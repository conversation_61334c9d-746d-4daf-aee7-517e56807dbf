package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("dc_db_connection")
public class DatabaseConnection {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String instance_name;

    private String entity;

    private Integer db_type;

    private Integer environment;

    private Integer is_active;

    private String password;

    private String username;

    private String connection;

    private String connection_desc;

    private String ip;

    private String port;

    private String service_name;

    private Integer connect_type;

    private String domain;

    private String tenant;

    private String cluster;

    private String version;

    private String db_name;

    private String gmt_create;

    private String gmt_modified;

    private String unique_key;

    private Integer is_delete;

    private String driver_id;

    private String kingbase_database_mode;

    private String authentication_parameters;

    private String driver_properties;

    private String db_role;

    private String category_id;

    private Integer connect_mode;

    private String jdbc_url;

    private Integer auth_source;

    private String extended_attributes;

    private int executor;

    private String db_id;

    private String config;

    private int security_rule_set_id;

    private String uuid;

    private String creator_id;

    private Integer pattern;

    private String sync;

    private Integer is_dblink;

    private String dblink_name;

    private Integer maximum_context_size;

    private Integer keep_alive_time;

    private Integer query_timeout;

    private Integer network_timeout;

    private Long sync_schema_time;

}

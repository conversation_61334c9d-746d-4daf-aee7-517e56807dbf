package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.model.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Optional;


@Mapper
public interface UserMapper extends BaseMapper<User> {

    List<String> getConnectManagerUser(@Param("connect_id") String connectId);

    List<User> getUsersByPage(Map map);

    void updateUserStatus(List<String> userNames);

    List<User> getAllUser();

    void updateBatchById(List<User> list);

    List<User> getAllDisableUserByExpireTime(@Param("expire_time") String expireTime);

    void disableUserByExpireTime(@Param("expire_time") String expireTime);
    void disableUserById(@Param("id") Integer id);

    default Optional<User> getUserByUniqueKey(String uniqueKey) {
        return Optional.ofNullable(
                selectOne(Wrappers.<User>lambdaQuery()
                        .eq(User::getUniqueKey, uniqueKey)
                        .eq(User::getIsDelete, 0)));
    }

}

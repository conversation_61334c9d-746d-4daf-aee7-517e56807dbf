package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.DcAccountOtherPrivs;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DcAccountOtherPrivsMapper extends BaseMapper<DcAccountOtherPrivs> {
    default List<DcAccountOtherPrivs> getPrivileges(LambdaQueryWrapper<DcAccountOtherPrivs> wrapper) {
        return selectList(wrapper);
    }

    default DcAccountOtherPrivs getPrivilege(LambdaQueryWrapper<DcAccountOtherPrivs> wrapper) {
        return selectOne(wrapper);
    }

    default Integer addPrivilege(DcAccountOtherPrivs privilege) {
        return insert(privilege);
    }

    Integer updatePrivilege(DcAccountOtherPrivs privs);
}

package com.dc.repository.mysql.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicReference;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MysqlContextHolder {

    public static final Logger log = LoggerFactory.getLogger(MysqlContextHolder.class);

    private static final AtomicReference<String> contextHolder = new AtomicReference<>();

    public static void setDataSourceType(DruidDataSource druidDataSource) {
        contextHolder.set(druidDataSource.getName());
    }

    public static String getDataSourceType() {
        return contextHolder.get();
    }

}

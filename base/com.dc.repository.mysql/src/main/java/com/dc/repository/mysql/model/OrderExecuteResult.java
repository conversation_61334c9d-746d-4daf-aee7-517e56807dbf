package com.dc.repository.mysql.model;

import lombok.Data;

import java.util.Date;

/**
 * xxl-job info
 *
 * <AUTHOR>  2016-1-12 18:25:49
 */
@Data
public class OrderExecuteResult {

    private Integer id;
    private Date gmt_create;
    private Date gmt_modified;
    private Integer is_delete;
    private Integer order_execute_id;
    private String script_name;
    private Integer status;
    private Long total_time;
    private Integer type;
    private String execute_fail_reason;

    public OrderExecuteResult() {
    }

    public OrderExecuteResult(Integer id, String reason, String script_name, Integer type) {
        this.order_execute_id = id;
        this.execute_fail_reason = reason;
        this.script_name = script_name;
        this.type = type;
    }

    public OrderExecuteResult(Integer order_execute_id, String script_name, Integer status, Integer type) {
        this.order_execute_id = order_execute_id;
        this.script_name = script_name;
        this.status = status;
        this.type = type;
    }

}

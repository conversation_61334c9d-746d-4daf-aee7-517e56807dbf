package com.dc.repository.mysql.service;

import com.dc.repository.mysql.model.Order;

import java.util.Map;

public interface OrderService {

    Order getById(Integer id);

    int updateStatus(Order order);

    int updateStatusWithReason(Order order);

    void updateStatusByModel(Integer id, Integer status);

    int updateFailStatus(Map<String, Object> map);

    int updateAuditStatus(Map<String, Object> map);

    int updateStatus(Order order, Integer status);

    int existsOrderExecute(Integer id);

    int updateToken(Order order);

    int updateCheckFailReason(Order order);

    int updateExecuteFailReason(Order order);

    int updateOrderStatus(Order order);

    int updateStatusAndCheckFailReason(Order order);

    int updateCurrentStatus(Order order);
}

package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.DcAccountTsQuotas;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DcAccountTsQuotasMapper extends BaseMapper<DcAccountTsQuotas> {
    default List<DcAccountTsQuotas> getQuotas(LambdaQueryWrapper<DcAccountTsQuotas> wrapper) {
        return selectList(wrapper);
    }

    default Integer removePrivilege(LambdaQueryWrapper<DcAccountTsQuotas> wrapper) {
        return delete(wrapper);
    }

    Integer replaceInto(DcAccountTsQuotas dcAccountTsQuotas);
}

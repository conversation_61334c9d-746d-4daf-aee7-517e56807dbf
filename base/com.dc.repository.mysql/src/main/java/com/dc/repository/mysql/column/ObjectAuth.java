package com.dc.repository.mysql.column;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class ObjectAuth {
    private String catalog_name;
    private int grantable;
    private String object_name;
    private String privilege;
    private String privilege_expire;
    private String schema_name;

    public String getSchemaObject() {
        if (StringUtils.isBlank(schema_name)) {
            return object_name;
        }
        return schema_name + "." + object_name;
    }

    public boolean isGrantable() {
        return grantable == 1;
    }
}

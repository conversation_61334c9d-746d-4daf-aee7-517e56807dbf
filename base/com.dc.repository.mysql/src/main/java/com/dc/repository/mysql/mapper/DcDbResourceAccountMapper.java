package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.DcDbResourceAccount;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface DcDbResourceAccountMapper extends BaseMapper<DcDbResourceAccount> {
    default List<DcDbResourceAccount> getResourceAccounts(LambdaQueryWrapper<DcDbResourceAccount> wrapper) {
        return selectList(wrapper);
    }

    default DcDbResourceAccount getResourceAccount(Wrapper<DcDbResourceAccount> wrapper) {
        return selectOne(wrapper);
    }

    List<DcDbResourceAccount> selectAccountWithResourceByUniqueKeys(List<String> uniqueKeys);

    DcDbResourceAccount selectAccountWithResourceByUniqueKey(String uniqueKey);
}

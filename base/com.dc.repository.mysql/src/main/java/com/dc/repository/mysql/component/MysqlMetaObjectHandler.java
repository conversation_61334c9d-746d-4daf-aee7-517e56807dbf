package com.dc.repository.mysql.component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class MysqlMetaObjectHandler implements MetaObjectHandler {
    /**
     * 插入时填充逻辑
     *
     * @param metaObject 元对象
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        /**
         * 参数1：填充字段名
         * 参数2：参数值
         * 参数3：元对象
         */
        Date date = new Date();
        this.setFieldValByName("isDelete", 0, metaObject);
        try {
            this.setFieldValByName("gmtCreate", date, metaObject);
        } catch (Exception e) {
            this.setFieldValByName("gmtCreate", date.getTime(), metaObject);
        }
        try {
            this.setFieldValByName("gmtModified", date, metaObject);
        } catch (Exception e) {
            this.setFieldValByName("gmtModified", date.getTime(), metaObject);
        }
    }


    /**
     * 更新时填充逻辑
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        Date date = new Date();
        try {
            this.setFieldValByName("gmtModified", date, metaObject);
        } catch (Exception e) {
            this.setFieldValByName("gmtModified", date.getTime(), metaObject);
        }
    }

}

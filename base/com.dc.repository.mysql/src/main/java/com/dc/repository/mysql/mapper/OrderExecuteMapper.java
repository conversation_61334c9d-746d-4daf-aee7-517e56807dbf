package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.model.OrderExecute;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface OrderExecuteMapper {

    void updateExecuteStatusByOrderId(OrderExecute orderExecute);

    void updateExecuteByOrderId(OrderExecute orderExecute);

    void updateExecuteById(OrderExecute orderExecute);

    OrderExecute getOrderExecuteByOrderId(int id);

    List<OrderExecute> getOrderTimingScript(@Param("now") Integer now);

    Set<Integer> getOrderIdOfSpecifiedType(@Param("birthOrderIds") Set<Integer> birthOrderIds, @Param("workOrderType") String workOrderType);

    List<OrderExecute> getOrderExecuteListByOrderId(Integer id);

    List<OrderExecute> getOrderExecuteListByTaskIds(List<Integer> ids);

    Integer getUnfinishedOrderExecuteCnt(@Param("order_id") Integer orderId);

    void updateOrderExecuteBatch(@Param("id_list") List<Integer> orderExecuteIds, @Param("status") Integer status);

    Integer updateInterruptStatus(@Param("taskId") Integer id, @Param("status") Integer status);

    OrderExecute getOrderExecuteByOrderIdAndSchemaId(@Param("id") Integer id, @Param("taskId") Integer taskId);

}

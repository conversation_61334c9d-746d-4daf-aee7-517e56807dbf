package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.RcTable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface RcTableMapper extends BaseMapper<RcTable> {

    void add(RcTable rcTable);

    List<RcTable> getRcTable(RcTable rcTable);

    List<RcTable> getRcTableListByIds(Map<String, Object> map);
}

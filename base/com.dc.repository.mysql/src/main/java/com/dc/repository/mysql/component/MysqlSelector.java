package com.dc.repository.mysql.component;

import com.alibaba.druid.pool.DataSourceDisableException;
import com.alibaba.druid.pool.DataSourceNotAvailableException;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidPooledConnection;
import com.dc.repository.mysql.config.MysqlRoutingDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.lookup.DataSourceLookupFailureException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class MysqlSelector {

    @Autowired(required = false)
    private MysqlRoutingDataSource mysqlRoutingDataSource;

    private static final ReentrantLock LOCK = new ReentrantLock();

    private Object availableKey;

    public DruidDataSource getAvailableDataSource() throws DataSourceNotAvailableException {

        if (mysqlRoutingDataSource == null) {
            return null;
        }

        DataSourceNotAvailableException exception;

        DruidDataSource druidDataSource;

        Object lookupKey = availableKey;

        druidDataSource = lookupKey == null ?
                (DruidDataSource) mysqlRoutingDataSource.getResolvedDefaultDataSource() :
                (DruidDataSource) mysqlRoutingDataSource.getResolvedDataSources().get(lookupKey);

        try {
            ensureAvailability(druidDataSource);
            log.info("MySQL 数据源使用 [{}] 。", lookupKey);
            return druidDataSource;
        } catch (Exception e) {
            exception = new DataSourceNotAvailableException(e);
        }

        for (Map.Entry<Object, DataSource> entry : mysqlRoutingDataSource.getResolvedDataSources().entrySet()) {
            Object key = entry.getKey();
            druidDataSource = (DruidDataSource) entry.getValue();

            if (!key.equals(lookupKey)) {
                try {
                    ensureAvailability(druidDataSource);
                    log.info("MySQL 数据源切换 [{}] 。", key);
                    return druidDataSource;
                } catch (Exception e) {
                    exception = new DataSourceNotAvailableException(e);
                }
            }

        }

        throw exception;

    }


    private void ensureAvailability(DruidDataSource druidDataSource) throws SQLException {

        if (druidDataSource == null) {
            throw new DataSourceLookupFailureException("druidDataSource is null.");
        }

        Object key = druidDataSource.getName();

        LOCK.lock();

        try {
            if (checkDataSourceFailContinuousStatus(druidDataSource)) {
                druidDataSource.restart();
            }

        } finally {
            LOCK.unlock();
        }

        try (DruidPooledConnection connection = druidDataSource.getConnection()) {
            if (connection.isValid(0)) {
                availableKey = druidDataSource.getName();
            } else {
                throw new DataSourceDisableException();
            }
        } catch (Exception e) {
            log.error("MySQL 数据源无效 [{}] 。", key);
            throw e;
        }

    }

    protected boolean checkDataSourceFailContinuousStatus(DruidDataSource druidDataSource) {

        if (!druidDataSource.isBreakAfterAcquireFailure()) {
            log.info("this data source does not open `BreakAfterAcquireFailure`");
            return false;
        }
        boolean failContinuous = druidDataSource.isFailContinuous(); // fail over retry attempts
        boolean createConnectionThreadIsAlive = true;
        try {
            DruidDataSource.CreateConnectionThread createConnectionThread = getCreateConnectionThread(druidDataSource);
            createConnectionThreadIsAlive = createConnectionThread.isAlive();
        } catch (Exception e) {
            log.error("Get dataSource: " + druidDataSource + "'s createConnectionThread fails.");
        }
        boolean flag = failContinuous && !createConnectionThreadIsAlive;
        if (flag) {
            log.info("Fail dataSource: {}", druidDataSource.getName());
        }
        return flag;
    }

    public static DruidDataSource.CreateConnectionThread getCreateConnectionThread(DruidDataSource dataSource)
            throws NoSuchFieldException, IllegalAccessException {
        Field createConnectionThread = DruidDataSource.class.getDeclaredField("createConnectionThread");
        createConnectionThread.setAccessible(true);
        DruidDataSource.CreateConnectionThread createConnectionThreadInstance =
                (DruidDataSource.CreateConnectionThread) createConnectionThread.get(dataSource);
        log.debug("Get dataSource: {} , createConnectionThread: {}",
                dataSource.toString().replace("\n", "").replace("\t", ""),
                createConnectionThreadInstance.getName());
        return createConnectionThreadInstance;
    }

}

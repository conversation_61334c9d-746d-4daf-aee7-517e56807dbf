package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.model.SensitiveData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DBColumnMapper {

    List<SensitiveData> getColumnMaskConfig(@Param("unique_key") String uniqueKey, @Param("table_name") String tableName, @Param("column_names") List<String> columnNames);

}

package com.dc.repository.mysql.config;

import com.dc.repository.mysql.component.MysqlSelector;
import com.dc.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.SQLRecoverableException;

@Aspect
@Component
@Order(-1)
@Slf4j
public class MysqlRepositoryAspect {

    @Resource
    private MysqlSelector mysqlSelector;

    @Pointcut("execution(* com.dc.repository.mysql.service.*.*(..))")
    public void service() {
    }

    @Pointcut("execution(* com.dc.repository.mysql.mapper.*.*(..))")
    public void mapper() {
    }

    @Pointcut("execution(* com.dc.administrator.dao.*.*(..))")
    public void dao() {
    }

    @Around("mapper()||dao()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {

        long ctm = System.currentTimeMillis();

        try {
            log.debug("==> ExecuteMethod: {}.{}", StringUtils.compressClassName(joinPoint.getSignature().getDeclaringTypeName()), joinPoint.getSignature().getName());
        } catch (Exception ignored) {
            // nothing to do here
        }

        try {
            return joinPoint.proceed();
        } catch (Throwable t) {
            Throwable throwable = t;
            while (throwable != null) {
                if (throwable instanceof SQLRecoverableException) {
                    MysqlContextHolder.setDataSourceType(mysqlSelector.getAvailableDataSource());
                    return joinPoint.proceed();
                }
                throwable = throwable.getCause();
            }
            throw t;
        } finally {
            log.debug("<== {}: {} ms", "ExecuteTime", System.currentTimeMillis() - ctm);
        }
    }

    @Before("service())")
    public void doBefore(JoinPoint joinPoint) {
        log.debug("==> ExecuteMethod: {}.{}", StringUtils.compressClassName(joinPoint.getSignature().getDeclaringTypeName()), joinPoint.getSignature().getName());
    }

}

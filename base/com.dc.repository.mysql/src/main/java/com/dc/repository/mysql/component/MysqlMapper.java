package com.dc.repository.mysql.component;

import com.dc.repository.mysql.model.RelationTable;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface MysqlMapper {

    MysqlMapper INSTANCE = Mappers.getMapper(MysqlMapper.class);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateRelationTable(RelationTable source, @MappingTarget RelationTable target);

}

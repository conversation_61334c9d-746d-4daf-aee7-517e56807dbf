package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.OrderSchemaTask;
import com.dc.repository.mysql.model.WorkOrderScript;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface WorkOrderScriptMapper extends EasyBaseMapper<WorkOrderScript> {

    void save(WorkOrderScript workOrderScript);

    void update(WorkOrderScript workOrderScript);

    void updateStatus(WorkOrderScript workOrderScript);
}

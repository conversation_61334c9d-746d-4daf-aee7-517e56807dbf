package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.DcAsyncQuery;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DcAsyncQueryMapper extends BaseMapper<DcAsyncQuery> {


    default Integer updateStatus(Integer id, Integer status) {
        LambdaUpdateWrapper<DcAsyncQuery> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DcAsyncQuery::getId, id)
                .set(DcAsyncQuery::getExecuteStatus, status);
        return this.update(null, updateWrapper);
    }

    default Integer updateRecord(DcAsyncQuery dcAsyncQuery) {
        LambdaUpdateWrapper<DcAsyncQuery> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DcAsyncQuery::getId, dcAsyncQuery.getId());
        return this.update(dcAsyncQuery, updateWrapper);
    }

    default DcAsyncQuery getDcAsyncQueryById(Integer id) {
        LambdaQueryWrapper<DcAsyncQuery> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcAsyncQuery::getId, id);
        return this.selectOne(queryWrapper);
    }

}

package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.DcConnectionAttempts;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;


@Mapper
public interface DcConnectionAttemptsMapper extends BaseMapper<DcConnectionAttempts> {
    List<Map<String, Object>> getConnectionAttempts(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("instanceUUID") String instanceUUID);
}

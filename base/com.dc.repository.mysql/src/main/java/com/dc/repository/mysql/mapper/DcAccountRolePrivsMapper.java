package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.DcAccountObjPrivs;
import com.dc.repository.mysql.model.DcAccountRolePrivs;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DcAccountRolePrivsMapper extends EasyBaseMapper<DcAccountRolePrivs> {

    default List<DcAccountRolePrivs> getPrivileges(LambdaQueryWrapper<DcAccountRolePrivs> wrapper) {
        return selectList(wrapper);
    }

    default Integer removePrivilege(LambdaQueryWrapper<DcAccountRolePrivs> wrapper) {
        return delete(wrapper);
    }

    Integer replaceInto(List<DcAccountRolePrivs> dcAccountRolePrivs);

    Integer replaceIntoNoAdminOption(List<DcAccountRolePrivs> dcAccountRolePrivs);

}

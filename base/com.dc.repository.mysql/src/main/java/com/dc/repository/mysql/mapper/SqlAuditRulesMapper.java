package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.model.SqlAuditRules;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SqlAuditRulesMapper extends BaseMapper<SqlAuditRules> {

    default List<SqlAuditRules> findByRuleSetId(Integer ruleSetId) {
        return selectList(Wrappers.<SqlAuditRules>lambdaQuery()
                .eq(SqlAuditRules::getRuleSetId, ruleSetId)
                .and(wq -> wq.eq(SqlAuditRules::getStatus, 1).or().eq(SqlAuditRules::getIsAlert, 1)));
    }

}

package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class DcWorkOrderFlowUser {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private Integer flow_id;
    private Integer base_type;
    private String user_id;
    private String username;
    private Integer is_read;
}

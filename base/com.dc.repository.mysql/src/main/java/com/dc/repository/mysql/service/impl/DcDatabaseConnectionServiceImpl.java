package com.dc.repository.mysql.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.mysql.service.DcDatabaseConnectionService;
import com.google.protobuf.ServiceException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class DcDatabaseConnectionServiceImpl implements DcDatabaseConnectionService {

    @Resource
    DatabaseConnectionMapper dbConnectionMapper;

    @Override
    public DatabaseConnection get(String uniqueKey) throws Exception {
        DatabaseConnection databaseConnection = dbConnectionMapper.selectOne(new QueryWrapper<DatabaseConnection>().lambda()
                .eq(DatabaseConnection::getUnique_key, uniqueKey)
                .eq(DatabaseConnection::getIs_delete, 0));
        Optional.ofNullable(databaseConnection).orElseThrow(() -> new ServiceException("connection not found"));
        return databaseConnection;
    }

    @Override
    public DatabaseConnection selectPaInstance(String instanceId) throws Exception {
        DatabaseConnection databaseConnection = dbConnectionMapper.selectOne(new QueryWrapper<DatabaseConnection>().lambda()
                .eq(DatabaseConnection::getSync, instanceId)
                .eq(DatabaseConnection::getIs_delete, 0));
        Optional.ofNullable(databaseConnection).orElseThrow(() -> new ServiceException("connection not found"));
        return databaseConnection;
    }
}

package com.dc.repository.mysql.config;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

public class MysqlRoutingDataSource extends AbstractRoutingDataSource {


    public static MysqlRoutingDataSource build() {
        return new MysqlRoutingDataSource();
    }

    @Override
    protected Object determineCurrentLookupKey() {
        return MysqlContextHolder.getDataSourceType();
    }

}

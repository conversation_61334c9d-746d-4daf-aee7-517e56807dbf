package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.model.UserLimit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户配置表
 */
@Mapper
public interface UserLimitMapper {

    List<UserLimit> getUserLimit(@Param("id") String id);

    UserLimit getUserLimitByPdKey(@Param("id") String id, @Param("pd_key") String pdKey);
}

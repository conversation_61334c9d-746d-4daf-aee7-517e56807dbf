package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.model.ConfigAlertRule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ConfigAlertRuleMapper extends BaseMapper<ConfigAlertRule> {

    default List<ConfigAlertRule> findByAlertType(Integer alertType) {
        return selectList(Wrappers.<ConfigAlertRule>lambdaQuery()
                .eq(ConfigAlertRule::getAlertType, alertType)
                .eq(ConfigAlertRule::getIsDelete, 0));
    }

}

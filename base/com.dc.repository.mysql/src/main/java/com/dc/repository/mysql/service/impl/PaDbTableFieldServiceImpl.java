package com.dc.repository.mysql.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dc.repository.mysql.mapper.PaTableFieldMapper;
import com.dc.repository.mysql.model.PaDbTableField;
import com.dc.repository.mysql.service.PaDbTableFieldService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Service
public class PaDbTableFieldServiceImpl extends ServiceImpl<PaTableFieldMapper, PaDbTableField> implements PaDbTableFieldService {

    @Override
    public PaDbTableField read(String connectId, String schemaId, String tableName) {
        //当天数据有效,避免数据库列变化,以及涉敏状态变化, xxlJob, 凌晨 truncate 一次
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        PaDbTableField paTableField = baseMapper.selectOne(new QueryWrapper<PaDbTableField>().lambda()
                .eq(PaDbTableField::getConnectionId, connectId)
                .eq(PaDbTableField::getSchemaId, schemaId)
                .eq(PaDbTableField::getTableName, tableName)
                .le(PaDbTableField::getGmtCreate, today)
                .last("limit 1")
        );
        return paTableField;
    }
}

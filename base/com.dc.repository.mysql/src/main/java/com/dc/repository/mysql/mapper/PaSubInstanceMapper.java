package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.PaSubInstance;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;


@Mapper
public interface PaSubInstanceMapper extends EasyBaseMapper<PaSubInstance> {

    List<PaSubInstance> getByEntityUuid(Map userMap);

    void updateBatchById(List<PaSubInstance> list);
}

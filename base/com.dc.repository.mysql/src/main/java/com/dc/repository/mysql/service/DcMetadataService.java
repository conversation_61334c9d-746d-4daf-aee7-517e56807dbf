package com.dc.repository.mysql.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dc.repository.mysql.model.DcMetadata;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DcMetadataService extends IService<DcMetadata> {

    /**
     * 更新对象的最后访问时间
     *
     * @param dcMetadata dcMetadata
     * @return update result
     */
    boolean updateMetadataObject(DcMetadata dcMetadata);

    /**
     * 更新用户的最后访问时间
     *
     * @param dcMetadata dcMetadata
     * @return update result
     */
    boolean updateMetaUser(DcMetadata dcMetadata);

    List<DcMetadata> findTableByName(Long connectIdHash, String schemaName, String tableName);
}

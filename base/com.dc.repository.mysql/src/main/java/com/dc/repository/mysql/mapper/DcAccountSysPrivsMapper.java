package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.DcAccountObjPrivs;
import com.dc.repository.mysql.model.DcAccountSysPrivs;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DcAccountSysPrivsMapper extends EasyBaseMapper<DcAccountSysPrivs> {
    default List<DcAccountSysPrivs> getPrivileges(LambdaQueryWrapper<DcAccountSysPrivs> wrapper) {
        return selectList(wrapper);
    }

    default Integer removePrivilege(LambdaQueryWrapper<DcAccountSysPrivs> wrapper) {
        return delete(wrapper);
    }

    Integer replaceInto(List<DcAccountSysPrivs> dcAccountSysPrivs);

    Integer replaceIntoNoAdminOption(List<DcAccountSysPrivs> dcAccountSysPrivs);
}

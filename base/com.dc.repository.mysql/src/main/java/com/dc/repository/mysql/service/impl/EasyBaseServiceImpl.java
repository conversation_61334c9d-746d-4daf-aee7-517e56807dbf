package com.dc.repository.mysql.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.column.ID;
import com.dc.repository.mysql.mapper.MySQLVariablesMapper;
import com.dc.repository.mysql.service.EasyBaseService;
import com.google.gson.Gson;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public abstract class EasyBaseServiceImpl<T extends ID> implements EasyBaseService<T> {

    private final EasyBaseMapper<T> easyBaseMapper;

    private static final int BATCH_INSERT_MAX_SIZE = 1000;

    private static final Gson GSON = new Gson();

    @Resource
    private MySQLVariablesMapper variablesMapper;

    protected EasyBaseServiceImpl(EasyBaseMapper<T> easyBaseMapper) {
        this.easyBaseMapper = easyBaseMapper;
    }

    public void insertBatchMaxSize(List<T> insertList, Runnable insertBefore, Runnable insertAfter) {
        if (null != insertList && insertList.size() > BATCH_INSERT_MAX_SIZE) {
            insertBatchAllEntity(insertList, insertBefore, insertAfter);
        }
    }

    public void insertBatchAllEntity(List<T> insertList, Runnable insertBefore, Runnable insertAfter) {
        if (null == insertList || insertList.isEmpty()) {
            return;
        }
        if (insertBefore != null) {
            insertBefore.run();
        }
        final long time = new Date().getTime();
        insertBatchAllEntity(insertList, Integer.parseInt(variablesMapper.showMaxAllowedPacket().getValue()));
        if (insertAfter != null) {
            // 回填ID
            List<T> ts = easyBaseMapper.selectList(Wrappers.<T>lambdaQuery()
                    .select(getResultClass(), t -> false)
                    .apply("gmt_create >= {0}", time));
            for (int i = 0; i < insertList.size(); i++) {
                // 如果 T 没有继承 ID 将会报错
                insertList.get(i).setId(ts.get(i).getId());
            }
            insertAfter.run();
        }
        insertList.clear();
    }

    public void insertBatchAllEntity(List<T> insertList, int maxAllowedPacket) {
        int size = insertList.size();
        if (size > 2) {

            int len = GSON.toJson(insertList).length();
            if (len > 0 && len > maxAllowedPacket) {

                // 使用二分法减少插入数量。
                int mid = size / 2;
                List<T> left = new ArrayList<>(mid);
                List<T> right = new ArrayList<>(size - mid);
                for (int i = 0; i < size; i++) {
                    T t = insertList.get(i);
                    if (i < mid) {
                        left.add(t);
                    } else {
                        right.add(t);
                    }
                }

                insertBatchAllEntity(left, maxAllowedPacket);
                insertBatchAllEntity(right, maxAllowedPacket);

                return;
            }
        }

        easyBaseMapper.insertBatchSomeColumn(insertList);
    }

    @SuppressWarnings("unchecked")
    private Class<T> getResultClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

}

package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.DataTable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface DataTableMapper extends BaseMapper<DataTable> {

    void add(DataTable dataTable);

    Map<String, Object> getDataMap(DataTable dataTable);

    List<Long> getDataIds(DataTable dataTable);

    void deleteDataByIds(DataTable dataTable);

    void deleteDataByRowIds(Map<String, Object> map);

    int getDataTableCount(Map<String, Object> map);

    void dropDataTable(Map<String, Object> map);
}

package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.model.DcDbResource;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DcDbResourceMapper extends BaseMapper<DcDbResource> {

    default List<DcDbResource> getResourcesByConnectionDesc(Integer dbType, String connectionDesc) {
        return selectList(Wrappers.<DcDbResource>lambdaQuery()
                .eq(DcDbResource::getDb_type, dbType)
                .eq(DcDbResource::getConnection_desc, connectionDesc));
    }

    default List<DcDbResource> getResources(QueryWrapper<DcDbResource> wrapper) {
        return selectList(wrapper);
    }

    default DcDbResource getResource(LambdaQueryWrapper<DcDbResource> wrapper) {
        return selectOne(wrapper);
    }
}

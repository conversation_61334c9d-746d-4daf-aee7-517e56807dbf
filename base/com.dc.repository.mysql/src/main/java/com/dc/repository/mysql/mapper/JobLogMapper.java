package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.model.JobLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;


@Mapper
public interface JobLogMapper {
	int updateTriggerStatus(@Param("logId") long logId,
                            @Param("triggerStatus") int triggerStatus,
                            @Param("triggerEndTime") Date triggerEndTime);

	int updateTriggerTime(@Param("logId") long logId,
                          @Param("triggerTime") Date triggerTime);

	int updateFileDownloadLink(@Param("logId") long logId,
                               @Param("fileDownloadLink") String fileDownloadLink);

	int updateBeginTriggerStatus(@Param("logId") long logId,
                                 @Param("triggerStatus") int triggerStatus,
                                 @Param("triggerCount") long triggerCount);

	int updateExecutorTomcatAddress(@Param("logId") long logId,
                                    @Param("executorTomcatAddress") String executorTomcatAddress);

	int findJobTriggerCount(@Param("jobId") int jobId);

	JobLog load(@Param("id") long id);

	JobLog loadByJobId(@Param("jobId") int jobId);

}

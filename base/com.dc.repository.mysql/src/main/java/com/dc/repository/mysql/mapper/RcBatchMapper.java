package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.RcBatch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface RcBatchMapper extends BaseMapper<RcBatch> {

    void add(RcBatch rcBatch);

    RcBatch getRcBatchById(@Param("id") Long id);

    void deleteExpiredRcBatch(@Param("id") Long id);

    void deleteExpiredRcBatchByIdList(Map<String, Object> map);
}

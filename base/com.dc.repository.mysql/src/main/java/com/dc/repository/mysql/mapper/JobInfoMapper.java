package com.dc.repository.mysql.mapper;
import com.dc.repository.mysql.model.JobInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface JobInfoMapper {

    public int save(JobInfo info);

    public JobInfo loadByHandle(String executor_handler);

    public String loadGroupId(@Param("app_name")String app_name);

    public int update(@Param("job_cron")String job_cron, @Param("executor_handler")String executor_handler);

    public int delete(@Param("executor_handler")String executor_handler);

    public int updateStatus(@Param("trigger_status")String trigger_status, @Param("executor_handler")String executor_handler);

    public int updateStatusByJobId(@Param("trigger_status")int trigger_status, @Param("job_id")int job_id);

}

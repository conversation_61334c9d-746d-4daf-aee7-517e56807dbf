package com.dc.repository.mysql.column;

import lombok.Data;

@Data
public class TsQuota {
    private long max_bytes;
    private String privilege_expire;
    private String tablespace_name;
    private String unit;

    public String getQuotaSize() {
        if (max_bytes == -1) {
            return "UNLIMITED";
        }
        switch (unit) {
            case "MB":
                return max_bytes + "M";
            case "GB":
                return max_bytes + "G";
            case "KB":
                return max_bytes + "K";
            default:
                return max_bytes + "";
        }
    }
}

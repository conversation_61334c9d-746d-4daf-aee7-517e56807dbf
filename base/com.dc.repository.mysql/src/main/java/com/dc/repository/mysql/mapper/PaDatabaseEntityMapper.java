package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.PaDatabaseEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;


@Mapper
public interface PaDatabaseEntityMapper extends EasyBaseMapper<PaDatabaseEntity> {

    List<PaDatabaseEntity> getByEntityUuids(Map userMap);

    void updateBatchById(List<PaDatabaseEntity> list);
}

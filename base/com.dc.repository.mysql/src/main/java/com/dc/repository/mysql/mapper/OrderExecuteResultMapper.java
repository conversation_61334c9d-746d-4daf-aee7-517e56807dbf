package com.dc.repository.mysql.mapper;


import com.dc.repository.mysql.model.OrderExecuteResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface OrderExecuteResultMapper {

    int updateStatus(OrderExecuteResult order);

    int updateExecuteResult(OrderExecuteResult order);

    Integer getFailCount(Integer id);

    void updateTotalTime(OrderExecuteResult order);

    OrderExecuteResult getOrderExecuteResult(OrderExecuteResult order);

    int selectSuccessScriptByOrderExecuteId(@Param("order_execute_id") int orderExecuteId);

    int selectAllScriptByOrderExecuteId(@Param("order_execute_id") int orderExecuteId);

    int selectScriptByOrderExecuteIdByStatus(@Param("order_execute_id") int orderExecuteId, @Param("status") Integer status);

    int updateExecuteFailReason(OrderExecuteResult order);
}

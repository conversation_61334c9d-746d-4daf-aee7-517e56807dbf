package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.OracleRecycleBin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OracleRecycleBinMapper extends BaseMapper<OracleRecycleBin> {

    void add(OracleRecycleBin oracleRecycleBin);

    OracleRecycleBin getRecycleBinById(@Param("id") Long id);
}

package com.dc.repository.mysql.utils;

import com.dc.repository.mysql.model.User;
import com.dc.utils.CipherUtils;
import org.apache.commons.lang3.StringUtils;

public class UserUtils {

    private UserUtils() {}

    public static String getDecryptedPhone(User user) {
        String phone = user.getPhone();
        String phoneSecretKey = user.getPhoneSecretKey();
        if (StringUtils.isNoneEmpty(phone, phoneSecretKey)) {
            return CipherUtils.sm4decrypt(phone, phoneSecretKey);
        }
        return phone;
    }

    public static String getDecryptedEmail(User user) {
        String email = user.getEmail();
        String emailSecretKey = user.getEmailSecretKey();
        if (StringUtils.isNoneEmpty(email, emailSecretKey)) {
            return CipherUtils.sm4decrypt(email, emailSecretKey);
        }
        return email;
    }


}

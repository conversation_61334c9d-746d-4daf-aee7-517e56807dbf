package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.RelationTable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface RelationTableMapper extends BaseMapper<RelationTable> {

    void add(RelationTable relationTable);

    List<RelationTable> getRelationTableByRowId(RelationTable relationTable);

    List<RelationTable> getRelationTables(Map<String, Object> map);

    RelationTable getRelationTableById(RelationTable relationTable);

    List<RelationTable> getRelationTableByRootId(RelationTable relationTable);

    void deleteRelationTables(Map<String, Object> map);

    int getRelationTableCount(Map<String, Object> map);

    void dropRelationTable(Map<String, Object> map);
}

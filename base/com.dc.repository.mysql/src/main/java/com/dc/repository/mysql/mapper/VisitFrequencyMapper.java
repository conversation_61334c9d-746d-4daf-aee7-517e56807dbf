package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.VisitFrequency;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface VisitFrequencyMapper extends BaseMapper<VisitFrequency> {

    long getCount(VisitFrequency visitFrequency);

    void deleteExpireRecords(@Param("now") Long now, @Param("day") Long day);

}

package com.dc.repository.mysql.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.mapper.OrderSqlParserMapper;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderSchemaTask;
import com.dc.repository.mysql.model.OrderSqlParse;
import com.dc.repository.mysql.service.OrderSqlParserService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
public class OrderSqlParserServiceImpl extends EasyBaseServiceImpl<OrderSqlParse> implements OrderSqlParserService {

    private final OrderSqlParserMapper orderSqlParserMapper;

    public OrderSqlParserServiceImpl(OrderSqlParserMapper orderSqlParserMapper) {
        super(orderSqlParserMapper);
        this.orderSqlParserMapper = orderSqlParserMapper;
    }

    @Override
    public List<OrderSqlParse> querySqlParser(Map<String, Object> param) {
        return orderSqlParserMapper.query(param);
    }

    @Override
    public List<OrderSqlParse> querySqlParserExport(Map<String, Object> param) {
        return orderSqlParserMapper.queryByEqual(param);
    }

    @Override
    public int queryCountSqlParser(Map<String, Object> param) {
        return orderSqlParserMapper.count(param);
    }

    @Override
    public void updateSqlParser(OrderSqlParse orderSqlParse) {
        orderSqlParserMapper.update(orderSqlParse);
    }

    @Override
    public void batchUpdateSqlParse(List<OrderSqlParse> orderSqlParseList, boolean isSave) {
        if (!CollectionUtils.isEmpty(orderSqlParseList) && (orderSqlParseList.size() > 1000 || isSave)) {
            orderSqlParserMapper.updateBatch(orderSqlParseList);
            orderSqlParseList.clear();
        }
    }

    @Override
    public void updateAllPauseSqlParse(OrderSqlParse orderSqlParse) {
        orderSqlParserMapper.updateAllPauseSqlParse(orderSqlParse);
    }

    @Override
    public void renewOrderSqlParser(OrderSqlParse orderSqlParse) {
        orderSqlParserMapper.renewOrderSqlParser(orderSqlParse);
    }

    @Override
    public void removeOrderSqlParserByOrder(Order order) {
        orderSqlParserMapper.delete(Wrappers.<OrderSqlParse>lambdaQuery()
                .eq(OrderSqlParse::getOrder_id, order.getId()));
    }

    @Override
    public void removeOrderSqlParserByTaskId(OrderSchemaTask task) {
        orderSqlParserMapper.delete(Wrappers.<OrderSqlParse>lambdaQuery()
                .eq(OrderSqlParse::getOrder_schema_task_id, task.getId()));
    }
}

package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("dc_db_resource_account_info")
public class DcDbResourceAccountInfo {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除 (0-未删除, 1-已删除)
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 用户来源 (1-本系统, 2-外部)
     */
    @TableField("user_source")
    private Integer userSource;

    /**
     * 用户类型
     */
    @TableField("user_type")
    private String userType;

    /**
     * 使用人
     */
    @TableField("user_id")
    private String userId;

    /**
     * 使用部门
     */
    @TableField("user_org")
    private String userOrg;

    /**
     * 员工编号
     */
    @TableField("user_no")
    private String userNo;

    /**
     * 岗位
     */
    @TableField("user_post")
    private String userPost;

    /**
     * 用途
     */
    @TableField("user_use")
    private String userUse;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 账号ID
     */
    @TableField("account_id")
    private String accountId;

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.PaUserChangeMapper">

    <select id="getUsersByList" resultType="com.dc.repository.mysql.model.PaUserChange" parameterType="java.util.Map">
        SELECT um
        FROM pa_user_change
        where um in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>

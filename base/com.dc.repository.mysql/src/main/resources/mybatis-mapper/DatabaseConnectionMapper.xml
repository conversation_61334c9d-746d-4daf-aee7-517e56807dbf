<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DatabaseConnectionMapper">

    <select id="getAllConnections" resultType="com.dc.repository.mysql.model.DatabaseConnection" >
        select *
        from dc_db_connection as t
        where t.is_active = 1
          and t.is_delete = 0
    </select>

    <select id="getActiveConnectionByUniqueKey" resultType="com.dc.repository.mysql.model.DatabaseConnection" parameterType="java.lang.String" >
        select *
        from dc_db_connection as t
        where t.is_active = 1
          and t.is_delete = 0
          and t.unique_key = #{unique_key}
    </select>

    <select id="getConnectionByUniqueKey" resultType="com.dc.repository.mysql.model.DatabaseConnection" parameterType="java.lang.String" >
        select *
        from dc_db_connection as t
        where t.unique_key = #{unique_key}
        and t.is_delete = 0
    </select>

    <select id="getByUuids" resultType="com.dc.repository.mysql.model.DatabaseConnection" parameterType="java.util.Map" >
        select id,unique_key,sync,extended_attributes,entity,security_rule_set_id
        from dc_db_connection
        where is_delete = 0 and sync in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <update id="updateBySyncId" parameterType="java.util.Map">
        update dc_db_connection set extended_attributes = #{extended_attributes}
        where sync = #{sync}
    </update>

    <select id="getConnections" resultType="com.dc.repository.mysql.model.DatabaseConnection" >
        select id,unique_key,ip, port,db_type,security_rule_set_id,sync,extended_attributes,entity
        from dc_db_connection
        where is_delete = 0 and sync is null
    </select>

    <select id="getInstanceByDblinkName" resultType="com.dc.repository.mysql.model.DatabaseConnection" parameterType="java.lang.String" >
        select *
        from dc_db_connection as t
        where t.dblink_name = #{dblink_name}
          and t.is_delete = 0
          and t.is_dblink = 1
          and t.is_active = 1
        limit 1
    </select>

    <select id="getDbEntity" resultType="com.dc.repository.mysql.model.DatabaseConnection" >
        select distinct db_type,entity
        from dc_db_connection
        where is_delete = 0 and entity is not null and entity != ''
    </select>

    <select id="getRaseSqlConnections" resultType="com.dc.repository.mysql.model.DatabaseConnection" >
        select unique_key from dc_db_connection where db_type = 41 and is_delete = 0 and is_active = 1 and (version is null or version = "")
    </select>

    <select id="getZombieInstances" resultType="com.dc.repository.mysql.model.DatabaseConnection">
        SELECT conn.*
        FROM dc_db_connection conn
                 JOIN dc_security_rule_set rule_set ON conn.security_rule_set_id = rule_set.id
                 JOIN dc_security_rule_details rule_details ON rule_details.rule_set_id = rule_set.id
        WHERE rule_set.db_type = 1
          AND rule_details.details_tpl_key = 'zombie_object'
          AND rule_details.value = '1'
          AND conn.is_active = 1
          AND conn.is_delete = 0
    </select>

</mapper>

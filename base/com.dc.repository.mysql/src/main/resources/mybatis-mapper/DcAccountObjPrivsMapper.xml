<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcAccountObjPrivsMapper">
    <insert id="insertIgnore" parameterType="java.util.List">
        INSERT IGNORE INTO dc_account_object_privs (
        id,
        resource_id,
        account_id,
        is_expire,
        privilege_expire,
        gmt_create,
        gmt_modified,
        is_delete,
        grantee,
        object_type,
        catalog_name,
        schema_name,
        object_name,
        privilege,
        grantor,
        grantable,
        hierarchy
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.resourceId},
            #{item.accountId},
            #{item.isExpire},
            #{item.privilegeExpire},
            #{item.gmtCreate},
            #{item.gmtModified},
            #{item.isDelete},
            #{item.grantee},
            #{item.objectType},
            #{item.catalogName},
            #{item.schemaName},
            #{item.objectName},
            #{item.privilege},
            #{item.grantor},
            #{item.grantable},
            #{item.hierarchy}
            )
        </foreach>
    </insert>

    <insert id="replaceInto" parameterType="java.util.List">
        REPLACE INTO dc_account_object_privs (
        id,
        resource_id,
        account_id,
        is_expire,
        privilege_expire,
        gmt_create,
        gmt_modified,
        is_delete,
        grantee,
        object_type,
        catalog_name,
        schema_name,
        object_name,
        privilege,
        grantor,
        grantable,
        hierarchy
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.resourceId},
            #{item.accountId},
            #{item.isExpire},
            #{item.privilegeExpire},
            #{item.gmtCreate},
            #{item.gmtModified},
            #{item.isDelete},
            #{item.grantee},
            #{item.objectType},
            #{item.catalogName},
            #{item.schemaName},
            #{item.objectName},
            #{item.privilege},
            #{item.grantor},
            #{item.grantable},
            #{item.hierarchy}
            )
        </foreach>
    </insert>

    <insert id="replaceIntoNoGrantable" parameterType="java.util.List">
        INSERT INTO dc_account_object_privs (
        id,
        resource_id,
        account_id,
        is_expire,
        privilege_expire,
        gmt_create,
        gmt_modified,
        is_delete,
        grantee,
        object_type,
        catalog_name,
        schema_name,
        object_name,
        privilege,
        grantor,
        grantable,
        hierarchy
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.resourceId},
            #{item.accountId},
            #{item.isExpire},
            #{item.privilegeExpire},
            #{item.gmtCreate},
            #{item.gmtModified},
            #{item.isDelete},
            #{item.grantee},
            #{item.objectType},
            #{item.catalogName},
            #{item.schemaName},
            #{item.objectName},
            #{item.privilege},
            #{item.grantor},
            #{item.grantable},
            #{item.hierarchy}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        resource_id = VALUES(resource_id),
        account_id = VALUES(account_id),
        object_type = VALUES(object_type),
        catalog_name = VALUES(catalog_name),
        schema_name = VALUES(schema_name),
        object_name = VALUES(object_name),
        grantee = VALUES(grantee),
        privilege = VALUES(privilege),
        is_delete = VALUES(is_delete),
        gmt_create = VALUES(gmt_create),
        gmt_modified = VALUES(gmt_modified),
        is_expire = VALUES(is_expire),
        privilege_expire = VALUES(privilege_expire)
    </insert>
</mapper>
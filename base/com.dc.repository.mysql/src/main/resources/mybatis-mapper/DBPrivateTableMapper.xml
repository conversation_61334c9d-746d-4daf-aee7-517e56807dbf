<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DBPrivateTableMapper">

    <select id="getTableCreatorUserId" parameterType="com.dc.repository.mysql.model.DBPrivateTable" resultType="java.lang.String">
        select user_id
        from dc_db_private_table
        where connect_id = #{dto.connectId}
            <if test="dto.catalogName != null">
                and catalog_name = #{dto.catalogName}
            </if>
            and schema_name = #{dto.schemaName}
            and table_name = #{dto.tableName}
            and is_delete = 0
    </select>

    <update id="updateModifyTimeByFiveParam">
        update dc_db_private_table set gmt_modified = #{gmtModified}
        where connect_id = #{connectId}
              <if test="userId != null">and user_id = #{userId}</if>
              <if test="catalogName != null">and catalog_name = #{catalogName}</if>
              and schema_name = #{schemaName}
              and table_name = #{tableName} and is_delete = 0
    </update>



</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.SysOrgMapper">

   <update id="updateBatchById" parameterType="java.util.List">
      <foreach collection="list" separator=";" item="item">
         update dc_sys_org set
         sync_uuid = #{item.syncUuid},
         comment = #{item.comment},
         gmt_modified = #{item.gmtModified}
         where id = #{item.id}
      </foreach>
   </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.OrderExecuteResultMapper">


    <resultMap id="OrderExecuteResultInfo" type="com.dc.repository.mysql.model.OrderExecuteResult">
        <result column="id" property="id"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="is_delete" property="is_delete"/>
        <result column="order_execute_id" property="order_execute_id"/>
        <result column="script_name" property="script_name"/>
        <result column="status" property="status"/>
        <result column="total_time" property="total_time"/>
        <result column="type" property="type"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.gmt_create,
		t.gmt_modified,
		t.order_execute_id,
		t.script_name,
		t.status,
		t.total_time,
        t.type,
        t.execute_fail_reason
    </sql>

    <select id="getFailCount" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT count(0) count
        FROM `work_order_execute_result` AS t
        WHERE t.id = #{id} and t.status = 3
    </select>


    <update id="updateStatus" parameterType="com.dc.repository.mysql.model.OrderExecuteResult">
        UPDATE `work_order_execute_result`
        SET status       = #{status},
            gmt_modified = NOW()
        WHERE order_execute_id = #{order_execute_id}
          <if test="script_name != null ">
              and script_name = #{script_name}
          </if>
        <if test="type != null ">
            and type = #{type}
        </if>
    </update>

    <update id="updateExecuteResult" parameterType="com.dc.repository.mysql.model.OrderExecuteResult">
        UPDATE `work_order_execute_result`
        SET status       = #{status},
            gmt_modified = NOW()
        WHERE order_execute_id = #{order_execute_id}
        <if test="script_name != null ">
            and script_name = #{script_name}
        </if>
        <if test="type != null ">
            and type = #{type}
        </if>
    </update>

    <update id="updateTotalTime" parameterType="com.dc.repository.mysql.model.OrderExecuteResult">
        UPDATE `work_order_execute_result`
        SET total_time   = #{total_time},
            gmt_modified = NOW()
        WHERE id = #{id}
        <if test="type != null ">
            and type = #{type}
        </if>
    </update>

    <select id="getOrderExecuteResult" parameterType="com.dc.repository.mysql.model.OrderExecuteResult"
            resultType="com.dc.repository.mysql.model.OrderExecuteResult">
        select <include refid="Base_Column_List"/>
        from `work_order_execute_result` AS t where
        t.order_execute_id = #{order_execute_id} and t.script_name = #{script_name}
        <if test="type != null ">
            and type = #{type}
        </if>
    </select>

    <select id="selectSuccessScriptByOrderExecuteId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT count(0) count
        FROM `work_order_execute_result` AS t
        WHERE t.order_execute_id = #{order_execute_id} and t.status = 4 and t.type = 1
    </select>

    <select id="selectAllScriptByOrderExecuteId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT count(0) count
        FROM `work_order_execute_result` AS t
        WHERE t.order_execute_id = #{order_execute_id} and t.type = 1
    </select>

    <select id="selectScriptByOrderExecuteIdByStatus" resultType="java.lang.Integer">
        SELECT count(0) count
        FROM `work_order_execute_result` AS t
        WHERE t.order_execute_id = #{order_execute_id} and t.status = #{status} and t.type = 1
    </select>

    <update id="updateExecuteFailReason" parameterType="com.dc.repository.mysql.model.OrderExecuteResult">
        UPDATE `work_order_execute_result`
        SET execute_fail_reason = #{execute_fail_reason},gmt_modified = NOW()
        <where>
            order_execute_id = #{order_execute_id}
            <if test="script_name != null ">
                and script_name = #{script_name}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
    </update>

</mapper>
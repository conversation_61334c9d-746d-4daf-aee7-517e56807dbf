<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.PaUserMapper">

    <select id="getAllOrg" resultType="com.dc.repository.mysql.model.PaUser" >
        SELECT distinct bu_id as buId,bu_id_short_desc as buIdShortDesc
        FROM pa_user
    </select>

    <select id="getUsersByList" resultType="com.dc.repository.mysql.model.PaUser" parameterType="java.util.Map">
        SELECT um,bu_id as buId,bu_id_short_desc as buIdShortDesc
        FROM pa_user
        where um in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getOrgByUsername" resultType="com.dc.repository.mysql.model.SysOrg" parameterType="java.lang.String">
        SELECT org.unique_key as uniqueKey, org.org_ids as orgIds
        FROM pa_user user inner join dc_sys_org org on user.bu_id = org.sync_uuid
        where user.um = #{username} limit 1
    </select>

    <update id="updateBatchById" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update pa_user set
            bu_id = #{item.buId},
            bu_id_short_desc = #{item.buIdShortDesc},
            chr_out_date = #{item.chrOutDate},
            emp_status = #{item.empStatus},
            emp_status_desc = #{item.empStatusDesc},
            paic_leave_date = #{item.paicLeaveDate},
            gmt_modified = #{item.gmtModified}
            where id = #{item.id}
        </foreach>
    </update>

</mapper>

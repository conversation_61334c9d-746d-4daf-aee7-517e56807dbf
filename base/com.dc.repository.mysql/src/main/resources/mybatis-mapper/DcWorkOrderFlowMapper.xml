<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcWorkOrderFlowMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.DcWorkOrderFlow">
        <result column="id" property="id"/>
        <result column="order_id" property="order_id"/>
        <result column="node_id" property="node_id"/>
        <result column="action_id" property="action_id"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.order_id,
		t.node_id,
		t.action_id,
		t.is_delete,
		t.gmt_create,
		t.gmt_modified
    </sql>

    <insert id="save" parameterType="com.dc.repository.mysql.model.DcWorkOrderFlow" keyColumn="id"
            useGeneratedKeys="true" keyProperty="id">
        insert into `dc_work_order_flow` (`order_id`,`node_id`) values (#{order_id},#{node_id});
    </insert>

    <select id="selectCnt" parameterType="com.dc.repository.mysql.model.DcWorkOrderFlow" resultType="int">
        select count(0)
        from `dc_work_order_flow`
        where order_id = #{order_id}
          and node_id = #{node_id} for update
    </select>

</mapper>
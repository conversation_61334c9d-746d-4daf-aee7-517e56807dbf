<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.OrderDownloadFileMapper">

    <resultMap id="OrderDownloadFile" type="com.dc.repository.mysql.model.OrderDownloadFile">
        <result column="file_id" property="fileId"/>
        <result column="file_name" property="fileName"/>
        <result column="file_path" property="filePath"/>
        <result column="file_size" property="fileSize"/>
        <result column="download_count" property="downloadCount"/>
        <result column="download_rule" property="downloadRule"/>
        <result column="relation_id" property="relationId"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.file_id,
		t.file_name,
		t.file_path,
		t.file_size,
		t.download_count,
		t.download_rule,
		t.relation_id,
		t.gmt_create,
		t.gmt_modified
    </sql>

    <insert id="saveDownloadFile" parameterType="com.dc.repository.mysql.model.OrderDownloadFile" keyColumn="file_id"
            useGeneratedKeys="true" keyProperty="fileId">
        insert into `dc_download_files` (file_name, file_path, download_rule, relation_id, gmt_create, gmt_modified) values (#{fileName}, #{filePath}, #{downloadRule}, #{relationId}, now(6), now(6))
    </insert>


</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.PaUserSuperiorMapper">

    <select id="getUsersByList" resultType="com.dc.repository.mysql.model.PaUserSuperior" parameterType="java.util.Map">
        SELECT emp_um as empUm, id
        FROM pa_user_superior
        where emp_um in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateBatchById" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update pa_user_superior set
            emp_um = #{item.empUm},
            emp_id = #{item.empId},
            chr_out_date = #{item.chrOutDate},
            report_um = #{item.reportUm},
            report_emp_id = #{item.reportEmpId},
            gmt_modified = #{item.gmtModified}
            where id = #{item.id}
        </foreach>
    </update>

</mapper>

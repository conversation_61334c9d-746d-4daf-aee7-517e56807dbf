<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.CustomizationConfigRelationMapper">

    <select id="getInstanceByType" parameterType="java.lang.String" resultType="java.lang.String">
        select res_id from dc_customization_config_relation cr
        INNER JOIN dc_customization_config c on cr.config_id = c.id and c.name_key = #{type}
    </select>


</mapper>

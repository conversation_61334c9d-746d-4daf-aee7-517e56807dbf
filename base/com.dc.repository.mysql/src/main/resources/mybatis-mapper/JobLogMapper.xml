<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.JobLogMapper">

	<resultMap id="XxlJobLog" type="com.dc.repository.mysql.model.JobLog" >
		<result column="id" property="id" />

		<result column="job_group" property="jobGroup" />
		<result column="job_id" property="jobId" />

		<result column="executor_address" property="executorAddress" />
		<result column="executor_handler" property="executorHandler" />
		<result column="executor_param" property="executorParam" />
		<result column="executor_sharding_param" property="executorShardingParam" />
		<result column="executor_fail_retry_count" property="executorFailRetryCount" />

		<result column="trigger_time" property="triggerTime" />
		<result column="trigger_code" property="triggerCode" />
		<result column="trigger_msg" property="triggerMsg" />

		<result column="handle_time" property="handleTime" />
		<result column="handle_code" property="handleCode" />
		<result column="handle_msg" property="handleMsg" />

		<result column="alarm_status" property="alarmStatus" />

		<result column="trigger_count" property="triggerCount" />
		<result column="trigger_status" property="triggerStatus" />
		<result column="file_download_link" property="fileDownloadLink" />
		<result column="executor_tomcat_address" property="executorTomcatAddress" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.job_group,
		t.job_id,
		t.executor_address,
		t.executor_handler,
		t.executor_param,
		t.executor_sharding_param,
		t.executor_fail_retry_count,
		t.trigger_time,
		t.trigger_code,
		t.trigger_msg,
		t.handle_time,
		t.handle_code,
		t.handle_msg,
		t.alarm_status,
        t.trigger_count,
        t.trigger_status,
        t.file_download_link,
        t.executor_tomcat_address
	</sql>

	<update id="updateTriggerStatus" >
		UPDATE xxl_job_log
		SET
			`trigger_status` = #{triggerStatus},
			`trigger_end_time`= #{triggerEndTime}
		WHERE `id`= #{logId}
	</update>

	<update id="updateTriggerTime" >
		UPDATE xxl_job_log
		SET
			`trigger_time`= #{triggerTime}
		WHERE `id`= #{logId}
	</update>

	<update id="updateFileDownloadLink" >
		UPDATE xxl_job_log
		SET
			`file_download_link` = #{fileDownloadLink}
		WHERE `id`= #{logId}
	</update>

	<update id="updateBeginTriggerStatus" >
		UPDATE xxl_job_log
		SET
			`trigger_status` = #{triggerStatus},
			`trigger_count` = #{triggerCount}
		WHERE `id`= #{logId}
	</update>

	<update id="updateExecutorTomcatAddress" >
		UPDATE xxl_job_log
		SET
			`executor_tomcat_address` = #{executorTomcatAddress}
		WHERE `id`= #{logId}
	</update>

	<select id="findJobTriggerCount" resultType="int" >
		SELECT
			count(1)
		FROM xxl_job_log
		WHERE job_id = #{jobId}
		  AND trigger_status >0
	</select>

	<select id="load" parameterType="java.lang.Long" resultMap="XxlJobLog">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_log AS t
		WHERE t.id = #{id}
	</select>

	<select id="loadByJobId" parameterType="java.lang.Integer" resultMap="XxlJobLog">
		SELECT
		<include refid="Base_Column_List"/>
		FROM xxl_job_log AS t
		WHERE t.job_id = #{jobId}
		ORDER BY id DESC
		LIMIT 1
	</select>

</mapper>

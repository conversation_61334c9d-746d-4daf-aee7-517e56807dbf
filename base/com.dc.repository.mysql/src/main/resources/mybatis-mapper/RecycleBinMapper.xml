<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.RecycleBinMapper">

    <select id="getRecycleBinByConnectionId" resultType="com.dc.repository.mysql.model.RecycleBin" parameterType="java.lang.String">
        select rec.*
        from dc_db_recycle_bin rec
        where rec.`type` = 'conn'
          and rec.is_active = 1
          and rec.is_delete = 0
          and rec.object_id = #{connect_id}
        limit 1
    </select>

    <select id="getRecycleBinBySchemaId" resultType="com.dc.repository.mysql.model.RecycleBin" parameterType="java.lang.String" >
        select rec.*
        from dc_db_recycle_bin rec
        where rec.`type` = 'schema'
          and rec.is_active = 1
          and rec.is_delete = 0
          and rec.object_id = #{schema_id}
        limit 1
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.UserLimitMapper">

    <sql id="Base_Column_List">
        t.id,
		t.pd_key,
		t.pd_value,
		t.uid,
		t.action,
		t.comment,
        t.gmt_create,
		t.gmt_modified,
		t.is_delete
    </sql>

    <select id="getUserLimit" resultType="com.dc.repository.mysql.model.UserLimit" >
        select <include refid="Base_Column_List" />
        from dc_sys_user_config t
        where t.uid = #{id}
        and t.is_delete = 0
    </select>

    <select id="getUserLimitByPdKey" resultType="com.dc.repository.mysql.model.UserLimit" >
        select <include refid="Base_Column_List" />
        from dc_sys_user_config t
        where t.uid = #{id}
            and t.pd_key = #{pd_key}
            and t.is_delete = 0
    </select>

</mapper>
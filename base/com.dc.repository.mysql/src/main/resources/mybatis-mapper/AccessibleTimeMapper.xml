<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.AccessibleTimeMapper">

    <select id="getInstanceAccessibleTime" resultType="com.dc.repository.mysql.model.AccessibleTime" parameterType="map" >
        select acc.*
        from dc_db_accessible_time acc
        where acc.object_type = 'connect'
          and acc.connect_id = #{con_id}
          and acc.is_active = 1
          and acc.is_delete = 0
        limit 1
    </select>

    <select id="getSchemasAccessibleTime" resultType="com.dc.repository.mysql.model.AccessibleTime" parameterType="map" >
        select acc.*
        from dc_db_accessible_time acc
        where acc.object_type = 'schema'
          and acc.connect_id = #{con_id}
          and acc.is_active = 1
          and acc.is_delete = 0
          and acc.schema_id in
            <foreach collection="schema_ids" item="schema_id" index="index" open="(" close=")" separator=",">
                #{schema_id}
            </foreach>
    </select>

    <select id="getTablesAccessibleTime" resultType="com.dc.repository.mysql.model.AccessibleTime" parameterType="map" >
        select acc.*
        from dc_db_accessible_time acc
        where acc.object_type = 'table'
          and acc.connect_id = #{con_id}
          and acc.is_active = 1
          and acc.is_delete = 0
          and acc.schema_id in
            <foreach collection="schema_ids" item="schema_id" index="index" open="(" close=")" separator=",">
                #{schema_id}
            </foreach>
          and acc.object_name in
            <foreach collection="table_names" item="table_name" index="index" open="(" close=")" separator=",">
                #{table_name}
            </foreach>
    </select>

    <resultMap id="AccessibleTimesMap" type="com.dc.repository.mysql.model.AccessibleTime">
        <id property="id" column="t_id"/>

        <!-- 基础字段映射 -->
        <result property="connect_id" column="connect_id"/>
        <result property="schema_id" column="schema_id"/>
        <result property="object_id" column="object_id"/>
        <result property="object_name" column="object_name"/>
        <result property="object_type" column="object_type"/>
        <result property="operator_id" column="operator_id"/>
        <result property="is_active" column="is_active"/>
        <result property="gmt_create" column="t_gmt_create"/>
        <result property="gmt_modified" column="t_gmt_modified"/>
        <result property="is_delete" column="t_is_delete"/>

        <!-- 多对多映射规则集合 -->
        <collection property="accessibleRules" ofType="com.dc.repository.mysql.model.AccessibleRule">
            <id property="id" column="ar_id"/>
            <result property="codeNum" column="code_num"/>
            <result property="ip" column="ip"/>
            <result property="operations" column="operations"/>
            <result property="type" column="type"/>
            <result property="accessibleTime" column="accessible_time"/>
            <result property="description" column="description"/>
            <result property="gmtCreate" column="ar_gmt_create"/>
            <result property="gmtModified" column="ar_gmt_modified"/>
            <result property="isDelete" column="ar_is_delete"/>
        </collection>

    </resultMap>

    <sql id="whichTableName">
        dc_db_accessible_time
    </sql>

    <sql id="common">
        SELECT
            t.id AS t_id,
            t.connect_id,
            t.schema_id,
            t.object_id,
            t.object_name,
            t.object_type,
            t.operator_id,
            t.is_active,
            t.gmt_create AS t_gmt_create,
            t.gmt_modified AS t_gmt_modified,
            t.is_delete AS t_is_delete,

            ar.id AS ar_id,
            ar.code_num,
            ar.ip,
            ar.operations,
            ar.type,
            ar.accessible_time,
            ar.description,
            ar.gmt_create AS ar_gmt_create,
            ar.gmt_modified AS ar_gmt_modified,
            ar.is_delete AS ar_is_delete

        FROM <include refid="whichTableName"/> t
            LEFT JOIN dc_db_accessible_time_rule atr
                ON t.id = atr.accessible_id
                AND atr.is_delete = 0
            LEFT JOIN dc_db_accessible_rule ar
                ON atr.rule_id = ar.id
                AND ar.is_delete = 0
    </sql>

    <select id="getInstanceAccessibleTimes" resultMap="AccessibleTimesMap">
        SELECT
            t.id AS t_id,
            t.connect_id,
            t.schema_id,
            t.object_id,
            t.object_name,
            t.object_type,
            t.operator_id,
            t.is_active,
            t.gmt_create AS t_gmt_create,
            t.gmt_modified AS t_gmt_modified,
            t.is_delete AS t_is_delete,

            ar.id AS ar_id,
            ar.code_num,
            ar.ip,
            ar.operations,
            ar.type,
            ar.accessible_time,
            ar.description,
            ar.gmt_create AS ar_gmt_create,
            ar.gmt_modified AS ar_gmt_modified,
            ar.is_delete AS ar_is_delete

        FROM <include refid="whichTableName"/> t
        LEFT JOIN dc_db_accessible_time_rule atr
        ON t.id = atr.accessible_id
            AND atr.is_delete = 0
            LEFT JOIN dc_db_accessible_rule ar
            ON atr.rule_id = ar.id
            AND ar.is_delete = 0
        WHERE t.connect_id = #{con_id} AND t.object_type = 'connect' AND t.is_active = 1 AND t.is_delete = 0
    </select>

    <select id="getSchemaAccessibleTimes" resultMap="AccessibleTimesMap">
        <include refid="common"/>
        WHERE t.connect_id = #{con_id}
            AND t.schema_id in
            <foreach collection="schema_ids" item="schema_id" open="(" close=")" separator=",">
                #{schema_id}
            </foreach>
            AND t.object_type = 'schema'
            AND t.is_active = 1
            AND t.is_delete = 0
    </select>

    <select id="getTableAccessibleTimes" resultMap="AccessibleTimesMap">
        <include refid="common"/>
        WHERE t.connect_id = #{con_id}
            AND t.schema_id in
            <foreach collection="schema_ids" item="schema_id" open="(" close=")" separator=",">
                #{schema_id}
            </foreach>
            AND t.object_name in
            <foreach collection="table_names" item="table_name" open="(" close=")" separator=",">
                #{table_name}
            </foreach>
            AND t.object_type = 'table'
            AND t.is_active = 1
            AND t.is_delete = 0
    </select>

</mapper>
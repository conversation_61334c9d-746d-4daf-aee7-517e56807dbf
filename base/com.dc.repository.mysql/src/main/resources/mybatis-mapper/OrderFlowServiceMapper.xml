<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.OrderFlowServiceMapper">


    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.OrderFlowService">
        <result column="id" property="id"/>
        <result column="work_order_type_key" property="work_order_type_key"/>
        <result column="work_order_type_name" property="work_order_type_name"/>
        <result column="service_name" property="service_name"/>
        <result column="service_key" property="service_key"/>
        <result column="service_info" property="service_info"/>
        <result column="sequence" property="sequence"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
    </resultMap>


    <sql id="Base_Column_List">
        t.id,
		t.work_order_type_key,
		t.work_order_type_name,
		t.service_name,
		t.service_key,
		t.service_info,
		t.sequence,
		t.is_delete,
		t.gmt_create,
		t.gmt_modified
    </sql>

    <select id="getKeyById" resultType="java.lang.String" parameterType="java.lang.Integer">
        SELECT t.work_order_type_key
        FROM `work_order_flow_service` AS t
        WHERE t.id = #{id}
    </select>


</mapper>
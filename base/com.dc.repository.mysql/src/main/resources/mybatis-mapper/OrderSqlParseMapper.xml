<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.OrderSqlParserMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.OrderSqlParse">
        <result column="id" property="id"/>
        <result column="is_delete" property="isDelete"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="order_id" property="order_id"/>
        <result column="script_name" property="script_name"/>
        <result column="sql_content" property="sql_content"/>
        <result column="sql_type" property="sql_type"/>
        <result column="verify_status" property="verify_status"/>
        <result column="verify_reason" property="verify_reason"/>
        <result column="execute_status" property="execute_status"/>
        <result column="is_skip" property="is_skip"/>
        <result column="log" property="log"/>
        <result column="line_number" property="line_number"/>
        <result column="affected_rows" property="affected_rows"/>
        <result column="file_path" property="file_path"/>
        <result column="type" property="type"/>
        <result column="backup_model" property="backup_model"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.is_delete,
        t.gmt_create,
        t.gmt_modified,
        t.order_id,
        t.script_name,
        t.sql_content,
        t.sql_type,
        t.verify_status,
        t.verify_reason,
        t.execute_status,
        t.is_skip,
        t.log,
        t.line_number,
        t.affected_rows,
        t.file_path,
        t.type,
        t.backup_model,
        t.privilege_model,
        t.privilege_expire,
        t.order_schema_task_id
    </sql>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(1) from order_sql_parse t
        <where>
            t.order_id =#{order_id}
            and t.script_name = #{script_name}
            <if test="order_schema_task_id != null">
                and t.order_schema_task_id = #{order_schema_task_id}
            </if>
            and t.type = #{type}
            <if test='execute_status != null and execute_status != ""'>
            and t.execute_status != #{execute_status}
            </if>
            and t.is_skip = #{is_skip}
        </where>

    </select>

    <select id="query" resultMap="OrderInfo" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from order_sql_parse t
        <where>
            t.order_id =#{order_id}
            and t.script_name = #{script_name}
            <if test='order_schema_task_id != null'>
                and t.order_schema_task_id = #{order_schema_task_id}
            </if>
            and t.type = #{type}
            <if test='execute_status != null and execute_status != ""'>
            and t.execute_status != #{execute_status}
            </if>
            and t.is_skip = #{is_skip}
        </where>

        <!-- 多线程执行顺序 -->
        order by line_number asc

        <if test="null != limit and null != offset ">
            limit #{offset}, #{limit}
        </if>

    </select>

    <select id="queryByEqual" resultMap="OrderInfo" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from order_sql_parse t
        <where>
            t.order_id =#{order_id}
            and t.script_name = #{script_name}
            <if test="order_schema_task_id != null">
                and t.order_schema_task_id = #{order_schema_task_id}
            </if>
            and t.execute_status = #{execute_status}
            and t.line_number &lt; #{line_number}
            and t.is_skip = #{is_skip}
        </where>

        <!-- 多线程执行顺序 -->
        order by line_number asc

        <if test="null != limit and null != offset ">
            limit #{offset}, #{limit}
        </if>

    </select>


    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update order_sql_parse t
            <set>
                <if test="null != item.execute_status">
                    t.execute_status = #{item.execute_status},
                </if>
                <if test="null != item.log">
                    t.log = #{item.log},
                </if>
                <if test="null != item.gmtModified">
                    t.gmt_modified = #{item.gmtModified},
                </if>
                <if test="null != item.verify_reason">
                    t.verify_reason = #{item.verify_reason},
                </if>
                <if test="null != item.file_path">
                    t.file_path = #{item.file_path},
                </if>
                <if test="null != item.backup_warning">
                    t.backup_warning = #{item.backup_warning},
                </if>
            </set>
            where t.id = #{item.id}
        </foreach>
    </update>

    <update id="update" parameterType="com.dc.repository.mysql.model.OrderSqlParse">
        update order_sql_parse t
        <set>
            <if test="null != execute_status">
                t.execute_status = #{execute_status},
            </if>
            <if test="null != log">
                t.log = #{log},
            </if>
            <if test="null != gmtModified">
                t.gmt_modified = #{gmtModified},
            </if>
            <if test="null != verify_reason">
                t.verify_reason = #{verify_reason},
            </if>
            <if test="null != file_path">
                t.file_path = #{file_path},
            </if>
        </set>
        where t.id = #{id}
    </update>

    <update id="updateAllPauseSqlParse" parameterType="com.dc.repository.mysql.model.OrderSqlParse">
        update order_sql_parse t
        <set>
            t.execute_status = 5,
            <if test="null != gmtModified">
                t.gmt_modified = #{gmtModified},
            </if>
        </set>
        where t.order_id = #{order_id}
        and t.script_name = #{script_name}
        <if test="null != order_schema_task_id">
            and t.order_schema_task_id = #{order_schema_task_id}
        </if>
        and t.type = #{type}
        and t.execute_status = 3
    </update>

    <update id="renewOrderSqlParser" parameterType="com.dc.repository.mysql.model.OrderSqlParse">
        update order_sql_parse t
        <set>
            t.execute_status = 3,
            t.log = '',
            t.file_path = '',
            <if test="null != gmtModified">
                t.gmt_modified = #{gmtModified},
            </if>
        </set>
        where t.order_id = #{order_id}
        and t.script_name = #{script_name}
        <if test="order_schema_task_id != null">
            and t.order_schema_task_id = #{order_schema_task_id}
        </if>
        and t.type = #{type}
        and t.is_skip = 0
    </update>

    <update id="updateLogAndExecuteStatus" parameterType="com.dc.repository.mysql.model.OrderSqlParse">
        update order_sql_parse t
        <set>
            t.execute_status = #{execute_status},
            t.log = #{log},
            <if test="null != gmtModified">
                t.gmt_modified = #{gmtModified},
            </if>
        </set>
        <where>
            where t.order_id = #{order_id}
            and t.script_name = #{script_name}
            <if test="order_schema_task_id != null">
                and t.order_schema_task_id = #{order_schema_task_id}
            </if>
            and t.type = #{type}
            and t.is_skip = #{is_skip}
        </where>

    </update>


</mapper>
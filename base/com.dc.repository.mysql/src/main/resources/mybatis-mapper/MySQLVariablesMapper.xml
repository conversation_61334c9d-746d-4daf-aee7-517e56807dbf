<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.MySQLVariablesMapper">

	<resultMap id="Variables" type="com.dc.repository.mysql.model.MySQLVariables" >
		<result column="Variable_name" property="variableName" />
		<result column="Value" property="value" />
	</resultMap>

	<select id="showMaxAllowedPacket" resultMap="Variables">
		SHOW VARIABLES LIKE 'max_allowed_packet';
	</select>

</mapper>
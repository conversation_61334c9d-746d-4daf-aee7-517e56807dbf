<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcConnectionAttemptsMapper">

    <select id="getConnectionAttempts" resultType="java.util.Map">
        SELECT
        dc_db_connection.instance_name as instanceName,
        dc_db_connection.unique_key as uniqueKey,
        dc_connection_attempts.is_success AS successCode,
        count( dc_connection_attempts.is_success ) AS Count
        FROM
        dc_connection_attempts
        LEFT JOIN dc_db_connection ON dc_connection_attempts.connect_id = dc_db_connection.unique_key
        WHERE
        dc_connection_attempts.is_delete = 0

        <if test="instanceUUID!=null">
            AND dc_db_connection.sync = #{instanceUUID}
        </if>
        AND dc_connection_attempts.gmt_create BETWEEN #{startDate} and #{endDate}
        GROUP BY
        dc_connection_attempts.is_success
    </select>
</mapper>
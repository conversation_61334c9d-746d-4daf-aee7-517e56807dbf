<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcWorkOrderStepUserMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.DcWorkOrderStepUser">
        <result column="id" property="id"/>
        <result column="step_id" property="step_id"/>
        <result column="user_id" property="user_id"/>
        <result column="username" property="username"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.step_id,
		t.user_id,
		t.username,
		t.is_delete,
		t.gmt_create,
		t.gmt_modified
    </sql>

    <select id="getStepUserById" parameterType="java.lang.Integer"
            resultType="com.dc.repository.mysql.model.DcWorkOrderStepUser">
        select u.user_id,u.username from dc_work_order_step step
        left join dc_work_order_step_user u on step.id = u.step_id
        where order_id=#{orderId} and step=1
    </select>

</mapper>
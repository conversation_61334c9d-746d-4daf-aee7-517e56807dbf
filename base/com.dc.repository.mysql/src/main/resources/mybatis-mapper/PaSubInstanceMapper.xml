<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.PaSubInstanceMapper">

    <resultMap id="subInstanceResultMap" type="com.dc.repository.mysql.model.PaSubInstance" >
        <result column="id" property="id" />
        <result column="instance_name" property="instanceName" />
        <result column="domain_name" property="domainName" />
        <result column="vip" property="vip" />
        <result column="port" property="port" />
        <result column="default_role" property="defaultRole" />
        <result column="database_version" property="databaseVersion" />
        <result column="status" property="status" />
        <result column="host_ip" property="hostIp" />
        <result column="host_port" property="hostPort" />
        <result column="system_name" property="systemName" />
        <result column="instance_uuid" property="instanceUuid" />
        <result column="parent_instance_id" property="parentInstanceId" />
        <result column="entity_uuid" property="entityUuid" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <select id="getByEntityUuid" resultMap="subInstanceResultMap" parameterType="java.util.Map">
        SELECT *
        FROM pa_sub_instance
        where entity_uuid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <update id="updateBatchById" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update pa_sub_instance set
            instance_name = #{item.instanceName},
            domain_name = #{item.domainName},
            vip = #{item.vip},
            default_role = #{item.defaultRole},
            database_version = #{item.databaseVersion},
            host_ip = #{item.hostIp},
            host_port = #{item.hostPort},
            status = #{item.status},
            port = #{item.port},
            system_name = #{item.systemName},
            instance_uuid = #{item.instanceUuid},
            parent_instance_id = #{item.parentInstanceId},
            entity_uuid = #{item.entityUuid},
            gmt_modified = #{item.gmtModified}
            where id = #{item.id}
        </foreach>
    </update>

</mapper>

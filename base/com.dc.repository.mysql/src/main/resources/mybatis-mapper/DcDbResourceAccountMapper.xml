<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcDbResourceAccountMapper">


    <!-- DcDbResourceAccount 的 ResultMap -->
    <resultMap id="dcDbResourceAccountMap" type="com.dc.repository.mysql.model.DcDbResourceAccount">
        <id column="id" property="id"/>
        <result column="unique_key" property="uniqueKey"/>
        <result column="verified" property="verified"/>
        <result column="password" property="password"/>
        <result column="username" property="username"/>
        <result column="connection" property="connection"/>
        <result column="connect_id" property="connectId"/>
        <result column="resource_id" property="resourceId"/>
        <result column="verify_time" property="verifyTime"/>
        <result column="password_update_time" property="passwordUpdateTime"/>
        <result column="period" property="period"/>
        <result column="user_type" property="userType"/>
        <result column="password_strength" property="passwordStrength"/>
        <result column="timing_time" property="timingTime"/>
        <result column="user_source_type" property="userSourceType"/>
        <result column="creator_id" property="creatorId"/>
        <result column="driver_id" property="driverId"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="is_delete" property="isDelete"/>
        <result column="is_main" property="isMain"/>
        <result column="db_role" property="dbRole"/>
        <result column="host" property="host"/>
        <result column="privilege_sync_state" property="privilegeSyncState"/>
        <!-- 关联 DcDbResource，使用嵌套结果映射 -->
        <association property="resource" javaType="com.dc.repository.mysql.model.DcDbResource"
                     resultMap="dcDbResourceMap"/>
    </resultMap>

    <!-- 定义用于连表查询的ResultMap -->
    <resultMap id="accountWithResourceMap" type="com.dc.repository.mysql.model.DcDbResourceAccount"
               extends="dcDbResourceAccountMap">
        <association property="resource" javaType="com.dc.repository.mysql.model.DcDbResource">
            <id column="r_id" property="id"/>
            <result column="r_unique_key" property="unique_key"/>
            <result column="r_resource_name" property="resource_name"/>
            <result column="r_resource_type" property="resource_type"/>
            <result column="r_db_type" property="db_type"/>
            <result column="r_ip" property="ip"/>
            <result column="r_port" property="port"/>
            <result column="r_executor" property="executor"/>
            <result column="r_driver_id" property="driver_id"/>
            <result column="r_connect_type" property="connect_type"/>
            <result column="r_username" property="username"/>
            <result column="r_password" property="password"/>
            <result column="r_db_role" property="db_role"/>
            <result column="r_service_name" property="service_name"/>
            <result column="r_driver_properties" property="driver_properties"/>
            <result column="r_connection_desc" property="connection_desc"/>
            <result column="r_connection" property="connection"/>
            <result column="r_connect_id" property="connect_id"/>
            <result column="r_category_id" property="category_id"/>
            <result column="r_creator_id" property="creator_id"/>
            <result column="r_comment" property="comment"/>
            <result column="r_is_delete" property="is_delete"/>
            <result column="r_gmt_create" property="gmt_create"/>
            <result column="r_gmt_modified" property="gmt_modified"/>
            <result column="r_environment" property="environment"/>
            <result column="r_security_rule_set_id" property="security_rule_set_id"/>
            <result column="r_entity" property="entity"/>
            <result column="r_connections_limit" property="connections_limit"/>
            <result column="r_pattern" property="pattern"/>
            <result column="r_commit_pattern" property="commit_pattern"/>
            <result column="r_label_id" property="label_id"/>
            <result column="r_domain" property="domain"/>
            <result column="r_cluster" property="cluster"/>
            <result column="r_tenant" property="tenant"/>
            <result column="r_order_code" property="order_code"/>
        </association>
    </resultMap>

    <!-- DcDbResource 的 ResultMap -->
    <resultMap id="dcDbResourceMap" type="com.dc.repository.mysql.model.DcDbResource">
        <id column="id" property="id"/>
        <result column="unique_key" property="unique_key"/>
        <result column="resource_name" property="resource_name"/>
        <result column="resource_type" property="resource_type"/>
        <result column="db_type" property="db_type"/>
        <result column="ip" property="ip"/>
        <result column="port" property="port"/>
        <result column="executor" property="executor"/>
        <result column="driver_id" property="driver_id"/>
        <result column="connect_type" property="connect_type"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="db_role" property="db_role"/>
        <result column="service_name" property="service_name"/>
        <result column="driver_properties" property="driver_properties"/>
        <result column="connection_desc" property="connection_desc"/>
        <result column="connection" property="connection"/>
        <result column="connect_id" property="connect_id"/>
        <result column="category_id" property="category_id"/>
        <result column="creator_id" property="creator_id"/>
        <result column="comment" property="comment"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="environment" property="environment"/>
        <result column="security_rule_set_id" property="security_rule_set_id"/>
        <result column="entity" property="entity"/>
        <result column="connections_limit" property="connections_limit"/>
        <result column="pattern" property="pattern"/>
        <result column="commit_pattern" property="commit_pattern"/>
        <result column="label_id" property="label_id"/>
        <result column="domain" property="domain"/>
        <result column="cluster" property="cluster"/>
        <result column="tenant" property="tenant"/>
        <result column="order_code" property="order_code"/>
    </resultMap>

    <!-- 基于ID查询资源 -->
    <select id="selectResourceByUniqueKey" resultMap="dcDbResourceMap">
        SELECT *
        FROM dc_db_resource
        WHERE unique_key = #{resourceId}
    </select>

    <!-- 连表查询：根据账户唯一键查询账户和关联的资源信息 -->
    <select id="selectAccountWithResourceByUniqueKeys" resultMap="accountWithResourceMap">
        SELECT
        a.id,
        a.unique_key,
        a.verified,
        a.password,
        a.username,
        a.connection,
        a.connect_id,
        a.resource_id,
        a.verify_time,
        a.password_update_time,
        a.period,
        a.user_type,
        a.password_strength,
        a.timing_time,
        a.user_source_type,
        a.creator_id,
        a.driver_id,
        a.gmt_create,
        a.gmt_modified,
        a.is_delete,
        a.is_main,
        a.db_role,
        a.host,
        a.privilege_sync_state,
        r.id AS r_id,
        r.unique_key AS r_unique_key,
        r.resource_name AS r_resource_name,
        r.resource_type AS r_resource_type,
        r.db_type AS r_db_type,
        r.ip AS r_ip,
        r.port AS r_port,
        r.executor AS r_executor,
        r.driver_id AS r_driver_id,
        r.connect_type AS r_connect_type,
        r.username AS r_username,
        r.password AS r_password,
        r.db_role AS r_db_role,
        r.service_name AS r_service_name,
        r.driver_properties AS r_driver_properties,
        r.connection_desc AS r_connection_desc,
        r.connection AS r_connection,
        r.connect_id AS r_connect_id,
        r.category_id AS r_category_id,
        r.creator_id AS r_creator_id,
        r.comment AS r_comment,
        r.is_delete AS r_is_delete,
        r.gmt_create AS r_gmt_create,
        r.gmt_modified AS r_gmt_modified,
        r.environment AS r_environment,
        r.security_rule_set_id AS r_security_rule_set_id,
        r.entity AS r_entity,
        r.connections_limit AS r_connections_limit,
        r.pattern AS r_pattern,
        r.commit_pattern AS r_commit_pattern,
        r.label_id AS r_label_id,
        r.domain AS r_domain,
        r.cluster AS r_cluster,
        r.tenant AS r_tenant,
        r.order_code AS r_order_code
        FROM dc_db_resource_account a
        LEFT JOIN dc_db_resource r ON a.resource_id = r.unique_key
        WHERE a.unique_key IN
        <foreach collection="uniqueKeys" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
    </select>

    <!-- 添加一个单个账户的连表查询 -->
    <select id="selectAccountWithResourceByUniqueKey" resultMap="accountWithResourceMap">
        SELECT a.id,
               a.unique_key,
               a.verified,
               a.password,
               a.username,
               a.connection,
               a.connect_id,
               a.resource_id,
               a.verify_time,
               a.password_update_time,
               a.period,
               a.user_type,
               a.password_strength,
               a.timing_time,
               a.user_source_type,
               a.creator_id,
               a.driver_id,
               a.gmt_create,
               a.gmt_modified,
               a.is_delete,
               a.is_main,
               a.db_role,
               a.host,
               a.privilege_sync_state,
               r.id                   AS r_id,
               r.unique_key           AS r_unique_key,
               r.resource_name        AS r_resource_name,
               r.resource_type        AS r_resource_type,
               r.db_type              AS r_db_type,
               r.ip                   AS r_ip,
               r.port                 AS r_port,
               r.executor             AS r_executor,
               r.driver_id            AS r_driver_id,
               r.connect_type         AS r_connect_type,
               r.username             AS r_username,
               r.password             AS r_password,
               r.db_role              AS r_db_role,
               r.service_name         AS r_service_name,
               r.driver_properties    AS r_driver_properties,
               r.connection_desc      AS r_connection_desc,
               r.connection           AS r_connection,
               r.connect_id           AS r_connect_id,
               r.category_id          AS r_category_id,
               r.creator_id           AS r_creator_id,
               r.comment              AS r_comment,
               r.is_delete            AS r_is_delete,
               r.gmt_create           AS r_gmt_create,
               r.gmt_modified         AS r_gmt_modified,
               r.environment          AS r_environment,
               r.security_rule_set_id AS r_security_rule_set_id,
               r.entity               AS r_entity,
               r.connections_limit    AS r_connections_limit,
               r.pattern              AS r_pattern,
               r.commit_pattern       AS r_commit_pattern,
               r.label_id             AS r_label_id,
               r.domain               AS r_domain,
               r.cluster              AS r_cluster,
               r.tenant               AS r_tenant,
               r.order_code           AS r_order_code
        FROM dc_db_resource_account a
                 LEFT JOIN dc_db_resource r ON a.resource_id = r.unique_key
        WHERE a.unique_key = #{uniqueKey}
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.OrderSchemaTaskMapper">

    <resultMap id="OrderSchemaTaskResultMap" type="com.dc.repository.mysql.model.OrderSchemaTask">
        <id column="t1_id" property="id"/>
        <result column="order_id" property="order_id"/>
        <result column="schema_id" property="schema_id"/>
        <result column="schema_status" property="schema_status"/>
        <result column="environment" property="environment"/>
        <result column="affected_rows" property="affected_rows"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="consumer_token_list" property="consumer_token_list"/>
        <result column="producer_token" property="producer_token"/>
        <result column="check_fail_reason" property="check_fail_reason"/>

        <association property="orderExecute" javaType="com.dc.repository.mysql.model.OrderExecute">
            <id column="w_id" property="id"/>
            <result column="w_order_id" property="order_id"/>
            <result column="current_status" property="current_status"/>
            <result column="execute_type" property="execute_type"/>
            <result column="apply_user_id" property="apply_user_id"/>
            <result column="user_id" property="user_id"/>
            <result column="available_at" property="available_at"/>
            <result column="apply_content" property="apply_content"/>
            <result column="execute_time" property="execute_time"/>
            <result column="w_gmt_create" property="gmt_create"/>
            <result column="w_gmt_modified" property="gmt_modified"/>
            <result column="is_delete" property="is_delete"/>
            <result column="is_parallel" property="is_parallel"/>
            <result column="order_schema_task_id" property="order_schema_task_id"/>
        </association>
    </resultMap>

    <select id="getUnfinishedCnt" resultType="java.lang.Integer"
            parameterType="com.dc.repository.mysql.model.OrderSchemaTask">
        select count(0)
        from order_schema_task
        where order_id = #{order_id}
          and (schema_status = 0 or schema_status = 2 or (schema_status = 1 and check_fail_reason is null)) for update
    </select>

    <select id="getUncheckWithFailCnt" resultType="java.lang.Integer"
            parameterType="com.dc.repository.mysql.model.OrderSchemaTask">
        select count(0)
        from order_schema_task
        where order_id = #{order_id}
          and schema_status = 1
          and check_fail_reason is not null
    </select>

    <select id="selectOrderSchemaTasks" resultMap="OrderSchemaTaskResultMap">
        SELECT
        t1.id AS t1_id,
        t1.order_id,
        t1.schema_id,
        t1.schema_status,
        t1.environment,
        t1.affected_rows,
        t1.gmt_create,
        t1.gmt_modified,
        t1.consumer_token_list,
        t1.producer_token,

        w.id AS w_id,
        w.order_id AS w_order_id,
        w.current_status,
        w.execute_type,
        w.apply_user_id,
        w.user_id,
        w.available_at,
        w.apply_content,
        w.execute_time,
        w.gmt_create AS w_gmt_create,
        w.gmt_modified AS w_gmt_modified,
        w.is_delete,
        w.is_parallel,
        w.order_schema_task_id

        FROM order_schema_task t1
        LEFT JOIN work_order_execute w
        ON t1.id = w.order_schema_task_id
        WHERE t1.order_id = #{orderId}
        <if test="schemaIdList != null and schemaIdList.size() > 0">
            AND t1.schema_id IN
            <foreach collection="schemaIdList" item="schemaId" open="(" separator="," close=")">
                #{schemaId}
            </foreach>
        </if>
    </select>
</mapper>

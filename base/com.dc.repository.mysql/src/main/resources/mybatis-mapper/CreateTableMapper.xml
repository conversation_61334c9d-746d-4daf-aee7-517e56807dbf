<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.CreateTableMapper">

    <update id="createTable" parameterType="String">
        ${sql}
    </update>

    <update id="deleteDataTable" parameterType="String">
        DROP TABLE ${tableName}
    </update>

    <select id="getTableNameList" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT table_name
        FROM information_schema.TABLES
        WHERE table_schema = 'dc'
          AND table_name LIKE ${forName}
    </select>


</mapper>
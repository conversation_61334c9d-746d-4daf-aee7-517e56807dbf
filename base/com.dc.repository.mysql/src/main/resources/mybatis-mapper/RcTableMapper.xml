<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.RcTableMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.RcTable">
        <result column="id" property="id"/>
        <result column="instance_id" property="instance_id"/>
        <result column="instance_name" property="instance_name"/>
        <result column="schema_id" property="schema_id"/>
        <result column="schema_name" property="schema_name"/>
        <result column="table_name" property="table_name"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="table_structure" property="table_structure"/>
        <result column="create_table_sql" property="create_table_sql"/>
        <result column="pk_name" property="pk_name"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.instance_id,
		t.instance_name,
		t.schema_id,
		t.schema_name,
		t.table_name,
        t.gmt_create,
        t.gmt_modified,
        t.table_structure,
        t.create_table_sql,
        t.pk_name
    </sql>


    <insert id="add" parameterType="com.dc.repository.mysql.model.RcTable" keyColumn="id"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `rc_table` (`instance_id`,`instance_name`,`schema_id`,`schema_name`,`table_name`,`gmt_create`,`gmt_modified`,`table_structure`,`create_table_sql`,`pk_name`)
        values (#{instance_id},#{instance_name},#{schema_id},#{schema_name},#{table_name},now(6),now(6),#{table_structure},#{create_table_sql},#{pk_name})
    </insert>

    <select id="getTableBySchemaId" parameterType="com.dc.repository.mysql.model.RcTable"
            resultType="com.dc.repository.mysql.model.RcTable">
        SELECT * FROM `rc_table` WHERE `instance_id` = #{instance_id} AND `schema_id` = #{schema_id} AND `table_name` = #{table_name};
    </select>

    <select id="getTableById" parameterType="java.lang.Long"
            resultType="com.dc.repository.mysql.model.RcTable">
        SELECT <include refid="Base_Column_List" />
        FROM `rc_table` t WHERE t.`id` = #{tableId} ;
    </select>

    <update id="updateTableTagById" parameterType="com.dc.repository.mysql.model.RcTable">
        UPDATE `rc_table` AS t SET t.tag = #{tag} , t.gmt_modified = now(6) WHERE t.id = #{id}
    </update>

    <select id="getRcTable" parameterType="com.dc.repository.mysql.model.RcTable"
            resultType="com.dc.repository.mysql.model.RcTable">
        SELECT <include refid="Base_Column_List" />
        FROM `rc_table` t WHERE
        t.`instance_id` = #{instance_id}
        AND t.`instance_name` = #{instance_name}
        AND t.`schema_id` = #{schema_id}
        AND t.`schema_name` = #{schema_name}
        AND t.`table_name` = #{table_name}
        order by t.gmt_modified DESC;
    </select>

    <select id="getRcTableListByIds" parameterType="map"
            resultType="com.dc.repository.mysql.model.RcTable">
        SELECT <include refid="Base_Column_List" />
        FROM `rc_table` AS t
        WHERE t.id in
        <foreach item="id" collection="ids" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

</mapper>
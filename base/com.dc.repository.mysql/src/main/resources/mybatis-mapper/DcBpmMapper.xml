<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcBpmMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.DcBpm">
        <result column="id" property="id"/>
        <result column="pd_key" property="pd_key"/>
        <result column="pd_name" property="pd_name"/>
        <result column="sequence" property="sequence"/>
        <result column="description" property="description"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.pd_key,
		t.pd_name,
		t.sequence,
		t.description,
		t.is_delete,
		t.gmt_create,
		t.gmt_modified
    </sql>

    <select id="getById" resultMap="OrderInfo" parameterType="java.lang.Integer">
        SELECT <include refid="Base_Column_List"/>
        FROM `dc_bpm` AS t
        WHERE t.id = #{id}
    </select>

    <select id="getKeyById" resultType="java.lang.String" parameterType="java.lang.Integer">
        SELECT t.pd_key
        FROM `dc_bpm` AS t
        WHERE t.id = #{id}
    </select>


</mapper>
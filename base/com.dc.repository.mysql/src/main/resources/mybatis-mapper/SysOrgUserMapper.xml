<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
         符号转义说明
    &lt;          <
    &gt;          >
    &lt;&gt;     <>
    &amp;        &
    &apos;       '
    &quot;       "
  <![CDATA[ 这里写你的SQL或者符号 ]]>
 -->

<mapper namespace="com.dc.repository.mysql.mapper.SysOrgUserMapper">

    <delete id="deleteByUserId" parameterType="java.util.List">
        delete from dc_sys_org_user
        where uid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

</mapper>

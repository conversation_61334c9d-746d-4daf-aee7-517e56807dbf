<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.SystemParamConfigMapper">

    <sql id="Base_Column_List">
        t.id,
		t.gmt_create,
		t.gmt_modified,
		t.is_delete,
		t.p_key,
		t.p_value,
		t.p_unit,
		t.comment,
		t.param_type
    </sql>

    <select id="getParamConfig" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT p_value
        FROM system_param_config AS t
        WHERE t.p_key = #{key}
    </select>

    <select id="getSystemParamConfig" resultType="com.dc.repository.mysql.model.SystemParamConfig">
        SELECT <include refid="Base_Column_List" />
        FROM system_param_config AS t
    </select>

    <select id="getSystemParamConfigByKey" parameterType="java.lang.String"
            resultType="com.dc.repository.mysql.model.SystemParamConfig">
        SELECT <include refid="Base_Column_List" />
        FROM system_param_config AS t
        WHERE t.p_key = #{key}
    </select>


</mapper>
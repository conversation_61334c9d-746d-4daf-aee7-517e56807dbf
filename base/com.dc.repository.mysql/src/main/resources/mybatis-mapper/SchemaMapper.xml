<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.SchemaMapper">

    <select id="updateTableCountById" resultType="com.dc.repository.mysql.model.Schema" parameterType="map" >
       update dc_db_schema
       set table_count = #{table_count}
       where unique_key = #{unique_key}
    </select>

    <select id="updateSchemaCharsetById" resultType="com.dc.repository.mysql.model.Schema" parameterType="java.lang.String">
        update dc_db_schema
        set charset = #{charset}
        where unique_key = #{unique_key}
    </select>

    <select id="updateSchemaIsSysById" resultType="com.dc.repository.mysql.model.Schema" parameterType="map" >
        update dc_db_schema
        set is_sys = #{is_sys}
        where unique_key = #{unique_key}
    </select>

    <update id="updateSchemaIsPrivate" parameterType="map">
        update dc_db_schema
        set is_private = #{is_private}
        where connect_id = #{connect_id}
        and schema_name in
        <foreach collection="schema_names" item="schema_name" index="index" open="(" close=")" separator=",">
            #{schema_name}
        </foreach>

    </update>


    <select id="isPrivateSchema" parameterType="java.lang.String" resultType="java.lang.Boolean">
        select
            case is_private
                when 0 then false
                when 1 then true
                else false
                end as isPrivateSchema
        from dc_db_schema where unique_key = #{schemaUniqueKey} and is_delete = 0
    </select>

    <select id="getAllPrivateSchemaId" resultType="java.lang.String">
        select unique_key from dc_db_schema where connect_id = #{connectId} and is_private = 1 and is_delete = 0
    </select>

</mapper>
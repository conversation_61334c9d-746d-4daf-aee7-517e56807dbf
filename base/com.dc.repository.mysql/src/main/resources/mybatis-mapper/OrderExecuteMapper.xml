<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.OrderExecuteMapper">

    <resultMap id="OrderExecuteInfo" type="com.dc.repository.mysql.model.OrderExecute" >
        <result column="id" property="id" />
        <result column="order_id" property="order_id" />
        <result column="current_status" property="current_status" />
        <result column="execute_type" property="execute_type" />
        <result column="apply_user_id" property="apply_user_id" />
        <result column="user_id" property="user_id" />
        <result column="available_at" property="available_at" />
        <result column="apply_content" property="apply_content" />
        <result column="execute_time" property="execute_time" />
        <result column="gmt_create" property="gmt_create" />
        <result column="gmt_modified" property="gmt_modified" />
        <result column="is_delete" property="is_delete" />
        <result column="is_parallel" property="is_parallel" />
        <result column="order_schema_task_id" property="order_schema_task_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.order_id,
		t.current_status,
		t.execute_type,
		t.apply_user_id,
		t.user_id,
		t.available_at,
		t.apply_content,
		t.execute_time,
		t.gmt_create,
		t.gmt_modified,
		t.is_delete,
		t.is_parallel,
        t.order_schema_task_id
    </sql>

    <update id="updateExecuteStatusByOrderId" parameterType="com.dc.repository.mysql.model.OrderExecute">
        UPDATE `work_order_execute`
        SET current_status = #{current_status},
            gmt_modified   = now()
        WHERE order_id = #{order_id}
        <if test="order_schema_task_id != null">
            and order_schema_task_id = #{order_schema_task_id}
        </if>
          and is_delete = 0
    </update>

    <update id="updateExecuteByOrderId" parameterType="com.dc.repository.mysql.model.OrderExecute">
        UPDATE `work_order_execute`
        SET current_status = #{current_status},
            user_id = #{user_id},
            gmt_modified   = now()
        WHERE order_id = #{order_id}
          and is_delete = 0
    </update>

    <select id="getOrderExecuteByOrderId" resultMap="OrderExecuteInfo" parameterType="java.lang.Integer">
        SELECT
        <include refid="Base_Column_List"/>
        FROM `work_order_execute` AS t
        WHERE t.order_id = #{id} and t.is_delete = 0
    </select>

    <select id="getOrderTimingScript" resultMap="OrderExecuteInfo" parameterType="java.lang.Integer" >
        SELECT <include refid="Base_Column_List" />
        FROM `work_order_execute` AS t
        WHERE t.execute_type = 3
        AND   t.current_status = 1
        AND   t.is_delete = 0
        AND   #{now} &gt;= t.execute_time
        AND (
        ( t.available_at IS NOT NULL
        AND  JSON_EXTRACT(t.available_at, '$.to') &gt;= #{now}
        AND  #{now} &gt;= JSON_EXTRACT(t.available_at, '$.from')
        ) OR t.available_at IS NULL
        )
    </select>
    
    <select id="getOrderIdOfSpecifiedType" resultType="java.lang.Integer">
        select t1.id from
            (SELECT id, bpm_id FROM `dc_work_order` WHERE id in <foreach collection="birthOrderIds" open="(" close=")" separator="," item="orderId">#{orderId}</foreach>) t1
                left join
            dc_bpm t2
                on t1.bpm_id = t2.id
                where t2.pd_key = #{workOrderType}
    </select>

    <update id="updateExecuteById" parameterType="com.dc.repository.mysql.model.OrderExecute">
        UPDATE `work_order_execute`
        SET current_status = #{current_status},
            user_id        = #{user_id},
            gmt_modified   = now()
        WHERE id = #{id}
          and is_delete = 0
    </update>

    <select id="getUnfinishedOrderExecuteCnt" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        select count(0)
        from `work_order_execute`
        where order_id = #{order_id}
          and current_status in (1, 2, 7) for update
    </select>

    <select id="getOrderExecuteListByOrderId" resultMap="OrderExecuteInfo">
        select
        <include refid="Base_Column_List"/>
        from `work_order_execute` AS t
        WHERE t.order_id = #{id}
    </select>

    <select id="getOrderExecuteListByTaskIds" resultMap="OrderExecuteInfo">
        select
        <include refid="Base_Column_List"/>
        from `work_order_execute` AS t
        WHERE t.order_schema_task_id in
        <foreach collection="list" item="task_id" open="(" separator="," close=")">
            #{task_id}
        </foreach>
    </select>

    <update id="updateOrderExecuteBatch">
        UPDATE `work_order_execute`
        SET current_status = #{status},
        gmt_modified = now()
        WHERE id in
        <foreach collection="id_list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_delete = 0
    </update>

    <update id="updateInterruptStatus">
        update `work_order_execute`
        set current_status = #{status},
            gmt_modified   = now()
        where order_schema_task_id = #{taskId}
          and current_status = 2
    </update>

    <select id="getOrderExecuteByOrderIdAndSchemaId" resultMap="OrderExecuteInfo">
        select
        <include refid="Base_Column_List"/>
        from `work_order_execute` AS t
        WHERE t.order_id = #{id} and t.order_schema_task_id = #{taskId}
    </select>


</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcAccountOtherPrivsMapper">

    <update id="updatePrivilege" parameterType="com.dc.repository.mysql.model.DcAccountOtherPrivs">
        update dc_account_other_privs
        set `sql` = #{sql}
        where resource_id = #{resourceId}
          and account_id = #{accountId}
          and is_delete = #{isDelete}
          and is_expire = #{isExpire}
    </update>

</mapper>
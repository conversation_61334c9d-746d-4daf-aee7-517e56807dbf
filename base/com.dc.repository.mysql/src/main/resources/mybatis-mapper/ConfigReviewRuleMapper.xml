<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.ConfigReviewRuleMapper">

	<resultMap id="ConfigReviewRuleInfo" type="com.dc.repository.mysql.model.ConfigReviewRule" >
		<result column="id" property="id" />
		<result column="user_id" property="user_id" />
	    <result column="review_name" property="review_name" />
	    <result column="operation" property="operation" />
	    <result column="trigger" property="trigger" />
	    <result column="source_type" property="source_type" />
	    <result column="db_source" property="db_source" />
	    <result column="review_user_type" property="review_user_type" />
		<result column="status" property="status" />
		<result column="review_users" property="review_users" />
	    <result column="operate_user_type" property="operate_user_type" />
		<result column="operate_users" property="operate_users" />
		<result column="is_delete" property="is_delete" />
		<result column="gmt_create" property="gmt_create" />
		<result column="gmt_modified" property="gmt_modified" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.user_id,
		t.review_name,
		t.operation,
		t.trigger,
		t.source_type,
		t.db_source,
		t.review_user_type,
		t.status,
		t.review_users,
		t.operate_user_type,
        t.operate_users,
        t.is_delete,
		t.gmt_create,
		t.gmt_modified
	</sql>

	<select id="getConfigReviewRule" resultMap="ConfigReviewRuleInfo">
		SELECT <include refid="Base_Column_List" />
		FROM `config_review_rule` AS t
		WHERE t.is_delete = 0
        AND   t.status = 1
        ORDER BY t.gmt_create
	</select>

	<select id="getConfigReviewRuleByCondition" parameterType="map" resultMap="ConfigReviewRuleInfo">
		SELECT <include refid="Base_Column_List" />
		FROM `config_review_rule` AS t
		WHERE t.is_delete = 0
		AND   t.status = 1
		AND   (t.source_type = 1
		<foreach item="db_source" collection="db_sources" separator="or" open="or" close=")">
			FIND_IN_SET(#{db_source},t.db_source)
		</foreach>
		ORDER BY t.gmt_create
	</select>

</mapper>
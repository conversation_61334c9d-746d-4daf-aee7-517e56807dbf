<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.PermissionsDictionaryMapper">

	<select id="findPermissionsDictionary" resultType="java.lang.String">
		SELECT pd.name_key
		FROM permissions_dictionary AS pd,permissions_dictionary_relation AS pdr
		WHERE pdr.is_delete = 0
		AND pd.id = pdr.dict_id
	</select>

	<select id="findOperationGroupName" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT pdg.group_name
		 FROM permissions_dictionary AS pd,
			  permissions_dictionary_relation AS pdr,
			  permissions_dictionary_group AS pdg
		WHERE pdr.is_delete = 0
		  AND pd.id = pdr.dict_id
		  AND pdg.id = pdr.group_id
		  AND pd.name_key = #{operation};
	</select>


</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcWorkOrderFlowUserMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.DcWorkOrderFlowUser">
        <result column="id" property="id"/>
        <result column="flow_id" property="flow_id"/>
        <result column="base_type" property="base_type"/>
        <result column="user_id" property="user_id"/>
        <result column="username" property="username"/>
        <result column="is_read" property="is_read"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.flow_id,
		t.base_type,
		t.user_id,
		t.username,
		t.is_read
    </sql>

    <insert id="save" parameterType="com.dc.repository.mysql.model.DcWorkOrderFlowUser">
        insert into dc_work_order_flow_user (flow_id,base_type,user_id) values (#{flow_id},2,#{user_id})
    </insert>

    <select id="getStepUserById" parameterType="java.lang.Integer"
            resultType="com.dc.repository.mysql.model.DcWorkOrderFlowUser">
        select u.user_id,u.username from dc_work_order_step step
        left join dc_work_order_step_user u on step.id = u.step_id
        where order_id=#{orderId} and step=1
    </select>


</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.RelationTableMapper">


    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.RelationTable">
        <result column="id" property="id"/>
        <result column="row_id" property="row_id"/>
        <result column="rc_sql_id" property="rc_sql_id"/>
        <result column="p_id" property="p_id"/>
        <result column="root_id" property="root_id"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="is_delete" property="is_delete"/>
        <result column="transaction_index" property="transaction_index"/>
        <result column="is_real_pk" property="is_real_pk"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.row_id,
		t.rc_sql_id,
		t.p_id,
		t.root_id,
		t.gmt_create,
        t.gmt_modified,
        t.is_delete,
        t.transaction_index,
        t.is_real_pk
    </sql>

    <insert id="add" parameterType="com.dc.repository.mysql.model.RelationTable" keyColumn="id"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ${tableName} (`row_id`,`rc_sql_id`,`p_id`,`root_id`,`gmt_create`,`gmt_modified`,`transaction_index`,`is_real_pk`)
        values (#{row_id},#{rc_sql_id},#{p_id},#{root_id},now(6),now(6),#{transaction_index},#{is_real_pk})
    </insert>

    <select id="getRelationTableByRowId" parameterType="com.dc.repository.mysql.model.RelationTable"
            resultType="com.dc.repository.mysql.model.RelationTable">
        SELECT <include refid="Base_Column_List" />
        FROM ${tableName} AS t where `row_id` = #{row_id} and `transaction_index` = #{transaction_index} order by `gmt_modified` ASC
    </select>

    <select id="getRelationTables" parameterType="map" resultType="com.dc.repository.mysql.model.RelationTable">
        SELECT <include refid="Base_Column_List" />
        FROM ${table_name} AS t
        WHERE t.`transaction_index` = #{transaction_index}
        AND t.`rc_sql_id` IN
        <foreach item="rc_sql_id" collection="rc_sql_ids" separator="," open="(" close=")">
            #{rc_sql_id}
        </foreach>
    </select>

    <select id="getRelationTableById" parameterType="com.dc.repository.mysql.model.RelationTable"
            resultType="com.dc.repository.mysql.model.RelationTable">
        SELECT <include refid="Base_Column_List" />
        FROM ${tableName} AS t where `id` = #{id} and `transaction_index` = #{transaction_index}
    </select>

    <select id="getRelationTableByRootId" parameterType="com.dc.repository.mysql.model.RelationTable"
            resultType="com.dc.repository.mysql.model.RelationTable">
        SELECT <include refid="Base_Column_List" />
        FROM ${tableName} AS t where `root_id` = #{root_id} and `transaction_index` = #{transaction_index} order by `gmt_modified` DESC
    </select>

    <delete id="deleteRelationTables" parameterType="map">
        DELETE
        FROM ${table_name}
        WHERE `transaction_index` = #{transaction_index}
        AND `rc_sql_id` IN
        <foreach item="rc_sql_id" collection="rc_sql_ids" separator="," open="(" close=")">
            #{rc_sql_id}
        </foreach>
    </delete>

    <select id="getRelationTableCount" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ${table_name}
    </select>

    <update id="dropRelationTable" parameterType="map">
        DROP TABLE ${table_name}
    </update>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.PaInstanceMapper">

    <resultMap id="instanceResultMap" type="com.dc.repository.mysql.model.PaInstance" >
        <result column="id" property="id" />
        <result column="instance_name" property="instanceName" />
        <result column="environment" property="environment" />
        <result column="domain_name" property="domainName" />
        <result column="vip" property="vip" />
        <result column="port" property="port" />
        <result column="default_role" property="defaultRole" />
        <result column="service_user" property="serviceUser" />
        <result column="database_version" property="databaseVersion" />
        <result column="status" property="status" />
        <result column="cyberark_entity_name" property="cyberarkEntityName" />
        <result column="architecture_type" property="architectureType" />
        <result column="create_method" property="createMethod" />
        <result column="deploy_ecology" property="deployEcology" />
        <result column="system_name" property="systemName" />
        <result column="instance_uuid" property="instanceUuid" />
        <result column="is_separate" property="isSeparate" />
        <result column="entity_uuid" property="entityUuid" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <select id="getAllOrg" resultType="com.dc.repository.mysql.model.PaUser" >
        SELECT distinct bu_id,bu_id_short_desc
        FROM pa_user
    </select>

    <select id="getByEntityUuids" resultMap="instanceResultMap" parameterType="java.util.Map">
        SELECT *
        FROM pa_instance
        where entity_uuid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <update id="updateBatchById" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update pa_instance set
            instance_name = #{item.instanceName},
            entity_uuid = #{item.entityUuid},
            environment = #{item.environment},
            domain_name = #{item.domainName},
            vip = #{item.vip},
            port = #{item.port},
            default_role = #{item.defaultRole},
            database_version = #{item.databaseVersion},
            service_user = #{item.serviceUser},
            status = #{item.status},
            cyberark_entity_name = #{item.cyberarkEntityName},
            architecture_type = #{item.architectureType},
            create_method = #{item.createMethod},
            deploy_ecology = #{item.deployEcology},
            system_name = #{item.systemName},
            instance_uuid = #{item.instanceUuid},
            is_separate = #{item.isSeparate},
            gmt_modified = #{item.gmtModified}
            where id = #{item.id}
        </foreach>
    </update>

</mapper>

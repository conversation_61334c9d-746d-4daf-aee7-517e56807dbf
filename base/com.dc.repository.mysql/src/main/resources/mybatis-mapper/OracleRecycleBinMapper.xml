<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.OracleRecycleBinMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.OracleRecycleBin">
        <result column="id" property="id"/>
        <result column="instance_id" property="instance_id"/>
        <result column="instance_name" property="instance_name"/>
        <result column="schema_id" property="schema_id"/>
        <result column="schema_name" property="schema_name"/>
        <result column="table_name" property="table_name"/>
        <result column="sql" property="sql"/>
        <result column="temporary_table_name" property="temporary_table_name"/>
        <result column="operator" property="operator"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
    </resultMap>


    <sql id="Base_Column_List">
        t.id,
		t.instance_id,
		t.instance_name,
		t.schema_id,
		t.schema_name,
		t.table_name,
        t.sql,
        t.temporary_table_name,
        t.operator,
        t.is_delete,
        t.gmt_create,
        t.gmt_modified
    </sql>


    <insert id="add" parameterType="com.dc.repository.mysql.model.OracleRecycleBin">
        insert into `recycle_bin` (`instance_id`,`instance_name`,`schema_id`,`schema_name`,`table_name`,`sql`,`temporary_table_name`,`operator`,`gmt_create`,`gmt_modified`)
        values (#{instance_id},#{instance_name},#{schema_id},#{schema_name},#{table_name},#{sql},#{temporary_table_name},#{operator},now(6),now(6))
    </insert>

    <select id="getRecycleBinById" parameterType="java.lang.Long"
            resultType="com.dc.repository.mysql.model.OracleRecycleBin">
        select <include refid="Base_Column_List" /> from `recycle_bin` AS t where t.`id` = #{id}
    </select>


</mapper>
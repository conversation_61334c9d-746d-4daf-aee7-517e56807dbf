<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.OrderPreCheckMapper">


    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.OrderPreCheck">
        <result column="id" property="id"/>
        <result column="order_id" property="order_id"/>
        <result column="check_status" property="check_status"/>
        <result column="desc" property="desc"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="is_delete" property="is_delete"/>
    </resultMap>

    <insert id="save" parameterType="com.dc.repository.mysql.model.OrderPreCheck">
        insert into `work_order_precheck` (`order_id`,`check_status`,`desc`,`gmt_create`,`gmt_modified`)
        values (#{order_id}, #{check_status}, #{desc}, now(6), now(6))
    </insert>

</mapper>
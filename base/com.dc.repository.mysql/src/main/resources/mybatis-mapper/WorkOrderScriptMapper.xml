<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.WorkOrderScriptMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.WorkOrderScript">
        <result column="id" property="id"/>
        <result column="order_id" property="order_id"/>
        <result column="check_status" property="check_status"/>
        <result column="script_name" property="script_name"/>
        <result column="desc" property="desc"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="sql_check" property="sql_check"/>
        <result column="grammar_check" property="grammar_check"/>
        <result column="type" property="type"/>
        <result column="order_schema_task_id" property="order_schema_task_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.order_id,
		t.check_status,
		t.script_name,
		t.desc,
		t.is_delete,
		t.gmt_create,
		t.gmt_modified,
        t.sql_check,
        t.grammar_check,
        t.type,
        t.order_schema_task_id
    </sql>

    <insert id="save" parameterType="com.dc.repository.mysql.model.WorkOrderScript">
        insert into work_order_script (order_id, check_status, script_name, type) values (#{order_id}, #{check_status}, #{script_name}, #{type})
    </insert>

    <update id="update" parameterType="com.dc.repository.mysql.model.WorkOrderScript">
        update work_order_script set
                check_status = #{check_status} ,
        <if test='check_fail_reason != null and check_fail_reason != ""'>
             `check_fail_reason` = #{check_fail_reason},
        </if>
        <if test='desc != null and desc != ""'>
            `desc` = #{desc} ,
        </if>
        <if test='sql_check != null and sql_check != ""'>
            `sql_check` = #{sql_check} ,
        </if>
        <if test='grammar_check != null and grammar_check != ""'>
            `grammar_check` = #{grammar_check} ,
        </if>
                gmt_modified = now()
        where order_id = #{order_id} and script_name = #{script_name} and type = #{type}
    </update>

    <update id="updateStatus" parameterType="com.dc.repository.mysql.model.WorkOrderScript">
        UPDATE work_order_script
        SET check_status      = #{check_status},
        check_fail_reason = #{check_fail_reason},
        <if test='grammar_check != null and grammar_check != ""'>
            `grammar_check` = #{grammar_check},
        </if>
        gmt_modified = now()
        WHERE order_schema_task_id = #{order_schema_task_id} and script_name = #{script_name} and type = #{type}
    </update>


</mapper>
package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component(value = "JOBSPLN")
public class SyncPaInstanceNameHandler {

    private static Logger logger = LoggerFactory.getLogger(SyncPaInstanceNameHandler.class);

    @Resource
    private JobConfig jobConfig;

    private static final String summerInterfaceSyncPaInstanceName = "/dc-summer/pa/instance/sync/entity-history";

    @XxlJob("SyncPaInstanceNameHandler")
    public ReturnT<String> syncPaInstanceNameHandler(String param) throws Exception {
        ReturnT<String> result = ReturnT.SUCCESS;
        try {
            logger.info("Start SyncPaInstanceNameHandler Synchronize");
            HttpClientUtils.doGet(jobConfig.getPath().getDcSummer() + summerInterfaceSyncPaInstanceName, null);
        } catch (Exception e) {
            logger.error("call SyncPaInstanceNameHandler error!", e);
        }
        return result;
    }
}

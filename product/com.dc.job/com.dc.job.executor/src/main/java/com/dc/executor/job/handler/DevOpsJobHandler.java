package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Slf4j
@Component
public class DevOpsJobHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    private static final String summerTruncateTable = "/dc-summer/pa/user/delete-table";

    @XxlJob("DevOpsJobHandler")
    public ReturnT<String> devOpsJobHandler(String param) throws Exception {
        try {
            log.info("Start DevOpsJobHandler !");
            Integer type = 0;
            if (!ObjectUtils.isEmpty(param)) {
                type = Integer.valueOf(param);
            }
            // 福建农信需求:定时清理过期文件
            HttpClientUtils.doGet(jobConfig.getPath().getDcSummer() + summerTruncateTable + "?type=" + type, null);
        } catch (Exception e) {
            log.error("call DevOpsJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

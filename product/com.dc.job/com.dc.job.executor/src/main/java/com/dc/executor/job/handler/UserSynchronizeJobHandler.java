package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.utils.http.HttpClientUtils;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Slf4j
@Component
public class UserSynchronizeJobHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    @XxlJob("UserSynchronizeJobHandler")
    public ReturnT<String> userSynchronizeJobHandler(String param) throws Exception {
        try {
            log.info("Start User Synchronize");
            HttpClientUtils.doPost(jobConfig.getPath().getDcBackend() + "/" + param, new StringEntity(""));
        } catch (Exception e) {
            log.error("call userSynchronizeJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }
}

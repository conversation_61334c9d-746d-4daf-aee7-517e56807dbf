package com.dc.executor.type;

public enum  JobOperationsType {
    add(1),
    update(2),
    remove(3),
    start(4),
    stop(5);

    Integer value;

    JobOperationsType(Integer value){
        // TODO Auto-generated constructor stub
        this.value = value;
    }


    public Integer getValue() {
        return value;
    }

    public static String getTypeByCode(Integer code) {
        for (JobOperationsType jh : JobOperationsType.values()) {
            if(jh.getValue().equals(code)) {
                return jh.toString();
            }
        }
        return "";
    }
}

package com.dc.executor.model;

import com.dc.repository.mysql.column.AuthGrant;
import com.dc.repository.mysql.column.AuthRevoke;
import com.dc.repository.mysql.column.ResourceContent;

import java.util.List;

public class ApplyContent {

    private int db_type;
    private String connect_id;
    private String schema_id;
    private String available_at;
    private String lxfkzmd;
    private String appendix;
    private String schema_name;
    private String connection_desc;
    private int environment;
    private String instance_name;
    private String connection;
    private String charset;
    private String username;
    private String skip_reason;

    /** 权限变更*/
    private List<ResourceContent> resource_list;
    private AuthGrant auth_grant;
    private AuthRevoke auth_revoke;
    private String account_change_apply_resource_id;

    /**
     * 批量工单
     */
    private List<SchemaInfo> schema_data;

    public int getDb_type() {
        return db_type;
    }

    public void setDb_type(int db_type) {
        this.db_type = db_type;
    }

    public String getConnect_id() {
        return connect_id;
    }

    public void setConnect_id(String connect_id) {
        this.connect_id = connect_id;
    }

    public String getSchema_id() {
        return schema_id;
    }

    public void setSchema_id(String schema_id) {
        this.schema_id = schema_id;
    }

    public String getAvailable_at() {
        return available_at;
    }

    public void setAvailable_at(String available_at) {
        this.available_at = available_at;
    }

    public String getLxfkzmd() {
        return lxfkzmd;
    }

    public void setLxfkzmd(String lxfkzmd) {
        this.lxfkzmd = lxfkzmd;
    }

    public String getAppendix() {
        return appendix;
    }

    public void setAppendix(String appendix) {
        this.appendix = appendix;
    }

    public String getSchema_name() {
        return schema_name;
    }

    public void setSchema_name(String schema_name) {
        this.schema_name = schema_name;
    }

    public String getConnection_desc() {
        return connection_desc;
    }

    public void setConnection_desc(String connection_desc) {
        this.connection_desc = connection_desc;
    }

    public int getEnvironment() {
        return environment;
    }

    public void setEnvironment(int environment) {
        this.environment = environment;
    }

    public String getInstance_name() {
        return instance_name;
    }

    public void setInstance_name(String instance_name) {
        this.instance_name = instance_name;
    }

    public String getConnection() {
        return connection;
    }

    public void setConnection(String connection) {
        this.connection = connection;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSkip_reason() {
        return skip_reason;
    }

    public void setSkip_reason(String skip_reason) {
        this.skip_reason = skip_reason;
    }

    public List<ResourceContent> getResource_list() {
        return resource_list;
    }

    public void setResource_list(List<ResourceContent> resource_list) {
        this.resource_list = resource_list;
    }

    public AuthGrant getAuth_grant() {
        return auth_grant;
    }

    public void setAuth_grant(AuthGrant auth_grant) {
        this.auth_grant = auth_grant;
    }

    public AuthRevoke getAuth_revoke() {
        return auth_revoke;
    }

    public void setAuth_revoke(AuthRevoke auth_revoke) {
        this.auth_revoke = auth_revoke;
    }

    public String getAccount_change_apply_resource_id() {
        return account_change_apply_resource_id;
    }

    public void setAccount_change_apply_resource_id(String account_change_apply_resource_id) {
        this.account_change_apply_resource_id = account_change_apply_resource_id;
    }

    public List<SchemaInfo> getSchema_data() {
        return schema_data;
    }

    public void setSchema_data(List<SchemaInfo> schema_data) {
        this.schema_data = schema_data;
    }
}

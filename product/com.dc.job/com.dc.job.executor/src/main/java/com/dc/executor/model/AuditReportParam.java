package com.dc.executor.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AuditReportParam {

    @JsonProperty("log_id")
    private Long logId; // log id

    @JsonProperty("is_repeat")
    private boolean isRepeat; // 是否重复执行

    @JsonProperty("repeat_type")
    private Integer repeatType; // 任务频率(每天1、每周2、每月3)

    @JsonProperty("daily_strategy")
    private String dailyStrategy; // 任务频率每天，策略的key(不限是unlimited)

}

package com.dc.executor.service.impl;

import com.dc.executor.service.PrivateSchemaService;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.mapper.SecurityRuleDetailsMapper;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.mysql.model.Schema;
import com.dc.type.DatabaseType;
import com.dc.type.SecurityRuleType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PrivateSchemaServiceImpl implements PrivateSchemaService {

    @Resource
    private SecurityRuleDetailsMapper securityRuleDetailsMapper;

    @Resource
    private SchemaMapper SchemaMapper;

    @Override
    public void updatePrivateSchema(DatabaseConnection instance, List<Schema> schemaMysqlList) {
        if (!DatabaseType.ORACLE.getValue().equals(instance.getDb_type())) {
            return;
        }
        List<String> privateSchemaNames = schemaMysqlList.stream().filter(schema -> schema.getIs_private() == 1).map(Schema::getSchema_name).collect(Collectors.toList());

        String privateSchemasSet = securityRuleDetailsMapper.getSecurityRuleValue(SecurityRuleType.PRIVATE_SCHEMA.getName(), instance.getUnique_key());
        if (privateSchemasSet == null) {
            log.info("No private schema for instance: " + instance.getInstance_name());
            return;
        }
        String[] privateSchemaArray = privateSchemasSet.split(",");
        List<String> diffPrivateSchemaArray = privateSchemaNames.stream()
                .filter(schemaName -> !Arrays.asList(privateSchemaArray).contains(schemaName))
                .collect(Collectors.toList());

        if (!diffPrivateSchemaArray.isEmpty()) {
            log.info("need remove private schema: " + diffPrivateSchemaArray + " for instance: " + instance.getInstance_name());
            try {
                this.SchemaMapper.updateSchemaIsPrivate(instance.getUnique_key(), diffPrivateSchemaArray, 0);
            } catch (Exception e) {
                log.error("Failed to update private schemas for instance: " + instance.getInstance_name(), e);
            }

        }
        List<String> addPrivateSchemaArray = Arrays.stream(privateSchemaArray)
                .filter(schemaName -> !privateSchemaNames.contains(schemaName))
                .collect(Collectors.toList());
        if (!addPrivateSchemaArray.isEmpty()) {
            try {
                this.SchemaMapper.updateSchemaIsPrivate(instance.getUnique_key(), addPrivateSchemaArray, 1);
            } catch (Exception e) {
                log.error("Failed to update private schemas for instance: " + instance.getInstance_name(), e);
            }
        }
    }
}

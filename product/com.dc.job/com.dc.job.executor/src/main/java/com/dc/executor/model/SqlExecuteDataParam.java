package com.dc.executor.model;

import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.execution.BatchExecuteModel;
import lombok.Data;

import java.util.List;

@Data
public class SqlExecuteDataParam {

    private List<BatchExecuteModel> batchExecuteModels;
    private ConnectionConfig connectionConfig; // 数据库连接配置信息
    private boolean errorContinue;
    private boolean export; // 是否需要导出
    private String exportType; // 导出类型
    private long logId;
    private String token;
    private TokenConfig tokenConfig;

    private String schemaName;
    private String connectId;
    private String schemaId;
    private String userId;
    private int dbType;
    private String filename; // 文件名
    private String dirname; // 文件路径

    private int limit;
    private int offset;
    private String charset;

    private ExecuteEvent executeEvent;

    private List<Integer> exportModes;
    private String exportEmail;
    private String code;
    private String instanceDesc;
    private String content;

}

//package com.dc.executor.util;
//
//import com.dc.model.SqlFieldData;
//import org.apache.commons.lang3.StringUtils;
//
//import java.util.Arrays;
//import java.util.List;
//import java.util.Locale;
//
//public class CreateTableUtil {
//
//    public static String getCreateTableSql(String tableName, List<SqlFieldData> fields) {
//        String columnSqls = "";
//        for (SqlFieldData sqlFieldData : fields) {
//            if (sqlFieldData.getField_type().equals("ROWID")) {
//                continue;
//            }
//            String columnSql = String.format("`recycle_column_%s` VARCHAR(%s) DEFAULT NULL,", sqlFieldData.getField_name(), sqlFieldData.getField_columnDisplaySize());
//            columnSqls = columnSqls + columnSql;
//        }
//        return "CREATE TABLE IF NOT EXISTS `"+ tableName +"`(\n" +
//                "   `id` bigint(20) NOT NULL AUTO_INCREMENT,\n" +
//                columnSqls +
//                "   `recycle_row_id` bigint(20) DEFAULT NULL,\n" +
//                "   `recycle_backup_sql_id` bigint(20) DEFAULT NULL,\n" +
//                "   `gmt_create` datetime(6) DEFAULT NULL COMMENT '创建时间',\n" +
//                "   `gmt_modified` datetime(6) DEFAULT NULL COMMENT '更新时间',\n" +
//                "   PRIMARY KEY ( `id` )\n" +
//                ")ENGINE=InnoDB DEFAULT CHARSET=utf8;";
//    }
//
//    public static String getCreateTableSqlRecover(String tableName, List<SqlFieldData> fields) {
//        String columnSqls = "";
//        for (SqlFieldData sqlFieldData : fields) {
//            String data_length = sqlFieldData.getData_length();
//            if (sqlFieldData.getField_type().equals("ROWID")) {
//                continue;
//            }
//            if (StringUtils.isBlank(data_length) || !Arrays.asList("VARCHAR","VARCHAR2","CHAR","NCHAR","TEXT").contains(sqlFieldData.getField_type().toUpperCase(Locale.ROOT))) {
////                sqlFieldData.setData_length("255");
//                data_length = "255";
//            }
//            String columnSql = String.format("`recycle_column_%s` VARCHAR(%s) DEFAULT NULL,", sqlFieldData.getField_name(), data_length);
//            columnSqls = columnSqls + columnSql;
//        }
//        return "CREATE TABLE IF NOT EXISTS `"+ tableName +"`(\n" +
//                "   `id` bigint(20) NOT NULL AUTO_INCREMENT,\n" +
//                columnSqls +
//                "   `recycle_version_id` bigint(20) DEFAULT NULL,\n" +
//                "   `recycle_row_id` bigint(20) DEFAULT NULL,\n" +
//                "   `recycle_backup_sql_id` bigint(20) DEFAULT NULL,\n" +
//                "   `gmt_create` datetime(6) DEFAULT NULL COMMENT '创建时间',\n" +
//                "   `gmt_modified` datetime(6) DEFAULT NULL COMMENT '更新时间',\n" +
//                "   PRIMARY KEY ( `id` )\n" +
//                ")ENGINE=InnoDB DEFAULT CHARSET=utf8;";
//    }
//
//    public static String getCreateRowTableSql(String tableName) {
//        return "CREATE TABLE IF NOT EXISTS `"+ tableName +"` (\n" +
//                "  `id` bigint(20) NOT NULL AUTO_INCREMENT,\n" +
//                "  `pk_column` varchar(255) DEFAULT NULL COMMENT '数据库中的主键',\n" +
//                "  `pk_column_value` varchar(255) DEFAULT NULL,\n" +
//                "  `unique_column` varchar(255) DEFAULT NULL COMMENT '数据库中唯一索引',\n" +
//                "  `unique_column_value` varchar(255) DEFAULT NULL,\n" +
//                "   `gmt_create` datetime(6) DEFAULT NULL COMMENT '创建时间',\n" +
//                "   `gmt_modified` datetime(6) DEFAULT NULL COMMENT '更新时间',\n" +
//                "   `is_delete` tinyint(4) NOT NULL DEFAULT '0',\n" +
//                "  PRIMARY KEY (`id`)\n" +
//                ") ENGINE=InnoDB DEFAULT CHARSET=utf8;";
//    }
//
//}

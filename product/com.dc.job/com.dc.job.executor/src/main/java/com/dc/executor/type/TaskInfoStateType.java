package com.dc.executor.type;

public enum TaskInfoStateType {

    error(-1, "接口没调通"),
    wait(-2, "第一条还没处理完"),
    running(-3, "已处理完第一条,正在处理剩下的"),
    finished(-4, "处理完成"),
    closed(-5, "连接已关闭");

    private final Integer value;
    private final String name;

    TaskInfoStateType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}

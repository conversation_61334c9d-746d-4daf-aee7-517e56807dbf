package com.dc.executor.job.handler;

import com.dc.executor.model.DataBackup;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.model.JobLog;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component()
public class DataBackupJobHandler extends AbstractJobHandler {

    @XxlJob("DataBackupJobHandler")
    public ReturnT<String> dataBackupJobHandler(String param) throws Exception {
        try {
            DataBackup dataBackup = json.getObjectMapper().readValue(param, DataBackup.class);
            if (dataBackup != null) {
                if (dataBackup.getLogId() != 0L) {
                    JobLog load = jobLogMapper.load(dataBackup.getLogId());
                    int jobTriggerCount = jobLogMapper.findJobTriggerCount(load.getJobId());
                    long triggerCount = jobTriggerCount + 1L;
                    // 1 表示执行中
                    jobLogMapper.updateBeginTriggerStatus(dataBackup.getLogId(), 1, triggerCount);

                    if (dataBackup.getExecutorParam() != null && !dataBackup.getExecutorParam().isEmpty()) {
                        HttpClientUtils.doGet(jobConfig.getPath().getDcBackend() + "/" + dataBackup.getExecutorParam() + "?id=" + dataBackup.getLogId(), null);
                    } else {
                        log.info("接口路径为空！");
                    }
                } else {
                    log.info("log id 获取失败！");
                }
            } else {
                log.info("参数获取失败！");
            }
        } catch (Exception e) {
            log.error("call dataBackupJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

package com.dc.executor.service.impl;

import com.dc.executor.service.ClearUserSessionService;
import com.dc.executor.util.DCJobLogger;
import com.dc.repository.mysql.mapper.SystemParamConfigMapper;
import com.dc.repository.mysql.model.SystemParamConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Map;

@Slf4j
@Service
public class ClearUserSessionImpl implements ClearUserSessionService {


    private static final String LOGIN_EXPIRED = "login_expired";

    private static final String USER_ONLINE = "dc_user_online";
    @Resource
    private SystemParamConfigMapper systemParamConfigMapper;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public void clearUserSession() {
        //获取系统配置超时时间参数
        DCJobLogger.log("clearUserSession start");
        SystemParamConfig systemParamConfig = systemParamConfigMapper.getSystemParamConfigByKey(LOGIN_EXPIRED);
        if (systemParamConfig != null) {
            //获取超时时间
            String expired = systemParamConfig.getP_value();
            //转int
            int expiredTime = Integer.parseInt(expired);

            //获取当前时间减去超时时间
            long time = Instant.now().getEpochSecond() - 60L * ( expiredTime > 0 ? expiredTime : 1);
            String match = "*";
            ScanOptions.ScanOptionsBuilder optionsBuilder = ScanOptions.scanOptions().match(match);
            ScanOptions options = optionsBuilder.build();
            try (Cursor<Map.Entry<Object, Object>> cursor = redisTemplate.opsForHash().scan(USER_ONLINE, options)) {
                while (cursor.hasNext()) {
                    Map.Entry<Object, Object> entry = cursor.next();
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        long gmtCreate = objectMapper.readTree(entry.getValue().toString()).get("gmt_create").asLong();
                        if (gmtCreate <= time) {
                            redisTemplate.opsForHash().delete(USER_ONLINE, entry.getKey());
                            DCJobLogger.log("clearUserSession delete key:{}", entry.getKey());
                        }
                    } catch (JsonMappingException e) {
                        throw new RuntimeException(e);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }

                }
            }
        }

    }
}

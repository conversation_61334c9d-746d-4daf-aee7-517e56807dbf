package com.dc.executor.job.handler;

import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.executor.service.ConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SchemaUpdate<PERSON><PERSON><PERSON>and<PERSON> extends AbstractJobHandler {

    @Resource
    private ConnectionService connectionService;

    @XxlJob("SchemaUpdateJobHandler")
    public ReturnT<String> schemaUpdateJobHandler(String param) throws Exception {
        try {
            // Schema 定时同步
            connectionService.updateSchema();
        } catch (Exception e) {
            log.error("call schemaUpdateJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

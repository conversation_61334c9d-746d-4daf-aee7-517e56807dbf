package com.dc.executor.job.handler;

import com.dc.executor.model.MockDataJobParam;
import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.enums.SqlExecuteStatusType;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.mysql.model.DcJobColumn;
import com.dc.repository.mysql.service.DcJobColumnService;
import com.dc.repository.redis.service.RedisService;
import com.dc.springboot.core.client.SummerMockDataClient;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.DcJobColumnDto;
import com.dc.springboot.core.model.database.MockDataMessage;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.utils.verification.VerificationException;
import com.dc.utils.verification.VerificationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 测试数据生成任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MockDataJobHandler extends AbstractJobHandler {

    @Resource
    private DcJobColumnService dcJobColumnService;

    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;

    @Resource
    private SummerMockDataClient summerMockDataClient;

    @Resource
    private JSON json;

    @Resource
    private RedisService redisService;

    @XxlJob("MockDataJobHandler")
    public ReturnT<String> mockDataJobHandler(String param) {

        log.info("receive mock data param: {}", param);
        MockDataJobParam mockDataJobParam;
        try {
            mockDataJobParam = json.getObjectMapper().readValue(param, MockDataJobParam.class);
        } catch (JsonProcessingException e) {
            log.error("parse param to MockDataJobParam error", e);
            DCJobLogger.log(e.getMessage());
            return ReturnT.SUCCESS;
        }

        //校验参数
        try {
            VerificationUtils.byAnnotation(mockDataJobParam);
        } catch (VerificationException e) {
            log.error("verify mockDataJobParam error", e);
            DCJobLogger.log(e.getMessage());
            return ReturnT.SUCCESS;
        }

        List<DcJobColumn> dcJobColumns = dcJobColumnService.findByDcJobId(mockDataJobParam.getDcJobId());
        if (CollectionUtils.isEmpty(dcJobColumns)) {
            DCJobLogger.log("没有找到相关的任务详情。");
            return ReturnT.SUCCESS;
        }

        // 获取连接实例信息
        DatabaseConnectionDto connection = null;
        if (StringUtils.isNotBlank(mockDataJobParam.getConnectId())) {
            DatabaseConnection dc = databaseConnectionMapper.getConnectionByUniqueKey(mockDataJobParam.getConnectId());
            connection = jobMapper.toDatabaseConnectionDto(dc);
        }

        Long logId = mockDataJobParam.getLogId();
        int jobId = jobLogMapper.load(logId).getJobId();

        this.updateTriggerTime(logId);

        this.updateBeginTriggerStatus(jobId, logId);

        // 实例删除或关闭,标注执行失败
        if (connection == null || connection.getIs_delete() == 1 || connection.getIs_active() == 0) {
            String errorMessage = "当前实例已删除或关闭,无法进行测试数据生成!";
            this.errorReturn(logId, errorMessage, jobId, false);
            return ReturnT.SUCCESS;
        }

        Client client = Client.getClient(jobConfig.getPath().getDcSummer());

        ConnectionConfig connectionConfig = connection.buildConnectionConfig(mockDataJobParam.getSchema(), mockDataJobParam.getCatalog());

        //构造请求信息
        MockDataMessage message = new MockDataMessage();
        message.setConnectionConfig(connectionConfig);
        message.setSchemaId(mockDataJobParam.getSchemaId());
        message.setSchema(mockDataJobParam.getSchema());
        message.setTableName(mockDataJobParam.getTableName());

        List<DcJobColumnDto> dcJobColumnDtos = dcJobColumns.stream()
                .map(dcJobColumn -> {
                    DcJobColumnDto dto = new DcJobColumnDto();
                    BeanUtils.copyProperties(dcJobColumn, dto);
                    try {
                        dto.setAlgorithm(json.getObjectMapper().readValue(dcJobColumn.getAlgorithm(), Map.class));
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                    return dto;
                }).collect(Collectors.toList());
        message.setDcJobColumnDtos(dcJobColumnDtos);

        message.setBatchLimitRows(mockDataJobParam.getBatchLimitRows());
        message.setRows(mockDataJobParam.getRows());
        message.setConflictManagement(mockDataJobParam.getConflictManagement());

        message.setRedisLog(true);
        String uuid = UUID.randomUUID().toString();
        message.setMockDataLogId(uuid + "_log_id");
        message.setMockDataStatus(uuid + "_status");

        summerMockDataClient.mockdataGenerate(client, message);

        //使用redis消费者/生产者模型 消费日志
        while (true) {

            // 读取队列中的数据
            String logMessage = redisService.lRpop(message.getMockDataLogId());
            if (logMessage != null) {
                log.info("Consumed logMessage: {}", logMessage);
                DCJobLogger.log(logMessage);
            }

            // 检查任务状态
            String taskStatus = redisService.get(message.getMockDataStatus());
            if ("completed".equals(taskStatus)) {
                if (redisService.lGetListSize(message.getMockDataLogId()) == 0) {
                    log.info("任务已完成，停止读取数据");

                    // 删除taskStatus
                    redisService.del("taskStatus");

                    break;
                }
            }

            // 休眠一段时间，避免过于频繁的轮询
            try {
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                DCJobLogger.schedulingTermination();
                Thread.currentThread().interrupt();
                return ReturnT.SUCCESS;
            }
        }

        redisService.del(message.getMockDataLogId());

        this.updateFinalStatus(SqlExecuteStatusType.success, logId, false, jobId); // 更新最终状态
        return ReturnT.SUCCESS;
    }
}

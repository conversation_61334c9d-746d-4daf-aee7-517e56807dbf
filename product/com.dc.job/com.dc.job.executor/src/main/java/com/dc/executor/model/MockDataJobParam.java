package com.dc.executor.model;

import com.dc.annotation.NotBlank;
import com.dc.annotation.NotNull;
import lombok.Data;

@Data
public class MockDataJobParam {

    @NotNull
    private Long logId;

    @NotNull
    private Integer jobId;

    @NotNull
    private Integer dcJobId;

    @NotBlank
    private String schemaId;

    /**
     * 实例ID
     */
    @NotBlank
    private String connectId;

    @NotBlank
    private String dbType;

    private String catalog;

    @NotBlank
    private String schema;

    @NotBlank
    private String tableName;

    /**
     * 安全规则集里的数据导入处理行数
     */
    @NotNull
    private int batchLimitRows;

    /**
     * 测试数据生成行数
     */
    @NotNull
    private Integer rows;

    /**
     * 冲突处理
     * 1: 遇到数据冲突则跳过
     * 2: 遇到数据冲突则替换
     */
    @NotBlank
    private String conflictManagement;
}

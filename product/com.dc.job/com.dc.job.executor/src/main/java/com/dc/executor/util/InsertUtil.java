//package com.dc.executor.util;
//
//import com.alibaba.fastjson.JSONArray;
//import com.dc.model.SqlFieldData;
//import org.apache.commons.lang3.StringUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//public class InsertUtil {
//
//    public static String generateInsertSql(String tableName, String jsonArray, long version_id, long recycle_row_id) {
//        if (null != jsonArray) {
//            List<SqlFieldData> list = JSONArray.parseArray(jsonArray, SqlFieldData.class);
//            if (null != list) {
//                List<String> fields = list.stream().map(SqlFieldData::getField_name).collect(Collectors.toList());
//                List<String> data = new ArrayList<>();
//                for (SqlFieldData item : list) {
//                    if (item.getField_type().equals("ROWID")) {
//                        continue;
//                    }
//                    if (null != item.getField_value()) {
//                        data.add(String.format("'%s'", item.getField_value()));
//                    } else {
//                        data.add(String.format("%s", "NULL"));
//                    }
//                    continue;
//                }
//                List<String> resultField = new ArrayList<>();
//                for (String field : fields) {
//                    if (field.equals("ROWID")) {
//                        continue;
//                    }
//                    resultField.add(String.format("recycle_column_%s",field));
//                }
//                String columns = StringUtils.join(resultField, "`,`");
//                String values = StringUtils.join(data, ",");
//
//                return String.format("INSERT INTO `%s` (%s,recycle_version_id,recycle_row_id,gmt_create,gmt_modified) VALUES (%s,%s,%s,now(6),now(6));", tableName, "`" + columns + "`", values, version_id, recycle_row_id);
//            }
//        }
//        return "";
//    }
//
//
//    public static String generateInsertSqlDataBack(String tableName, String jsonArray, long recycle_row_id, long recycle_backup_sql_id, long recycle_version_id) {
//        if (null != jsonArray) {
//            List<SqlFieldData> list = JSONArray.parseArray(jsonArray, SqlFieldData.class);
//            if (null != list) {
//                List<String> fields = list.stream().map(SqlFieldData::getField_name).collect(Collectors.toList());
//                List<String> data = new ArrayList<>();
//                for (SqlFieldData item : list) {
//                    if (item.getField_type().equals("ROWID")) {
//                        continue;
//                    }
//                    if (null != item.getField_value()) {
//                        data.add(String.format("'%s'", item.getField_value()));
//                    } else {
//                        data.add(String.format("%s", "NULL"));
//                    }
//                    continue;
//                }
//                List<String> resultField = new ArrayList<>();
//                for (String field : fields) {
//                    if (field.equals("ROWID")) {
//                        continue;
//                    }
//                    resultField.add(String.format("recycle_column_%s",field));
//                }
//                String columns = StringUtils.join(resultField, "`,`");
//                String values = StringUtils.join(data, ",");
//
//                return String.format("INSERT INTO `%s` (%s,recycle_version_id,recycle_row_id,recycle_backup_sql_id,gmt_create,gmt_modified) VALUES (%s,%s,%s,%s,now(6),now(6));", tableName, "`" + columns + "`", values, recycle_version_id, recycle_row_id, recycle_backup_sql_id);
//            }
//        }
//        return "";
//    }
//
//    public static String generateDataSqlForInsert(String tableName, Map<String,Object> dataMap, long recycle_row_id, long recycle_backup_sql_id, long recycle_version_id){
//        if (dataMap != null) {
//            List<String> fileds = new ArrayList<>();
//            List<String> data = new ArrayList<>();
//            for (String key : dataMap.keySet()) {
//                if (key.contains("recycle_column")) {
//                    fileds.add(key);
//                    data.add(String.format("'%s'",dataMap.get(key) != null ? dataMap.get(key).toString() : "NULL"));
//                }
//            }
//            String columns = StringUtils.join(fileds,"`,`");
//            String values = StringUtils.join(data, ",");
//
//            return String.format("INSERT INTO `%s` (%s,recycle_version_id,recycle_row_id,recycle_backup_sql_id,gmt_create,gmt_modified) VALUES (%s,%s,%s,%s,now(6),now(6));", tableName, "`" + columns + "`", values, recycle_version_id, recycle_row_id, recycle_backup_sql_id);
//        }
//        return "";
//    }
//}

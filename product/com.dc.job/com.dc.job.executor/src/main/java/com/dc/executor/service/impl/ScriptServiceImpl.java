package com.dc.executor.service.impl;

import com.dc.executor.service.ScriptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ScriptServiceImpl implements ScriptService {



    @Override
    public String doHandle(String param) {
        try {
            Process process = Runtime.getRuntime().exec(param);
        } catch (Exception e) {
            log.error("call doHandle function error!", e);
        }
        return null;
    }

}

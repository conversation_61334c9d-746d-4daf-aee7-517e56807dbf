package com.dc.executor.model;

import com.dc.springboot.core.model.recovery.BackupModel;
import com.dc.springboot.core.model.sensitive.SqlDesensitization;
import lombok.Data;

import java.util.List;

@Data
public class BatchExecuteModel {

    private BackupModel backupModel;
    private String operation;
    private List<String> primaryKeyColumns;
    private String sql;
    private SqlDesensitization sqlDesensitization = new SqlDesensitization();

}

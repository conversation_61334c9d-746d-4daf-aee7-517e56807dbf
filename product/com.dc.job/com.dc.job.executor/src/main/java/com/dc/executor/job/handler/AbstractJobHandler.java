package com.dc.executor.job.handler;

import com.dc.executor.component.JobMapper;
import com.dc.executor.config.JobConfig;
import com.dc.executor.util.DCJobLogger;
import com.dc.job.enums.SqlExecuteStatusType;
import com.dc.job.util.IpUtil;
import com.dc.repository.mysql.mapper.DCJobMapper;
import com.dc.repository.mysql.mapper.JobInfoMapper;
import com.dc.repository.mysql.mapper.JobLogMapper;
import com.dc.springboot.core.component.JSON;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public abstract class AbstractJobHandler {

    @Resource
    protected JSON json;

    @Resource
    protected JobConfig jobConfig;

    @Resource
    protected JobLogMapper jobLogMapper;

    @Resource
    protected JobInfoMapper jobInfoDao;

    @Value("${server.port:8082}")
    protected int port;

    @Value("${xxl.job.executor.ip}")
    protected String ip;

    @Resource
    protected DCJobMapper dcJobMapper;

    @Resource
    protected JobMapper jobMapper;

    protected final Gson gson = new GsonBuilder().serializeNulls().create();

    /**
     * 更新最终执行状态
     * @param sqlExecuteStatusType 状态
     * @param logId xxl_job_log - id
     * @param isRepeat 明年此时此刻，是否执行。
     * @param jobId xxl_job_log - job_id
     */
    protected void updateFinalStatus(SqlExecuteStatusType sqlExecuteStatusType, long logId, boolean isRepeat, int jobId) {
        if (sqlExecuteStatusType != null) {
            this.updateStatus(logId, sqlExecuteStatusType.getValue());
        }
        if (sqlExecuteStatusType == SqlExecuteStatusType.fail) {
            DCJobLogger.schedulingFailed();
        } else if (sqlExecuteStatusType == SqlExecuteStatusType.success) {
            DCJobLogger.schedulingSuccessful();
        } else if (sqlExecuteStatusType == SqlExecuteStatusType.termination) {
            DCJobLogger.schedulingTermination();
        }

        if (!isRepeat) {
            this.updateTriggerStatus(jobId, 0);
        }
    }

    protected void updateStatus(long logId, int status) {
        Date date = this.getDate();
        jobLogMapper.updateTriggerStatus(logId, status, date);
    }

    protected void updateTriggerStatus(int jobId, int status) {
        jobInfoDao.updateStatusByJobId(status, jobId);
    }

    protected Date getDate() {
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        return calendar.getTime();
    }

    protected void updateTriggerTime(long logId) {
        Date date = this.getDate();
        jobLogMapper.updateTriggerTime(logId, date);
    }

    protected void updateBeginTriggerStatus(int jobId, long logId) {
        int jobTriggerCount = jobLogMapper.findJobTriggerCount(jobId);
        long triggerCount = jobTriggerCount + 1L;
        jobLogMapper.updateBeginTriggerStatus(logId, SqlExecuteStatusType.running.getValue(), triggerCount);

        jobLogMapper.updateExecutorTomcatAddress(logId, IpUtil.getIpPort(ip, port)); // 更新执行器服务的ip和port
    }

    protected void errorReturn(long logId, String errorMessage, int jobId, boolean isRepeat) {
        DCJobLogger.log(errorMessage);

        this.updateStatus(logId, SqlExecuteStatusType.fail.getValue());

        if (!isRepeat) {
            this.updateTriggerStatus(jobId, 0);
        }

        jobLogMapper.updateExecutorTomcatAddress(logId, IpUtil.getIpPort(ip, port)); // 更新执行器服务的ip和port
    }

}

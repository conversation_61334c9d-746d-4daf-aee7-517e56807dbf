package com.dc.executor.model;


import com.dc.annotation.NotBlank;
import com.dc.annotation.NotNull;
import com.dc.springboot.core.model.database.StructCompareConfigType;
import lombok.Data;

import java.util.List;

@Data
public class StructCompareJobParam {

    @NotNull
    private Integer dcJobId;

    @NotBlank
    private String sourceConnectId;

    @NotBlank
    private String targetConnectId;

    @NotNull
    private List<StructCompareConfigType> configTypes;

    @NotNull
    private Long logId;

}

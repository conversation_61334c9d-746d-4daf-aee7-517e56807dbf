//package com.dc.executor.util;
//
//import com.dc.springboot.component.JSON;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@SuppressWarnings("all")
//public class JsonUtil {
//
//
//	/**
//	 * 将json转化为对象
//	 * @param jsonStr
//	 * @return
//	 */
//	public static Object Json2Obj (String jsonStr,Class clazz){
//        return  JSON.parseObject(jsonStr,clazz);
//    }
//	/**
//	 * @Title: parseJSON2List
//	 * @Class: JsonUtil.java
//	 * @Description: Json字符串转List<Map<String, Object>>
//
//	 * @param jsonStr
//	 * @return
//
//	 * @AuthorOriginally DHL
//	 * @date 2015年9月24日 上午9:25:23
//	 */
//	public static List<Map<String, Object>> parseJSON2List(String jsonStr){
//		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
//		list = JSON.parseObject(jsonStr, list.getClass());
//        return list;
//    }
//
//    /**
//     * @Title: parseJSON2Map
//     * @Class: JsonUtil.java
//     * @Description: Json字符串转Map<String, Object>
//
//     * @param jsonStr
//     * @return
//
//     * @AuthorOriginally DHL
//     * @date 2015年9月24日 上午9:25:51
//     */
//    public static Map<String, Object> parseJSON2Map(String jsonStr){
//        Map<String, Object> map = new HashMap<String, Object>();
//        map = JSON.parseObject(jsonStr, map.getClass());
//        return map;
//    }
//
//    /**
//	 * @Title: writeJsonByFilter
//	 * @Class: BaseController.java
//	 * @Description: 将对象转换成JSON字符串，并响应回前台
//
//	 *@param object
//	 *@param includesProperties	需要转换的属性
//	 *@param excludesProperties 不需要转换的属性
//
//	 * @AuthorOriginally DHL
//	 * @date 2014年4月18日 上午8:30:05
//	 */
//
//
//	/**
//	 * @Title: writeJson
//	 * @Class: BaseController.java
//	 * @Description: 将对象转换成JSON字符串，并响应回前台
//
//	 *@param object
//
//	 * @AuthorOriginally DHL
//	 * @date 2014年4月18日 上午8:33:29
//	 */
//
//	/**
//	 * @Title: writeJsonByIncludesProperties
//	 * @Class: BaseController.java
//	 * @Description: 将对象转换成JSON字符串，并响应回前台
//
//	 *@param object
//	 *@param includesProperties 需要转换的属性
//
//	 * @AuthorOriginally DHL
//	 * @date 2014年4月18日 上午8:33:57
//	 */
//
//	/**
//	 * @Title: writeJsonByExcludesProperties
//	 * @Class: BaseController.java
//	 * @Description: 将对象转换成JSON字符串，并响应回前台
//
//	 *@param object
//	 *@param excludesProperties 不需要转换的属性
//
//	 * @AuthorOriginally DHL
//	 * @date 2014年4月18日 上午8:34:30
//	 */
//
//	public static List parseJsonString(String str, Class cls){
//		List list = new ArrayList();
//		List parselist = (List) JsonUtil.Json2Obj(str, List.class);
//		for (Object o : parselist) {
//			list.add(JsonUtil.Json2Obj(o.toString(), cls));
//		}
//		return list;
//	}
//
//	public static void main(String[] args) {
//		Map<String, Object> map = new HashMap<String, Object>();
//		Map<String, Object> map1 = new HashMap<String, Object>();
//		map1.put("map1", 1);
//		map.put("map", "map");
//		map.put("map1", map1);
//
//		String jsonString = JSON.toJSONString(map);
//		System.out.println(jsonString);
//
//		Map<String, Object> parseJSON2Map = parseJSON2Map(jsonString);
//		System.out.println();
//	}
//}

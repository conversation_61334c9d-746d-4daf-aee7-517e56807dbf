package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component(value = "JOBSPLV")
public class SyncPaInstanceVersionHandler {

    private static Logger logger = LoggerFactory.getLogger(SyncPaInstanceVersionHandler.class);

    @Resource
    private JobConfig jobConfig;

    private static final String summerInterfaceSyncPaInstanceVersion = "/dc-summer/pa/instance/sync/entity-version";

    @XxlJob("SyncPaInstanceVersionHandler")
    public ReturnT<String> syncPaInstanceVersionHandler(String param) throws Exception {
        ReturnT<String> result = ReturnT.SUCCESS;
        try {
            logger.info("Start SyncPaInstanceVersionHandler Synchronize");
            HttpClientUtils.doGet(jobConfig.getPath().getDcSummer() + summerInterfaceSyncPaInstanceVersion, null);
        } catch (Exception e) {
            logger.error("call SyncPaInstanceVersionHandler error!", e);
        }
        return result;
    }
}

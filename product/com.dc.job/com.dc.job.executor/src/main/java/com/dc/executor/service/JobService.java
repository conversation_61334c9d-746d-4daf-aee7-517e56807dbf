package com.dc.executor.service;

public interface JobService {

    /**
     * add job
     *
     * @param job_cron, executor_handler, group_id
     * @return
     */
    public String add(String job_cron, String app_name, String executor_handler, String param);

    /**
     * update job
     *
     * @param job_cron, executor_handler
     * @return
     */
    public String update(String job_cron, String executor_handler);

    /**
     * remove job
     * 	 *
     * @param executor_handler
     * @return
     */
    public String remove(String executor_handler);

    /**
     * start job
     *
     * @param executor_handler
     * @return
     */
    public String start( String executor_handler);

    /**
     * stop job
     *
     * @param executor_handler
     * @return
     */
    public String stop( String executor_handler);

}

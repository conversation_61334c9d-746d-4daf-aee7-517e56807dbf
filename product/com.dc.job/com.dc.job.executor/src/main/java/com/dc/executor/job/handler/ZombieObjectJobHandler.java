package com.dc.executor.job.handler;

import com.dc.executor.component.JobMapper;
import com.dc.executor.type.TaskStatus;
import com.dc.executor.util.ClientUtils;
import com.dc.executor.util.DCJobLogger;
import com.dc.executor.util.ParallelStreamUtil;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.redis.service.RedisService;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.client.ProxyInternalClient;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.ExecutorData;
import com.dc.springboot.core.model.database.ConnectionMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.dc.executor.util.JobCommonUtil.sleepSeconds;

/**
 * 僵尸对象 任务处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ZombieObjectJobHandler extends AbstractJobHandler {

    /**
     * 跟php约定好的 redis key：1 是进行中 2 是已结束 3是异常退出
     */
    private final String REDIS_KEY_PATTERN = "dc_metadata_sync_%s";

    @Value("${zombie-object.handle-thread:2}")
    private int handleThread;

    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;

    @Resource
    private BackendClient backendClient;

    @Resource
    private ProxyInternalClient proxyInternalClient;

    @Resource
    private RedisService redisService;

    @Resource
    private JobMapper jobMapper;

    @XxlJob("ZombieObjectJobHandler")
    public ReturnT<String> zombieObjectJobHandler(String param) throws Exception {
        DCJobLogger.log("zombieObjectJobHandler start");
        try {
            List<DatabaseConnection> zombieInstances = databaseConnectionMapper.getZombieInstances();
            DCJobLogger.log("zombieObjectJobHandler zombieInstances:{}", zombieInstances.stream().map(DatabaseConnection::getInstance_name).collect(Collectors.toList()));

            Client backendClient = Client.getClient(jobConfig.getPath().getDcBackend());
            Map<Integer, ExecutorData> executorDataMap = this.backendClient.getExecutorDataMap(backendClient);

            ParallelStreamUtil.parallelForEach(
                    zombieInstances,
                    handleThread,
                    instance -> {
                        syncDcMetadata(backendClient, instance);
                        updateDcMetadata(executorDataMap, instance);
                    },
                    1,
                    TimeUnit.DAYS
            );
        } catch (Exception e) {
            log.error("call zombieObjectJobHandler error!", e);
            DCJobLogger.log(e);
        }
        DCJobLogger.log("zombieObjectJobHandler finish");
        return ReturnT.SUCCESS;
    }

    /**
     * 调用php 接口，同步数据字典
     *
     * @param zombieInstance 开启僵尸对象的实例，目前只有oracle 有此开关
     */
    public void syncDcMetadata(Client client, DatabaseConnection zombieInstance) {
        DCJobLogger.log("call php client sync dc_metadata, instance name is : {}", zombieInstance.getInstance_name());

        backendClient.syncDcMetadata(client, zombieInstance.getUnique_key());

        sleepSeconds(30);

        String redisKey = String.format(REDIS_KEY_PATTERN, zombieInstance.getUnique_key());
        while (true) {
            String statusCode = redisService.get(redisKey);
            if (statusCode == null) {
                DCJobLogger.log("syncDcMetadata status code is null, instance name is : {}", zombieInstance.getInstance_name());
                break;
            }
            TaskStatus taskStatus = TaskStatus.fromCode(statusCode);
            if (taskStatus != TaskStatus.IN_PROGRESS) {
                redisService.del(redisKey);
                DCJobLogger.log("syncDcMetadata status code is {}, instance name is : {}", statusCode, zombieInstance.getInstance_name());
                break;
            }
            // 休眠60s时间，避免过于频繁的轮询
            sleepSeconds(60);
        }
        DCJobLogger.log("syncDcMetadata finish, instance name is : {}", zombieInstance.getInstance_name());
    }

    /**
     * 调用proxy 接口，更新对象最后访问时间
     *
     * @param zombieInstance 开启僵尸对象的实例，目前只有oracle 有此开关
     */
    public void updateDcMetadata(Map<Integer, ExecutorData> executorDataMap, DatabaseConnection zombieInstance) {
        DCJobLogger.log("call proxy client update dc_metadata, instance name is : {}", zombieInstance.getInstance_name());

        ConnectionMessage connectionMessage = new ConnectionMessage();
        connectionMessage.setConnectionConfig(jobMapper.toDatabaseConnectionDto(zombieInstance).buildConnectionConfig(null, null));

        ExecutorData executorData = executorDataMap.get(zombieInstance.getExecutor());

        proxyInternalClient.syncZombieObject(Client.getClient(executorData.getDcIceage()), connectionMessage);
        DCJobLogger.log("proxy client update dc_metadata success, instance name is : {}", zombieInstance.getInstance_name());
    }
}

package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.model.data.Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class CheckPrivilegeExpireJobHandler {

    @Resource
    private BackendClient backendClient;

    @Resource
    private JobConfig jobConfig;

    @XxlJob("CheckPrivilegeExpireJobHandler")
    public ReturnT<String> checkPrivilegeExpireJobHandler(String param) throws Exception {

        DCJobLogger.log("checkPrivilegeExpireJobHandler start");
        try{
            backendClient.notifyPrivilegeExpire(Client.getClient(jobConfig.getPath().getDcBackend()));
        }catch (Exception e){
            log.error("checkPrivilegeExpireJobHandler error",e);
            DCJobLogger.log(e);
        }
        DCJobLogger.log("checkPrivilegeExpireJobHandler finish");

        return ReturnT.SUCCESS;
    }

}

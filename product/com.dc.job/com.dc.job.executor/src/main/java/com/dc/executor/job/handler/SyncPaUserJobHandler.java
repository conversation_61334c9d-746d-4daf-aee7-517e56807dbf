package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.job.log.XxlJobLogger;
import com.dc.utils.http.HttpClientUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;

@Component(value = "JOBSPU")
public class SyncPaUserJobHandler {

    private static Logger logger = LoggerFactory.getLogger(SyncPaUserJobHandler.class);

    @Resource
    private JobConfig jobConfig;

    private static final String summerSyncPaUser = "/dc-summer/pa/user/sync-user";

    @XxlJob("SyncPaUserJobHandler")
    public ReturnT<String> syncPaUserJobHandler(String param) throws Exception {
        ReturnT<String> result = ReturnT.SUCCESS;
        try {
            URL url = new URL(jobConfig.getPath().getDcSummer() + summerSyncPaUser);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "text/event-stream");
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (StringUtils.isNotBlank(line) && line.contains("error")) {
                        result = ReturnT.FAIL;
                    }
                    XxlJobLogger.log("\n----------- summer log -----------" + line);
                }
            } finally {
                connection.disconnect();
            }
        } catch (Exception e) {
            logger.error("call syncPaUserJobHandler error!", e);
        }
        return result;
    }
}

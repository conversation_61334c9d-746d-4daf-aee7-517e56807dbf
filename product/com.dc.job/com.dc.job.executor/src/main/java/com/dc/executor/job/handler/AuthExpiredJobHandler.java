package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class AuthExpiredJobHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    @XxlJob("RuleExpiredJobHandler")
    public ReturnT<String> expireJobHandler(String param) throws Exception {
        try {
            // v3不在统一服务器
            HttpClientUtils.doGet(jobConfig.getPath().getDcBackend() + "/" + param, null);
        } catch (Exception e) {
            log.error("call expireJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

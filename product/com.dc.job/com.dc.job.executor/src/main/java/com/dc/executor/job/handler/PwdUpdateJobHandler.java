package com.dc.executor.job.handler;

import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class PwdUpdateJob<PERSON>andler extends AbstractJobHandler {

    @XxlJob("PwdUpdateJobHandler")
    public ReturnT<String> pwdUpdateJobHandler(String param) throws Exception {
        try {
            Date date = new Date();
            int hours = date.getHours();
            int minutes = date.getMinutes();
            if (hours == 0 && minutes >= 0 && minutes <= 9) {
                log.info("Start Password Update");
                HttpClientUtils.doPost(jobConfig.getPath().getDcBackend() + "/" + param, new StringEntity(""));
            } else {
                log.info("Unexpected Password Update Trigger");
            }
        } catch (Exception e) {
            log.error("call pwdUpdateJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }
}

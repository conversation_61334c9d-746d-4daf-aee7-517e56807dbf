package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class TruncatePaDbTableFieldJobHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    private static final String summerTruncateTable = "/dc-summer/pa/user/delete-table?type=4";

    @XxlJob("TruncatePaDbTableFieldJobHandler")
    public ReturnT<String> truncateDBTableField(String param) throws Exception {
        try {
            log.info("Start TruncatePaDbTableFieldJobHandler !");
            // 定时清理，避免缓存很久
            HttpClientUtils.doGet(jobConfig.getPath().getDcSummer() + summerTruncateTable, null);
        } catch (Exception e) {
            log.error("call TruncatePaDbTableFieldJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

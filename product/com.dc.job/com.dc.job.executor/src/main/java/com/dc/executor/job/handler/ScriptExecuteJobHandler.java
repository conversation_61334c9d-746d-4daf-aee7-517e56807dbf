package com.dc.executor.job.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.executor.model.ApplyContent;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.column.AuthGrant;
import com.dc.repository.mysql.column.AuthRevoke;
import com.dc.repository.mysql.column.ResourceContent;
import com.dc.repository.mysql.column.SchemaAuth;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.repository.mysql.type.GrantObjectType;
import com.dc.repository.redis.service.RedisService;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.client.SummerWorkOrderClient;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.ExecutorData;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.type.SqlScriptType;
import com.dc.springboot.core.model.workorder.ExecuteOrderBatchMessage;
import com.dc.springboot.core.model.workorder.ExecuteOrderMessage;
import com.dc.springboot.core.model.workorder.ExecuteOrderModel;
import com.dc.springboot.core.model.workorder.OperatorUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ScriptExecuteJobHandler extends AbstractJobHandler {

    @Resource
    private OrderExecuteMapper orderExecuteDao;
    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;
    @Resource
    private SchemaMapper schemaMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private DcDbResourceMapper dcDbResourceMapper;
    @Resource
    private SummerWorkOrderClient workOrderClient;
    @Resource
    private RedisService redisService;

    @Resource
    private DcAccountObjPrivsMapper dcAccountObjPrivsMapper;

    @Resource
    private DcAccountOtherPrivsMapper dcAccountOtherPrivsMapper;

    @Resource
    private DcDbResourceAccountMapper dcDbResourceAccountMapper;

    @Resource
    private BackendClient backendClient;


    @XxlJob("ScriptExecuteJobHandler")
    public ReturnT<String> scriptExecuteJobHandler(String param) throws Exception {

        try {

            // 定时查找 work_order_execute 表
            long currentTime = System.currentTimeMillis() / 1000;
            List<OrderExecute> orderTimingScript = orderExecuteDao.getOrderTimingScript((int) currentTime);
            if (orderTimingScript == null || orderTimingScript.size() == 0) {
                return ReturnT.SUCCESS;
            }

            Map<Integer, ExecutorData> executorDataMap = backendClient.getExecutorDataMap(Client.getClient(jobConfig.getPath().getDcBackend()));

            log.info("开始定时执行, 定时工单的数量为: " + orderTimingScript.size());

            Map<Integer, List<OrderExecute>> groups = orderTimingScript.stream().collect(Collectors.groupingBy(OrderExecute::getOrder_id));
            for (Map.Entry<Integer, List<OrderExecute>> group : groups.entrySet()) {
                List<OrderExecute> orderExecutes = group.getValue();
                boolean isBatch = orderExecutes.get(0).getOrder_schema_task_id() != 0;

                if (isBatch) {
                    Map<ExecutorData, ExecuteOrderBatchMessage> executorDataListMap = new HashMap<>();
                    for (OrderExecute execute : orderExecutes) {
                        String userId = StringUtils.isBlank(execute.getUser_id()) ? execute.getApply_user_id() : execute.getUser_id();
                        ApplyContent applyContent;
                        try {
                            String applyContentString = execute.getApply_content();
                            applyContent = JSON.parseObject(applyContentString, ApplyContent.class);
                        } catch (Exception e) {
                            log.error("解析apply_content出错！", e);
                            continue;
                        }
                        List<com.dc.executor.model.SchemaInfo> schemaData = applyContent.getSchema_data();
                        DatabaseConnection instance = getInstance(schemaData.get(0).getConnect_id());

                        String isParallel = execute.getIs_parallel() == null ? "0" : execute.getIs_parallel().toString();
                        InetAddress addr = InetAddress.getLocalHost();
                        List<String> orgList = this.getOrgList(userId);
                        List<String> organizationNames = this.getOrganizationNames(userId);
                        ExecuteOrderModel executeOrderModel = new ExecuteOrderModel();
                        executeOrderModel.setId(execute.getOrder_id());
                        executeOrderModel.setTimeScript(true);
                        executeOrderModel.setOrgList(orgList);
                        executeOrderModel.setOrganizationNames(organizationNames);
                        executeOrderModel.setSqlScriptType(SqlScriptType.ORIGIN_SCRIPT);
                        executeOrderModel.setIp(addr.getHostAddress());
                        executeOrderModel.setHostname(addr.getHostName());
                        executeOrderModel.setIsParallel(isParallel);
                        executeOrderModel.setActiveInstance(true);
                        executeOrderModel.setActiveSchema(true);

                        User user = this.userMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getUniqueKey, userId));
                        if (user == null) {
                            log.info("查询user失败！");
                            continue;
                        }

                        String name = String.format("%s(%s)", user.getUsername(), user.getRealName());

                        OperatorUserInfo operatorUserInfo = new OperatorUserInfo(userId, user.getRealName(), orgList, organizationNames, name);

                        executorDataListMap.computeIfPresent(executorDataMap.get(instance.getExecutor()), (k, v) -> {
                            v.getSchema_list().addAll(jobMapper.toSchemaInfo(schemaData));
                            return v;
                        });

                        executorDataListMap.computeIfAbsent(executorDataMap.get(instance.getExecutor()), k -> {
                            ExecuteOrderBatchMessage executeOrderBatchMessage = new ExecuteOrderBatchMessage();
                            executeOrderBatchMessage.setExecuteOrderModel(executeOrderModel);
                            executeOrderBatchMessage.setOperatorUserInfo(operatorUserInfo);
                            executeOrderBatchMessage.setSchema_list(jobMapper.toSchemaInfo(applyContent.getSchema_data()));
                            return executeOrderBatchMessage;
                        });
                    }

                    for (Map.Entry<ExecutorData, ExecuteOrderBatchMessage> entry : executorDataListMap.entrySet()) {
                        ExecuteOrderBatchMessage executeOrderBatchMessage = entry.getValue();
                        ExecutorData executorData = entry.getKey();
                        log.info("工单[" + executeOrderBatchMessage.getExecuteOrderModel().getId() + "]准备发送到执行器(" + executorData.getDcSummer() + ").");
                        Client client = Client.getClient(executorData.getDcSummer());
                        ResponseEntity<Result<Object>> resultResponseEntity = workOrderClient.executeOrder(client, executeOrderBatchMessage);
                        HttpHeaders headers = resultResponseEntity.getHeaders();
                        String cookieValue = headers.getFirst(HttpHeaders.SET_COOKIE);
                        //order_id md5加密
                        String redisKey = "dc_route_" + DigestUtils.md5DigestAsHex(executeOrderBatchMessage.getExecuteOrderModel().getId().toString().getBytes());
                        if (cookieValue != null) {
                            redisService.set(redisKey, cookieValue);
                        }
                        redisService.expire(redisKey, 60 * 60 * 8);
                        log.info("工单[" + executeOrderBatchMessage.getExecuteOrderModel().getId() + "]已发送执行器(" + executorData.getDcSummer() + "),待执行.");
                    }

                    continue;
                }

                for (OrderExecute orderExecute : orderExecutes) {

                    String userId = StringUtils.isBlank(orderExecute.getUser_id()) ? orderExecute.getApply_user_id() : orderExecute.getUser_id();
                    if (StringUtils.isBlank(userId)) {
                        log.info("user id 为 null！");
                        continue;
                    }

                    // 解析实例id等存储的信息
                    ApplyContent applyContent;
                    try {
                        String applyContentString = orderExecute.getApply_content();
                        applyContent = JSON.parseObject(applyContentString, ApplyContent.class);
                    } catch (Exception e) {
                        log.error("解析apply_content出错！", e);
                        continue;
                    }

                    boolean isActiveInstance = true;
                    boolean isActiveSchema = true;

                    boolean isPrivilegeChange = false;

                    ConnectionConfig connectionConfig;
                    ExecutorData executorData;
                    if (CollectionUtils.isNotEmpty(applyContent.getResource_list())) {
                        isPrivilegeChange = true;
                        DcDbResource dcDbResource = dcDbResourceMapper.getResource(new LambdaQueryWrapper<DcDbResource>().eq(DcDbResource::getUnique_key, applyContent.getAccount_change_apply_resource_id()));
                        if (dcDbResource == null) {
                            continue;
                        }
                        connectionConfig = jobMapper.toDatabaseConnectionDto(dcDbResource).buildConnectionConfig(null, null);
                        executorData = executorDataMap.get(dcDbResource.getExecutor());
                    } else {
                        // 查询实例和schema信息
                        DatabaseConnection instance = getInstance(applyContent.getConnect_id());
                        Schema schema = getSchema(applyContent.getSchema_id());

                        if (instance == null || schema == null) {
                            log.info("查询实例和schema失败！");
                            continue;
                        }

                        DatabaseConnection activeConnection = this.databaseConnectionMapper.getActiveConnectionByUniqueKey(instance.getUnique_key());
                        if (activeConnection == null) {
                            isActiveInstance = false;
                        }
                        if (schema.getIs_delete() == 1) {
                            isActiveSchema = false;
                        }
                        executorData = executorDataMap.get(instance.getExecutor());

                        // 实例连接配置信息
                        connectionConfig = jobMapper.toDatabaseConnectionDto(instance).buildConnectionConfig(schema.getSchema_name(), schema.getCatalog_name());
                    }

                    // order具体信息
                    String isParallel = orderExecute.getIs_parallel() == null ? "0" : orderExecute.getIs_parallel().toString();
                    InetAddress addr = InetAddress.getLocalHost();
                    List<String> orgList = this.getOrgList(userId);
                    List<String> organizationNames = this.getOrganizationNames(userId);

                    ExecuteOrderModel executeOrderModel = new ExecuteOrderModel();
                    executeOrderModel.setId(orderExecute.getOrder_id());
                    executeOrderModel.setTimeScript(true);
                    executeOrderModel.setOrgList(orgList);
                    executeOrderModel.setOrganizationNames(organizationNames);
                    executeOrderModel.setSqlScriptType(SqlScriptType.ORIGIN_SCRIPT);
                    executeOrderModel.setIp(addr.getHostAddress());
                    executeOrderModel.setHostname(addr.getHostName());
                    executeOrderModel.setIsParallel(isParallel);
                    executeOrderModel.setActiveInstance(isActiveInstance);
                    executeOrderModel.setActiveSchema(isActiveSchema);
                    // 操作用户信息
                    User user = this.userMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getUniqueKey, userId));
                    if (user == null) {
                        log.info("查询user失败！");
                        continue;
                    }

                    String name = String.format("%s(%s)", user.getUsername(), user.getRealName());

                    OperatorUserInfo operatorUserInfo = new OperatorUserInfo(userId, user.getRealName(), orgList, organizationNames, name);

                    // 传输到summer的封装对象

                    log.info("工单[" + orderExecute.getOrder_id() + "]准备发送到执行器(" + executorData.getDcSummer() + ").");


                    ExecuteOrderMessage message = new ExecuteOrderMessage();
                    message.setConnectionConfig(connectionConfig);
                    message.setOperatorUserInfo(operatorUserInfo);
                    message.setExecuteOrderModel(executeOrderModel);

                    Client client = Client.getClient(executorData.getDcSummer());
                    ResponseEntity<Result<Object>> resultResponseEntity = workOrderClient.executeOrder(client, message);
                    HttpHeaders headers = resultResponseEntity.getHeaders();
                    String cookieValue = headers.getFirst(HttpHeaders.SET_COOKIE);
                    //order_id md5加密
                    String redisKey = "dc_route_" + DigestUtils.md5DigestAsHex(orderExecute.getOrder_id().toString().getBytes());
                    redisService.set(redisKey, cookieValue);
                    redisService.expire(redisKey, 60 * 60 * 8);

                    log.info("工单[" + orderExecute.getOrder_id() + "]已发送执行器(" + executorData.getDcSummer() + "),待执行.");

                    if (isPrivilegeChange) {
                        AuthGrant authGrant = applyContent.getAuth_grant();
                        AuthRevoke authRevoke = applyContent.getAuth_revoke();
                        DcDbResource resource = getResource(applyContent.getResource_list().get(0).getResource_id());
                        for (ResourceContent resourceContent : applyContent.getResource_list()) {
                            DcDbResourceAccount account = getResourceAccount(resourceContent.getAccount_id());
                            if (!authGrant.getSchema_auth().isEmpty()) {
                                saveSchemaPrivilege(authGrant.getSchema_auth(), resource.getId(), account.getId(), account.getUsername(), resource.getUsername());
                            }
                            if (StringUtils.isNotBlank(authGrant.getOther_auth())) {
                                saveOtherPrivilege(authGrant.getOther_auth(), resource.getId(), account.getId());
                            }
                            removeSchemaPrivilege(authRevoke.getSchema_auth(), resource.getId(), account.getId(), account.getUsername());
                        }
                    }
                }

            }

        } catch (Exception e) {
            log.error("call scriptExecuteJobHandler error!", e);
        }

        return ReturnT.SUCCESS;
    }

    private List<String> getOrgList(String userId) {
        try {
            User userByUniqueKey = this.userMapper.selectOne(Wrappers.<User>lambdaQuery()
                    .eq(User::getUniqueKey, userId)
            );
            return new ArrayList<>(List.of(userByUniqueKey.getROrgIds().split("\\,")));
        } catch (Exception e) {
            log.error("get org list error!", e);
        }

        return new ArrayList<>();
    }

    private List<String> getOrganizationNames(String userId) {
        List<String> organizationNames = new ArrayList<>();

        try {
            User userByUniqueKey = this.userMapper.selectOne(Wrappers.<User>lambdaQuery()
                    .eq(User::getUniqueKey, userId)
            );
            List<String> organizations = new ArrayList<>(List.of(userByUniqueKey.getROrgIds().split("\\,")));
            List<Organization> organizationList = this.organizationMapper.selectList(Wrappers.<Organization>lambdaQuery()
                    .in(Organization::getUniqueKey, organizations)
                    .orderByAsc(Organization::getGmtCreate)
            );

            Collections.reverse(organizationList); // 翻转组织名称显示顺序

            for (Organization organization : organizationList) {
                String orgNames = organization.getOrgNames();
                List<String> list = gson.fromJson(orgNames, List.class);
                String organizationName = String.join("/", list);
                organizationNames.add(organizationName);
            }
        } catch (Exception e) {
            log.error("get organization names error!", e);
        }

        return organizationNames;
    }

    public Schema getSchema(String schemaId) {
        Map<String, Object> map = new HashMap<>();
        map.put("unique_key", schemaId);
        List<Schema> schemaList = this.schemaMapper.selectByMap(map);
        if (schemaList != null && !schemaList.isEmpty()) {
            return schemaList.get(0);
        }
        return null;
    }

    public DatabaseConnection getInstance(String connect_id) {
        Map<String, Object> map = new HashMap<>();
        map.put("unique_key", connect_id);
        List<DatabaseConnection> instanceList = this.databaseConnectionMapper.selectByMap(map);
        if (instanceList != null && !instanceList.isEmpty()) {
            for (DatabaseConnection databaseConnection : instanceList) {
                if (databaseConnection.getIs_delete() == 0) {
                    return databaseConnection;
                }
            }
            return instanceList.get(0);
        }
        return null;
    }

    private void saveSchemaPrivilege(List<SchemaAuth> schemaAuthList, Integer resourceId, Integer accountId, String grantee, String grantor) {
        List<DcAccountObjPrivs> schemas = schemaAuthList.stream().map(s -> {
            DcAccountObjPrivs priv = new DcAccountObjPrivs();
            priv.setResourceId(resourceId);
            priv.setAccountId(accountId);
            priv.setIsExpire((byte) 0);
            priv.setGmtCreate(LocalDateTime.now());
            priv.setGmtModified(LocalDateTime.now());
            priv.setGrantee(grantee);
            priv.setPrivilege(s.getPrivilege());
            priv.setCatalogName("");
            priv.setSchemaName(s.getSchema_name());
            priv.setGrantor(grantor);
            priv.setIsDelete((byte) 0);
            priv.setObjectType(GrantObjectType.SCHEMA.getName());
            priv.setObjectName("");
            if (StringUtils.isNotBlank(s.getPrivilege_expire())) {
                priv.setIsExpire((byte) 1);
                priv.setPrivilegeExpire(LocalDateTime.parse(s.getPrivilege_expire(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            priv.setGrantable((byte) s.getGrantable());
            priv.setHierarchy((byte) 0);
            return priv;
        }).collect(Collectors.toList());

        dcAccountObjPrivsMapper.replaceInto(schemas);
    }

    private void removeSchemaPrivilege(List<SchemaAuth> schemaAuthList, Integer resourceId, Integer accountId, String grantee) {
        for (SchemaAuth schemaAuth : schemaAuthList) {
            LambdaQueryWrapper<DcAccountObjPrivs> qw = Wrappers.<DcAccountObjPrivs>lambdaQuery()
                    .eq(DcAccountObjPrivs::getResourceId, resourceId)
                    .eq(DcAccountObjPrivs::getAccountId, accountId)
                    .eq(DcAccountObjPrivs::getGrantee, grantee)
                    .eq(DcAccountObjPrivs::getPrivilege, schemaAuth.getPrivilege())
                    .eq(DcAccountObjPrivs::getSchemaName, schemaAuth.getSchema_name())
                    .eq(DcAccountObjPrivs::getObjectName, "")
                    .eq(DcAccountObjPrivs::getObjectType, GrantObjectType.SCHEMA.getName());
            dcAccountObjPrivsMapper.removePrivilege(qw);
        }
    }

    private void saveOtherPrivilege(String otherAuth, Integer resourceId, Integer accountId) {
        DcAccountOtherPrivs priv = new DcAccountOtherPrivs();
        priv.setResourceId(resourceId);
        priv.setAccountId(accountId);
        priv.setIsExpire((byte) 0);
        priv.setGmtCreate(LocalDateTime.now());
        priv.setGmtModified(LocalDateTime.now());
        priv.setIsDelete((byte) 0);
        priv.setSql(otherAuth);
        LambdaQueryWrapper<DcAccountOtherPrivs> qw = new LambdaQueryWrapper<DcAccountOtherPrivs>()
                .eq(DcAccountOtherPrivs::getResourceId, resourceId)
                .eq(DcAccountOtherPrivs::getAccountId, accountId)
                .eq(DcAccountOtherPrivs::getIsDelete, priv.getIsDelete())
                .eq(DcAccountOtherPrivs::getIsExpire, priv.getIsExpire());
        DcAccountOtherPrivs dcAccountOtherPrivs = dcAccountOtherPrivsMapper.getPrivilege(qw);
        if (dcAccountOtherPrivs != null) {
            String origin = dcAccountOtherPrivs.getSql();
            dcAccountOtherPrivs.setSql(otherAuth + origin);
            dcAccountOtherPrivsMapper.updatePrivilege(dcAccountOtherPrivs);
        } else {
            dcAccountOtherPrivsMapper.addPrivilege(priv);
        }
    }

    private DcDbResource getResource(String resourceId) {
        LambdaQueryWrapper<DcDbResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcDbResource::getUnique_key, resourceId);
        return dcDbResourceMapper.getResource(queryWrapper);
    }

    private DcDbResourceAccount getResourceAccount(String accountId) {
        LambdaQueryWrapper<DcDbResourceAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcDbResourceAccount::getUniqueKey, accountId);
        return dcDbResourceAccountMapper.getResourceAccount(queryWrapper);
    }

}

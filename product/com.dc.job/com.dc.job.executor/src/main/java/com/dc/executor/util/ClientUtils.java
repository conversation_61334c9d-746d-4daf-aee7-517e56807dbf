package com.dc.executor.util;

import com.dc.executor.type.CodeType;
import com.dc.springboot.core.component.JSON;
import com.dc.executor.type.TaskInfoStateType;
import com.dc.utils.http.HttpClientUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.StringEntity;
import org.springframework.http.HttpHeaders;

import java.util.*;

@Slf4j
public class ClientUtils {

    private static final Gson gson = new GsonBuilder().serializeNulls().create();

    public static List<String> getSummerSplit(String script, Integer database_type, String summerInterfaceAddress) {
        List<String> sqlList = new ArrayList<>();

        try {
            if (StringUtils.isBlank(script)) {
                return sqlList;
            }

            Map<String, Object> params = new HashMap<>();
            params.put("database_type", database_type);
            params.put("script", script);

            String summerReturnMessage = HttpClientUtils.doPost(summerInterfaceAddress, new StringEntity(JSON.toJSONString(params), "UTF-8"));

            Map<String, Object> map = (Map<String, Object>) JSON.parseObject(summerReturnMessage, Map.class);
            if (map.get("body") instanceof Map) {
                Map<String, Object> body = (Map<String, Object>) map.get("body");
                if (body.get("queries") instanceof List) {
                    List<Map<String, Object>> queries = (List<Map<String, Object>>) body.get("queries");
                    for (Map<String, Object> query : queries) {
                        int start = Integer.parseInt(query.get("start").toString());
                        int end = Integer.parseInt(query.get("end").toString());
                        String sql = script.substring(start, end);
                        sqlList.add(sql);
                    }
                }
            }
        } catch (Exception e) {
            log.error("call summer split error!", e);
        }

        return sqlList;
    }

    public static Map<String, Integer> taskInfo(String token, int task_id, String summerInterfaceAddress, String cookieValue) {
        Map<String, Integer> taskInfo = new HashMap<>();
        taskInfo.put("stage", TaskInfoStateType.error.getValue());
        taskInfo.put("status", TaskInfoStateType.error.getValue());

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("token", token);
            params.put("task_id", task_id);

            Map<String, String> headers = new HashMap<>();
            headers.put(HttpHeaders.COOKIE, cookieValue);
            String summerReturnMessage = HttpClientUtils.doPost(summerInterfaceAddress, new StringEntity(JSON.toJSONString(params), "UTF-8"), headers);
            if (StringUtils.isBlank(summerReturnMessage)) {
                log.info("call interface:" + summerInterfaceAddress + ", but get nothing!");
            }

            Map<String, Object> map = (Map<String, Object>) JSON.parseObject(summerReturnMessage, Map.class);
            if (map.get("body") instanceof Map) {
                Map<String, Object> body = (Map<String, Object>) map.get("body");

                if (body.get("stage") != null) {
                    String stageString = body.get("stage").toString();
                    if (StringUtils.isNotBlank(stageString)) {
                        try {
                            taskInfo.put("stage", Integer.parseInt(stageString));
                            taskInfo.put("status", TaskInfoStateType.running.getValue());
                        } catch (Exception e) {
                            log.error("transform stage error!", e);
                            taskInfo.put("status", TaskInfoStateType.wait.getValue());
                        }
                    } else {
                        taskInfo.put("status", TaskInfoStateType.wait.getValue());
                    }
                } else {
                    taskInfo.put("status", TaskInfoStateType.wait.getValue());
                }

                if (body.get("status") != null) {
                    String statusString = body.get("status").toString();
                    if (!"RUNNING".equalsIgnoreCase(statusString)) {
                        taskInfo.put("status", TaskInfoStateType.finished.getValue());
                    }
                }
            }

            if (map.get("code") != null) {
                String codeString = map.get("code").toString();
                if (StringUtils.isNotBlank(codeString)) {
                    try {
                        int code = Integer.parseInt(codeString);
                        taskInfo.put("code", code);
                        if (code == CodeType.NOT_TOKEN.getValue()) {
                            taskInfo.put("status", TaskInfoStateType.closed.getValue());
                        }
                    } catch (Exception e) {
                        log.error("transform code error!", e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("call taskInfo error!", e);
        }

        return taskInfo;
    }

    public static Map<String, Object> taskResult(String token, int task_id, int stage, String summerInterfaceAddress, String cookieValue ) {
        Map<String, Object> taskResult = new HashMap<>();

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("token", token);
            params.put("task_id", task_id);
            params.put("stage", stage);

            String summerReturnMessage = HttpClientUtils.doPost(summerInterfaceAddress, new StringEntity(JSON.toJSONString(params), "UTF-8"), Collections.singletonMap(HttpHeaders.COOKIE, cookieValue));
            if (StringUtils.isBlank(summerReturnMessage)) {
                log.info("call interface:" + summerInterfaceAddress + ", but get nothing!");
            }

            Map<String, Object> map = (Map<String, Object>) JSON.parseObject(summerReturnMessage, Map.class);
            if (map.get("body") instanceof Map) {
                Map<String, Object> body = (Map<String, Object>) map.get("body");
                if (body.get("query_results") instanceof List) {
                    List<Map<String, Object>> queries = (List<Map<String, Object>>) body.get("query_results");
                    if (queries.size() > 0) {
                        Map<String, Object> webSQLQueryResult = queries.get(0);
                        taskResult.put("sql", webSQLQueryResult.get("sql"));
                        taskResult.put("message", webSQLQueryResult.get("message"));
                        taskResult.put("status", webSQLQueryResult.get("status"));
                        taskResult.put("backup_warning", webSQLQueryResult.get("backup_warning"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("call taskResult error!", e);
        }

        return taskResult;
    }

    public static void killConnection(String token, String summerInterfaceAddress, String cookieValue) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("token", token);

            Map<String, String> headers = new HashMap<>();
            headers.put(HttpHeaders.COOKIE, cookieValue);
            HttpClientUtils.doPost(summerInterfaceAddress, new StringEntity(JSON.toJSONString(params), "UTF-8"), headers);
        } catch (Exception e) {
            log.error("call killConnection error!", e);
        }
    }

    public static Map<String, Object> getUserToken(String user_id, String backendInterfaceAddress) {
        Map<String, Object> auth = new HashMap<>();
        auth.put("symbol", "******");
        auth.put("enable_desensite_type", false);
        auth.put("api_getway", "");
        auth.put("has_super_manager", false);
        auth.put("user_token", "");

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("user_id", user_id);

            String phpReturnMessage = HttpClientUtils.doPost(backendInterfaceAddress, new StringEntity(JSON.toJSONString(params), "UTF-8"));
            if (StringUtils.isBlank(phpReturnMessage)) {
                log.info("call interface:" + backendInterfaceAddress + ", but get nothing!");
            }

            Map<String, Object> map = (Map<String, Object>) JSON.parseObject(phpReturnMessage, Map.class);
            if (map != null && map.get("data") instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) map.get("data");
                auth.put("symbol", data.get("symbol") != null ? data.get("symbol") : "******");
                auth.put("enable_desensite_type", data.get("enable_desensite_type") != null ? data.get("enable_desensite_type") : false);
                auth.put("api_getway", data.get("api_getway") != null ? data.get("api_getway") : "");
                auth.put("has_super_manager", data.get("super") != null ? data.get("super") : false);
                auth.put("user_token", data.get("token") != null ? data.get("token") : "");
            }
        } catch (Exception e) {
            log.error("get user token error!", e);
        }

        return auth;
    }

}

package com.dc.executor.controller;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/kafka")
public class KafkaProducerController {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

//    @Autowired
//    private KafkaTemplate kafkaTemplate;

    @RequestMapping("send")
    public String send(String msg){
       try {
           Process process = Runtime.getRuntime().exec("php /usr/local/nginx/html/dc-backend/yii password/verify");
       } catch (Exception e) {
           log.error("send error : ", e);
       }
        return "success";
    }

}
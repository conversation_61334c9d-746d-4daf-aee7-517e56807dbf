package com.dc.executor.component;

import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.mysql.model.DcDbResource;
import com.dc.repository.mysql.model.DcScmpResult;
import com.dc.repository.mysql.model.Schema;
import com.dc.springboot.core.model.database.StructCompareInfo;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.springboot.core.model.parser.dto.SchemaDto;
import com.dc.springboot.core.model.workorder.SchemaInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface JobMapper {

    JobMapper INSTANCE = Mappers.getMapper(JobMapper.class);

    DatabaseConnectionDto toDatabaseConnectionDto(DatabaseConnection connection);

    DcScmpResult toDcScmpResult(StructCompareInfo structCompareInfo);

    @Mapping(target = "instance_name", source = "resource_name")
    DatabaseConnectionDto toDatabaseConnectionDto(DcDbResource dcDbResource);

    SchemaDto toSchemaDto(Schema schema);

    List<SchemaInfo> toSchemaInfo(List<com.dc.executor.model.SchemaInfo> schemaInfo);

}

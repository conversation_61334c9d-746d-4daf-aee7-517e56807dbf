package com.dc.executor.job.handler;


import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.VisitFrequencyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ClearVisitFrequencyJobHandler extends AbstractJobHandler {

    @Resource
    private VisitFrequencyMapper visitFrequencyMapper;

    @XxlJob("ClearVisitFrequencyJobHandler")
    public ReturnT<String> clearVisitFrequencyJobHandler(String param) throws Exception {
        try {
            log.info("开始清理30天以上的visit_frequency表数据...");
            long day = 1000 * 60 * 60 * 24 * 30L; // 30天
            long now = System.currentTimeMillis();
            visitFrequencyMapper.deleteExpireRecords(now, day);
            log.info("清理visit_frequency表数据完成。");
        } catch (Exception e) {
            log.error("call clearVisitFrequencyJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

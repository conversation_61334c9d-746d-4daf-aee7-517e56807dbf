package com.dc.executor.job.handler;

import com.dc.executor.model.StructCompareJobParam;
import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.enums.SqlExecuteStatusType;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.mapper.DcScmpParamMapper;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.model.DcScmpParam;
import com.dc.repository.mysql.model.DcScmpResult;
import com.dc.repository.mysql.model.DcScmpResultSql;
import com.dc.repository.mysql.model.Schema;
import com.dc.repository.mysql.service.DcScmpResultService;
import com.dc.repository.mysql.type.ResultCompareScopeType;
import com.dc.repository.mysql.type.ResultSqlType;
import com.dc.springboot.core.client.ProxyInternalClient;
import com.dc.springboot.core.client.SummerExecuteClient;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.database.*;
import com.dc.springboot.core.model.exception.ClientException;
import com.dc.springboot.core.model.exception.ConnectionException;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.utils.verification.VerificationException;
import com.dc.utils.verification.VerificationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class StructCompareJobHandler extends AbstractJobHandler {

    @Resource
    private ProxyInternalClient proxyInternalClient;

    @Resource
    private SummerExecuteClient summerExecuteClient;

    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;

    @Resource
    private SchemaMapper schemaMapper;

    @Resource
    private DcScmpParamMapper dcScmpParamMapper;

    @Resource
    private DcScmpResultService dcScmpResultService;


    @XxlJob("StructCompareJobHandler")
    public ReturnT<String> structCompareJobHandler(String param) {

        StructCompareJobParam structCompareJobParam;
        try {
            structCompareJobParam = json.getObjectMapper().readValue(param, StructCompareJobParam.class);
        } catch (JsonProcessingException e) {
            log.error("read json error", e);
            DCJobLogger.log(e.getMessage());
            return ReturnT.SUCCESS;
        }

        try {
            VerificationUtils.byAnnotation(structCompareJobParam);
        } catch (VerificationException e) {
            log.error("verify json error", e);
            DCJobLogger.log(e.getMessage());
            return ReturnT.SUCCESS;
        }

        Long logId = structCompareJobParam.getLogId();
        int jobId = jobLogMapper.load(logId).getJobId();

        SqlExecuteStatusType sqlExecuteStatusType = SqlExecuteStatusType.success;

        try {

            List<DcScmpParam> dcScmpParams = dcScmpParamMapper.getParamByDcJobId(structCompareJobParam.getDcJobId());
            if (CollectionUtils.isEmpty(dcScmpParams)) {
                sqlExecuteStatusType = SqlExecuteStatusType.fail;
                DCJobLogger.log("没有找到相关的任务详情。");
                return ReturnT.SUCCESS;
            }

            this.updateTriggerTime(logId);

            this.updateBeginTriggerStatus(jobId, logId);

            Map<String, DatabaseConnectionDto> connectCache = new HashMap<>();
            Map<String, Schema> schemaCache = new HashMap<>();

            // 构建缓存，避免多次查询
            for (String connectId : List.of(structCompareJobParam.getSourceConnectId(), structCompareJobParam.getTargetConnectId())) {
                DatabaseConnectionDto databaseConnectionDto = connectCache.computeIfAbsent(connectId,
                        key -> jobMapper.toDatabaseConnectionDto(databaseConnectionMapper.getActiveConnectionByUniqueKey(key)));
                if (databaseConnectionDto == null) {
                    sqlExecuteStatusType = SqlExecuteStatusType.fail;
                    DCJobLogger.log("没有找到可使用的实例信息。");
                    return ReturnT.SUCCESS;
                }
            }
            for (DcScmpParam dcScmpParam : dcScmpParams) {
                for (String schemaId : List.of(dcScmpParam.getSourceSchemaId(), dcScmpParam.getTargetSchemaId())) {
                    Schema schema = schemaCache.computeIfAbsent(schemaId,
                            key -> schemaMapper.getSchemaByUniqueKey(key).orElse(null));
                    if (schema == null) {
                        sqlExecuteStatusType = SqlExecuteStatusType.fail;
                        DCJobLogger.log("没有找到可使用的schema信息!");
                        return ReturnT.SUCCESS;
                    }
                }
            }

            Client summerClient = Client.getClient(jobConfig.getPath().getDcSummer());

            for (DatabaseConnectionDto databaseConnectionDto : connectCache.values()) {
                String concatName = String.format("%s【%s】", databaseConnectionDto.getConnection_desc(), databaseConnectionDto.getInstance_name());
                try {
                    summerExecuteClient.testConnection(summerClient, databaseConnectionDto.buildConnectionConfig(null, null));
                    DCJobLogger.log("连接实例" + concatName);
                } catch (ConnectionException connectionException) {
                    sqlExecuteStatusType = SqlExecuteStatusType.fail;
                    DCJobLogger.log("ERROR: " + concatName + connectionException.getMessage());
                    log.error("连接指定实例异常", connectionException);
                    return ReturnT.SUCCESS;

                } catch (ClientException clientException) {
                    sqlExecuteStatusType = SqlExecuteStatusType.fail;
                    DCJobLogger.log("测试连接服务端异常，请稍后重试。");
                    log.error("Summer 服务端异常", clientException);
                    return ReturnT.SUCCESS;
                }
            }

            Client iceageClient = Client.getClient(jobConfig.getPath().getDcIceage());

            for (DcScmpParam dcScmpParam : dcScmpParams) {

                StructCompareMessage message = new StructCompareMessage();

                message.setSourceConnectionConfig(getConnectionConfig(connectCache, schemaCache, structCompareJobParam.getSourceConnectId(), dcScmpParam.getSourceSchemaId()));

                message.setTargetConnectionConfig(getConnectionConfig(connectCache, schemaCache, structCompareJobParam.getTargetConnectId(), dcScmpParam.getTargetSchemaId()));

                message.setConfigTypes(structCompareJobParam.getConfigTypes());

                if (ResultCompareScopeType.of(dcScmpParam.getCompareScope()) == ResultCompareScopeType.OBJECT) {

                    StructCompareObjectMapping mapping = new StructCompareObjectMapping();
                    mapping.setObjectType(StructCompareObjectType.of(dcScmpParam.getObjectType()));
                    mapping.setSourceObjectName(dcScmpParam.getSourceObjectName());
                    mapping.setTargetObjectName(dcScmpParam.getTargetObjectName());

                    message.setObjectMappings(Collections.singletonList(mapping));
                }

                try {

                    String sourceName = message.getSourceConnectionConfig().makeConcatSchemaName();
                    Integer sourceDbType = message.getSourceConnectionConfig().getDatabaseType();
                    String targetName = message.getTargetConnectionConfig().makeConcatSchemaName();
                    Integer targetDbType = message.getTargetConnectionConfig().getDatabaseType();

                    for (StructCompareObjectMapping objectMapping : message.getObjectMappings()) {
                        sourceName = makeConcatName(sourceName, objectMapping.getSourceObjectName());
                        targetName = makeConcatName(targetName, objectMapping.getTargetObjectName());
                    }

                    DCJobLogger.log("比较" + sourceName + "与" + targetName);

                    List<StructCompareInfo> structCompareInfos = proxyInternalClient.structCompare(iceageClient, message);

                    for (StructCompareInfo structCompareInfo : structCompareInfos) {

                        DcScmpResult dcScmpResult = jobMapper.toDcScmpResult(structCompareInfo);
                        dcScmpResult.setJobId(jobId);
                        dcScmpResult.setLogId(logId);
                        dcScmpResult.setSourceSchemaId(dcScmpParam.getSourceSchemaId());
                        dcScmpResult.setTargetSchemaId(dcScmpParam.getTargetSchemaId());
                        dcScmpResult.setSourceDbType(sourceDbType);
                        dcScmpResult.setTargetDbType(targetDbType);

                        List<DcScmpResultSql> dcScmpResultSqls = new ArrayList<>();

                        structCompareInfo.getSourceQuery().forEach(sqlText -> {
                            DcScmpResultSql dcScmpResultSql = new DcScmpResultSql();
                            dcScmpResultSql.setSqlText(sqlText);
                            dcScmpResultSql.setSqlType(ResultSqlType.SOURCE.getCode());
                            dcScmpResultSqls.add(dcScmpResultSql);
                        });

                        structCompareInfo.getTargetQuery().forEach(sqlText -> {
                            DcScmpResultSql dcScmpResultSql = new DcScmpResultSql();
                            dcScmpResultSql.setSqlText(sqlText);
                            dcScmpResultSql.setSqlType(ResultSqlType.TARGET.getCode());
                            dcScmpResultSqls.add(dcScmpResultSql);
                        });

                        structCompareInfo.getChangeQuery().forEach(sqlText -> {
                            DcScmpResultSql dcScmpResultSql = new DcScmpResultSql();
                            dcScmpResultSql.setSqlText(sqlText);
                            dcScmpResultSql.setSqlType(ResultSqlType.CHANGE.getCode());
                            dcScmpResultSqls.add(dcScmpResultSql);
                        });

                        dcScmpResultService.insert(dcScmpResult, dcScmpResultSqls);
                    }

                    DCJobLogger.log("成功");
                } catch (Exception e) {
                    sqlExecuteStatusType = SqlExecuteStatusType.fail;
                    DCJobLogger.log("ERROR: " + e.getMessage());
                    log.error("对比具体对象异常", e);
                    return ReturnT.SUCCESS;
                }
            }

        } finally {
            this.updateFinalStatus(sqlExecuteStatusType, logId, false, jobId); // 更新最终状态
            DCJobLogger.log("结构对比完成");
        }

        return ReturnT.SUCCESS;

    }

    private static ConnectionConfig getConnectionConfig(Map<String, DatabaseConnectionDto> connectCache,  Map<String, Schema> schemaCache, String connectId, String schemaId) {
        Schema schema = schemaCache.get(schemaId);
        DatabaseConnectionDto databaseConnectionDto = connectCache.get(connectId);
        return databaseConnectionDto.buildConnectionConfig(schema.getSchema_name(), schema.getCatalog_name());
    }


    public static String makeConcatName(String... names) {
        return String.join(".", names);
    }

}

package com.dc.executor.config;

import com.dc.springboot.core.config.PathConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "job", ignoreInvalidFields = true)
public class JobConfig {

    private String uploadLocation;
    private String oracle;

    private String toolPath;

    private Path path;

    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @Data
    public static class Path extends PathConfig {
        private String dcIceage;
        private String dcSummer;
        private String dcSpi;
    }

    public static void setPathInstance(Path path) {
        PathConfig.setInstance(path);
    }

    public static Path getPathInstance() {
        return (Path) PathConfig.getInstance();
    }

    public static class Upload {
        private String location;
    }
}

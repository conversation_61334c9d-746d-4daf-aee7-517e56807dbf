package com.dc.executor.job.handler;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.SqlCollectMapper;
import com.dc.repository.mysql.model.SqlCollect;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;

@Slf4j
@Component
public class CollectUpdateJobHandler extends AbstractJobHandler {

    @Resource
    private SqlCollectMapper SqlCollectMapper;

    @XxlJob("CollectUpdateJobHandler")
    public ReturnT<String> collectUpdateJobHandler(String param) throws Exception {
        try {
            // 获取当前时间
            LocalDateTime currentDateTime = LocalDateTime.now();

            // 减去 7 天
            LocalDateTime sevenDaysAgo = currentDateTime.minusDays(7);

            //查询 set is_delete = 1 where origin = 3 and is_delete = 0 and gmt_create < sevenDaysAgo
            SqlCollect sqlCollect = new SqlCollect();
            sqlCollect.setGmtModified(new Date());
            sqlCollect.setIsDelete(1);

            final int LIMIT_SIZE = 200;
            int affectedRows = 0;

            do {
                Wrapper<SqlCollect> updateWrapper = new QueryWrapper<SqlCollect>().lambda()
                        .eq(SqlCollect::getOrigin, 2)
                        .eq(SqlCollect::getIsDelete, 0)
                        .lt(SqlCollect::getGmtCreate, sevenDaysAgo)
                        .orderByAsc(SqlCollect::getId)
                        .last("limit " + LIMIT_SIZE);

                //更新影响条数
                affectedRows = SqlCollectMapper.update(sqlCollect, updateWrapper);
            } while (affectedRows >= LIMIT_SIZE);   //-- 避免行锁太多


        } catch (Exception e) {
            log.error("call collectUpdateJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

package com.dc.executor.model;


import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.springboot.core.model.data.DataSet;
import lombok.Data;

import java.util.List;


@Data
public class RecycleModel {

    /**
     * 版本id
     */
    private Long batchId;
    /**
     * 数据库类型
     */
    private Integer dbType;
    /**
     * 实例id
     */
    private String connectId;
    /**
     * schema id
     */
    private String schemaId;
    /**
     * redis key值
     */
    private String requestId;
    /**
     * 执行用户id
     */
    private String userId;

    private Integer origin;

    private String realName;

    private String organizationName;

    private String ip;

    private String hostname;
    /**
     * 遇到错误时是否继续
     */
    private Boolean errorContinue = false;

    private DatabaseConnection instance;

    private String charset;

    //切换用户后id
    private String accountId;

    private String checkModelList;

    private List<DataSet<List<CheckModel>>> recoveryDataSqlList;

    private Long logId;

    private Integer jobId;

    private String redisKey;

}

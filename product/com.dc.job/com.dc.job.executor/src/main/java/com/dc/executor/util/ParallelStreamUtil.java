package com.dc.executor.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@Slf4j
public class ParallelStreamUtil {

    /**
     * 使用指定的线程数并发执行任务，并支持超时限制
     *
     * @param collection  要处理的集合
     * @param parallelism 并发线程数
     * @param action      对每个元素执行的任务
     * @param timeout     超时时间
     * @param timeUnit    超时时间单位
     * @param <T>         集合中元素的类型
     */
    public static <T> void parallelForEach(Collection<T> collection, int parallelism, Consumer<T> action, long timeout, TimeUnit timeUnit) {
        ForkJoinPool customThreadPool = new ForkJoinPool(parallelism);

        try {
            // 提交任务到自定义线程池
            customThreadPool.submit(() -> collection.parallelStream()
                    .forEach(item -> {
                        // 包装任务逻辑，检查中断状态
                        if (Thread.currentThread().isInterrupted()) {
                            System.out.println(Thread.currentThread().getName() + " was interrupted, stopping task.");
                            return;
                        }
                        try {
                            action.accept(item);
                        } catch (Exception e) {
                            // 捕获任务异常，处理或记录
                            log.error("Task failed for item: {}, error: {}", item, e.getMessage());
                        }
                    })
            ).get(timeout, timeUnit); // 阻塞等待任务完成或超时
        } catch (Exception e) {
            throw new RuntimeException("Error occurred during parallel execution", e);
        } finally {
            // 强制关闭线程池
            customThreadPool.shutdownNow();
            log.debug("Forced shutdown of ForkJoinPool after timeout.");
        }
    }
}

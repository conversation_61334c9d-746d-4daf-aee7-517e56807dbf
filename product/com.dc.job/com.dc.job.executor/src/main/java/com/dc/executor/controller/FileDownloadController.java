package com.dc.executor.controller;

import com.dc.executor.config.JobConfig;
import com.dc.job.log.XxlJobFileAppender;
import com.dc.utils.http.FileDownloadAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.util.Date;


@Slf4j
@SuppressWarnings("all")
@RestController(value = "JOBFDC")
@RequestMapping("/fileDownload")
public class FileDownloadController {

    @Resource
    private JobConfig jobConfig;

    @RequestMapping(value = "/downloadDump", method = RequestMethod.GET)
    public void downloadDump(@RequestParam("filename") String filename, @RequestParam("token") String token, @RequestParam("ts") int ts,
                             HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {

            // 校验token
            boolean hasAuth = FileDownloadAuthUtil.fileDownloadAuth(ts, filename, token);
            if (!hasAuth) {
                response.setStatus(500);
                return;
            }

            request.setCharacterEncoding("UTF-8");

            String path = jobConfig.getUploadLocation() + File.separator + "downloads" + File.separator;

            //创建file对象
            File file = new File(path + filename);
            filename = file.getName();
            //设置response的编码方式
            response.setContentType("application/x-msdownload");

            //写明要下载的文件的大小
            response.setContentLength((int) file.length());

            //解决中文乱码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(filename.getBytes("UTF-8"), "iso-8859-1"));

            byte[] b = new byte[1024];//相当于我们的缓存

            long k = 0;//该值用于计算当前实际下载了多少字节

            //读出文件到i/o流
            try (FileInputStream fis = new FileInputStream(file);
                 BufferedInputStream buff = new BufferedInputStream(fis);
                 OutputStream myout = response.getOutputStream();) {

                //开始循环下载
                while (k < file.length()) {

                    int j = buff.read(b, 0, 1024);
                    k += j;

                    //将b中的数据写到客户端的内存
                    myout.write(b, 0, j);

                }
                //将写入到客户端的内存的数据,刷新到磁盘
                myout.flush();
            }

        } catch (Exception e) {
            log.error("downloadDump Error.", e);
        }

    }

    @RequestMapping(value = "/downloadLog", method = RequestMethod.GET)
    public void downloadLog(@RequestParam("triggerTime") long triggerTime, @RequestParam("logId") long logId,
                            @RequestParam("filename") String fileName, @RequestParam("token") String token, @RequestParam("ts") int ts,
                            HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {

            // 校验token
            boolean hasAuth = FileDownloadAuthUtil.fileDownloadAuth(ts, fileName, token);
            if (!hasAuth) {
                response.setStatus(500);
                return;
            }

            request.setCharacterEncoding("UTF-8");
            //得到下载文件的名字
            String logFileName = XxlJobFileAppender.makeLogFileName(new Date(triggerTime), logId);

            //创建file对象
            File file = new File(logFileName);
            String filename = file.getName();
            //设置response的编码方式
            response.setContentType("application/x-msdownload");
            //写明要下载的文件的大小
            response.setContentLength((int) file.length());
            //解决中文乱码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(filename.getBytes("UTF-8"), "iso-8859-1"));

            byte[] b = new byte[1024];//相当于我们的缓存
            long k = 0;//该值用于计算当前实际下载了多少字节

            //读出文件到i/o流
            try(FileInputStream fis = new FileInputStream(file);
                BufferedInputStream buff = new BufferedInputStream(fis);
                OutputStream myout = response.getOutputStream();) {

                //开始循环下载
                while (k < file.length()) {
                    int j = buff.read(b, 0, 1024);
                    k += j;
                    //将b中的数据写到客户端的内存
                    myout.write(b, 0, j);
                }
                //将写入到客户端的内存的数据,刷新到磁盘
                myout.flush();
            }

        } catch (Exception e) {
            log.error("downloadLog Error.", e);
        }

    }

}

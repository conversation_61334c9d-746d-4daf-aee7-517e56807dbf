package com.dc.executor.controller;

import com.dc.executor.component.JobMapper;
import com.dc.executor.config.JobConfig;
import com.dc.executor.type.ToolType;
import com.dc.executor.model.NoLockChangesModel;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.utils.CipherUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.*;
import java.util.Map;

@Slf4j
@RestController(value = "test")
@RequestMapping("/test")
public class TestController {

    @Resource
    private JobConfig jobConfig;

    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;

    @Resource
    private JobMapper jobMapper;

    @RequestMapping(value = "/no-lock", method = RequestMethod.POST)
    public void testNoLockChanges(@RequestBody NoLockChangesModel noLockChangesModel) {

        String instanceId = noLockChangesModel.getConnectId();
        DatabaseConnectionDto instance = jobMapper.toDatabaseConnectionDto(databaseConnectionMapper.getConnectionByUniqueKey(instanceId));
        String userName = instance.getUsername();
        String password = CipherUtils.decrypt(instance.getPassword());
        String ip = instance.getIp();
        String port = instance.getPort();
        StringBuilder cmd = new StringBuilder();
        StringBuilder cmd_star = new StringBuilder();
        String prefix = "";

        Map<String, String> toolPathMap = JSON.parseObject(jobConfig.getToolPath().replaceAll("'", "\""), new TypeReference<Map<String, String>>() {
        });

        prefix = toolPathMap.get(ToolType.PT_OLINE.getValue());
        cmd.append(prefix).append(" ");
        cmd.append(String.format(
                "--user=%s --password=%s --host=%s P=%s,D=%s,t=%s %s --execute --charset=utf8 --no-version-check",
                userName,
                password,
                ip,
                port,
                noLockChangesModel.getSchema(),
                noLockChangesModel.getTableName(),
                noLockChangesModel.getChangesCommand()));
        cmd_star.append(String.format(
                "--user=%s --password=%s --host=%s P=%s,D=%s,t=%s %s --execute --charset=utf8 --no-version-check",
                userName,
                "******",
                ip,
                port,
                noLockChangesModel.getSchema(),
                noLockChangesModel.getTableName(),
                noLockChangesModel.getChangesCommand()));



        log.info("\n" + prefix + " " + cmd_star);

        log.info("cmd = " + cmd);

        try {
            ProcessBuilder processBuilder = new ProcessBuilder(cmd.toString());

            //input和error流合并
            processBuilder.redirectErrorStream(true);

            Process process;
//            process = Runtime.getRuntime().exec(cmd.toString());
//            process = processBuilder.start();
            String[] command = {"/bin/bash", "-c", cmd.toString()};
            process = Runtime.getRuntime().exec(command);

            InputStream inputStream = process.getInputStream();
            InputStream errorStream = process.getErrorStream();
            //input和error流合并
            SequenceInputStream mergeStream = new SequenceInputStream(inputStream, errorStream);
            StringBuilder sb_mergeStream = new StringBuilder();

            String charset = "UTF8";
            String os_name = System.getProperty("os.name");
            if (os_name.contains("Linux")) {
                charset = "UTF8";
            } else if (os_name.contains("Windows")) {
                charset = "GBK";
            }

            BufferedReader br = new BufferedReader(new InputStreamReader(mergeStream, charset));
            try {
                String line = null;
                while ((line = br.readLine()) != null) {
                    // 此方法有延迟,放弃使用
    //                        if(Thread.currentThread().isInterrupted()){
    //                            return ReturnT.SUCCESS;
    //                        }

                    log.info(line);
                    sb_mergeStream.append(line).append("\n");
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    inputStream.close();
                    errorStream.close();
                    mergeStream.close();
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            log.error("无锁变更报错", e);
        }
    }
}

package com.dc.executor.job.handler;

import com.dc.utils.http.HttpClientUtils;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class IllegalOperationAlertJobHandler extends AbstractJobHandler {

    @XxlJob("IllegalOperationAlertJobHandler")
    public ReturnT<String> illegalOperationAlertJobHandler(String param) throws Exception {
        try {
            log.info("Start User Illegal Operation Alert");
            HttpClientUtils.doPost(jobConfig.getPath().getDcBackend() + "/" + param, new StringEntity(""));
        } catch (Exception e) {
            log.error("call illegalOperationAlertJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }
}

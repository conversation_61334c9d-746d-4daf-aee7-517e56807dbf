package com.dc.executor.job.handler;

import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.model.data.Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ClearAsyncExecutionResultsJobHandler extends AbstractJobHandler {

    @Resource
    private BackendClient backendClient;

    @XxlJob("ClearAsyncExecutionResultsJobHandler")
    public ReturnT<String> clearAsyncExecutionResultsJobHandler(String param) throws Exception {
        try {
            DCJobLogger.log("Start Clear asynchronous execution results");
            backendClient.clearAsyncExecRes(Client.getClient(jobConfig.getPath().getDcBackend()));
        } catch (Exception e) {
            DCJobLogger.log("call clearAsyncExecutionResultsJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

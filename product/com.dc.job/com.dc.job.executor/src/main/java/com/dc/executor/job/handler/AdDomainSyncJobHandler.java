package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class AdDomainSyncJobHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    @XxlJob("AdDomainSyncJobHandler")
    public ReturnT<String> adDomainSyncJobHandler(String param) {
        try {
            log.info("Start AD Domain Synchronize!");
            HttpClientUtils.doGet(jobConfig.getPath().getDcBackend() + "/" + param, null);
        } catch (Exception e) {
            log.error("call adDomainSyncJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

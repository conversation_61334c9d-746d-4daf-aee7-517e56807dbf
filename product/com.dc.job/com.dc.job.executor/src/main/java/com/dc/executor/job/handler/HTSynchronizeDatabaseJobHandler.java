package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class HTSynchronizeDatabaseJobHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    @XxlJob("HTSynchronizeDatabaseJobHandler")
    public ReturnT<String> hTSynchronizeDatabaseJobHandler(String param) throws Exception {
        try {
            log.info("Start HuaTai Database Synchronize!");
            // 华泰需求:同步数据库
            HttpClientUtils.doGet(jobConfig.getPath().getDcBackend() + "/" + param, null);
        } catch (Exception e) {
            log.error("call hTSynchronizeDatabaseJ<PERSON>Hand<PERSON> error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

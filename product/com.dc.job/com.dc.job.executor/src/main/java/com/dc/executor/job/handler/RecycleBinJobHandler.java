package com.dc.executor.job.handler;

import com.dc.executor.model.CheckModel;
import com.dc.executor.model.RecycleModel;
import com.dc.executor.service.SqlRecycleService;
import com.dc.repository.redis.service.RedisService;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.job.util.IpUtil;
import com.dc.springboot.core.model.data.DataSet;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class RecycleBinJobHandler extends AbstractJobHandler {

    @Resource
    private SqlRecycleService sqlRecycleService;

    @Resource
    private RedisService redisService;

    @XxlJob("RecycleBinJobHandler")
    public ReturnT<String> recycleBinJobHandler(String param) throws Exception {
        try {
            RecycleModel recycleModel = json.getObjectMapper().readValue(param, RecycleModel.class);
            try {
                String checkModelList = redisService.get(recycleModel.getRedisKey());
                redisService.del(recycleModel.getRedisKey());

                List<DataSet<List<CheckModel>>> recoveryDataSqlList = json.getObjectMapper().readValue(checkModelList, new TypeReference<List<DataSet<List<CheckModel>>>>() {
                });
                recycleModel.setRecoveryDataSqlList(recoveryDataSqlList);
            } catch (Exception e) {
                log.error("get recoveryDataSqlList error!", e);
            }

            if (recycleModel.getJobId() != null) {
                dcJobMapper.beginUpdate(recycleModel.getJobId(), 1);
            }
            if (recycleModel.getLogId() != 0L) {
                int jobTriggerCount = jobLogMapper.findJobTriggerCount(recycleModel.getJobId());
                long triggerCount = jobTriggerCount + 1L;
                // 1 表示执行中
                jobLogMapper.updateBeginTriggerStatus(recycleModel.getLogId(), 1, triggerCount);
                // 更新执行器服务的ip和port
                jobLogMapper.updateExecutorTomcatAddress(recycleModel.getLogId(), IpUtil.getIpPort(ip, port));

            }
            sqlRecycleService.recycleBin(recycleModel);

        } catch (Exception e) {
            log.error("call recycleBinJobHandler error!", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}

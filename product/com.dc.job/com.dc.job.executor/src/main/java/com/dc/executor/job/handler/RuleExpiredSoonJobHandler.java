package com.dc.executor.job.handler;

import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.GroupUserMapper;
import com.dc.repository.mysql.mapper.SystemParamConfigMapper;
import com.dc.repository.mysql.model.SystemParamConfig;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.message.AuthSoonExpiredMessage;
import com.dc.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
public class RuleExpiredSoonJobHandler extends AbstractJobHandler {

    private static final String PERMISSION_EXPIRED_KEY = "permission_expired";

    @Resource
    private BackendClient backendClient;

    @Resource
    private SystemParamConfigMapper systemParamConfigMapper;

    @Resource
    private GroupUserMapper groupUserMapper;

    @XxlJob("RuleExpiredSoonJobHandler")
    public ReturnT<String> ruleExpiredSoonJobHandler(String param) {
        try {
            log.info("Start Rule Expired Soon!");
            SystemParamConfig systemParamConfig = systemParamConfigMapper.getSystemParamConfigByKey(PERMISSION_EXPIRED_KEY);
            if (systemParamConfig != null && !StringUtils.isNullOrEmpty(systemParamConfig.getP_value())) {
                int hour = Integer.parseInt(systemParamConfig.getP_value());
                long nowSecond = Instant.now().getEpochSecond();
                int more = hour * 3600;
                List<Map<String, String>> userSoonExpireAuth = groupUserMapper.getUserSoonExpireAuth(nowSecond, nowSecond + more);
                System.out.println(userSoonExpireAuth);
                if (CollectionUtils.isNotEmpty(userSoonExpireAuth)) {
                    userSoonExpireAuth.forEach(row -> {
                        AuthSoonExpiredMessage authSoonExpiredMessage = new AuthSoonExpiredMessage(row.get("user_id"), Set.of(row.get("instance_names").split(",")));
                        backendClient.notifySoonExpiredAuth(Client.getClient(jobConfig.getPath().getDcBackend()), authSoonExpiredMessage);
                    });
                }

            } else {
                DCJobLogger.log("未配置权限过期阈值");
                return ReturnT.SUCCESS;
            }

        } catch (Exception e) {
            log.error("Call RuleExpiredSoonJobHandler Error!", e);
            DCJobLogger.log(e);
            return ReturnT.FAIL;
        }
        DCJobLogger.log("RuleExpiredSoonJobHandler Finished!");
        return ReturnT.SUCCESS;
    }
}

package com.dc.executor.job.handler;

import com.dc.executor.model.NoLockChangesModel;
import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.job.util.IpUtil;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.model.JobLog;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.utils.CipherUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

@Slf4j
@Component
public class NoLockChangesJobHandler extends AbstractJobHandler {

    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;

    @XxlJob("noLockChangesJobHandler")
    public ReturnT<String> noLockChangesJobHandler(String param) throws Exception {

        log.info("无锁变更参数：" + param);
        byte[] bytes = param.getBytes(StandardCharsets.UTF_8);
        param = new String(bytes);
        NoLockChangesModel noLockChangesModel = gson.fromJson(param, NoLockChangesModel.class);
        // 获取调度记录
        JobLog load = jobLogMapper.load(noLockChangesModel.getLogId());
        if (noLockChangesModel.getJobId() == null) {
            noLockChangesModel.setJobId(load.getJobId());
        }

        try {
            if (noLockChangesModel.getJobId() != null) {
                dcJobMapper.beginUpdate(noLockChangesModel.getJobId(), 1);
            }

            if (noLockChangesModel.getLogId() != 0L) {
                int jobTriggerCount = jobLogMapper.findJobTriggerCount(noLockChangesModel.getJobId());
                long triggerCount = jobTriggerCount + 1L;
                // 1 表示执行中
                jobLogMapper.updateBeginTriggerStatus(noLockChangesModel.getLogId(), 1, triggerCount);
                // 更新执行器服务的ip和port
                jobLogMapper.updateExecutorTomcatAddress(noLockChangesModel.getLogId(), IpUtil.getIpPort(ip, port));
            }

            if (noLockChangesModel.getConnectId() != null && noLockChangesModel.getDbType() != null) {
                String instanceId = noLockChangesModel.getConnectId();
                DatabaseConnectionDto instance = jobMapper.toDatabaseConnectionDto(this.databaseConnectionMapper.getConnectionByUniqueKey(instanceId));
                String userName = instance.getUsername();
                String password = CipherUtils.sm4decrypt(instance.getPassword());
                String ip = instance.getIp();
                String port = instance.getPort();
                StringBuilder cmd = new StringBuilder();
                StringBuilder cmd_star = new StringBuilder();
                String prefix = "";

                Map<String, String> toolPathMap = JSON.parseObject(jobConfig.getToolPath().replaceAll("'", "\""), new TypeReference<Map<String, String>>() {
                });

                prefix = toolPathMap.get(noLockChangesModel.getToolType());
                cmd.append(prefix).append(" ");
                cmd.append(String.format(
                        "--user=%s --password=%s --host=%s P=%s,D=%s,t=%s %s",
                        userName,
                        password,
                        ip,
                        port,
                        noLockChangesModel.getSchema(),
                        noLockChangesModel.getTableName(),
                        noLockChangesModel.getChangesCommand()));
                cmd_star.append(String.format(
                        "--user=%s --password=%s --host=%s P=%s,D=%s,t=%s %s",
                        userName,
                        "******",
                        ip,
                        port,
                        noLockChangesModel.getSchema(),
                        noLockChangesModel.getTableName(),
                        noLockChangesModel.getChangesCommand()));



                DCJobLogger.log("\n" + prefix + " " + cmd_star);

                log.info("无锁变更命令：" + cmd);
                ProcessBuilder processBuilder = new ProcessBuilder(cmd.toString());

                //input和error流合并
                processBuilder.redirectErrorStream(true);

                Process process;
                String[] command = {"/bin/bash", "-c", cmd.toString()};
                process = Runtime.getRuntime().exec(command);

                InputStream inputStream = process.getInputStream();
                InputStream errorStream = process.getErrorStream();
                //input和error流合并
                SequenceInputStream mergeStream = new SequenceInputStream(inputStream, errorStream);

                StringBuilder sb_mergeStream = new StringBuilder();
                String charset = "UTF8";
                String os_name = System.getProperty("os.name");
                if (os_name.contains("Linux")) {
                    charset = "UTF8";
                } else if (os_name.contains("Windows")) {
                    charset = "GBK";
                }

                BufferedReader br = new BufferedReader(new InputStreamReader(mergeStream, charset));
                try {
                    String line = null;
                    while ((line = br.readLine()) != null) {
                        // 此方法有延迟,放弃使用
//                        if(Thread.currentThread().isInterrupted()){
//                            return ReturnT.SUCCESS;
//                        }
                        try {
                            Thread.sleep(10); // 最低10才有效
                        } catch (InterruptedException e) {
                            DCJobLogger.schedulingTermination();
                            return ReturnT.SUCCESS;
                        }
                        DCJobLogger.log(line);
                        sb_mergeStream.append(line).append("\n");
                        log.info(line);
                    }
                } catch (IOException e) {
                    log.error("read log error!", e);
                } finally {
                    try {
                        inputStream.close();
                        errorStream.close();
                        mergeStream.close();
                        br.close();
                    } catch (IOException e) {
                        log.error("close stream error!", e);
                    }
                }


                try {
                    process.waitFor();
                } catch (InterruptedException e) {
                    DCJobLogger.schedulingTermination();
                    return ReturnT.SUCCESS;
                }

                int status = 1;
                if (sb_mergeStream.toString().contains("Successfully")) {
                    status = 2; // 成功
                } else {
                    status = 3; // 失败
                }
                if (noLockChangesModel.getJobId() != null) {
                    dcJobMapper.update(noLockChangesModel.getJobId(), status);
                }
                if (noLockChangesModel.getLogId() != 0L) {
                    jobLogMapper.updateTriggerStatus(noLockChangesModel.getLogId(), status, new Date());
                    if (status == 2) {
                        DCJobLogger.schedulingSuccessful();
                    } else if (status == 3) {
                        DCJobLogger.schedulingFailed();
                    }
                }
                process.destroy();

            } else {
                log.info("实例id、数据库类型不能为空！");
                DCJobLogger.log("实例id、数据库类型不能为空！");
            }

        } catch (Exception e) {
            log.error("无锁变更出错", e);
            DCJobLogger.log("无锁变更出错: " + e);
            if (noLockChangesModel.getJobId() != null) {
                try {
                    dcJobMapper.update(noLockChangesModel.getJobId(), 3);
                } catch (Exception e1) {
                    DCJobLogger.log("连接数据库出错: " + e1);
                    log.error("noLockChangesJobHandler error : ", e1);
                }
            }
            if (noLockChangesModel.getLogId() != 0L) {
                try {
                    jobLogMapper.updateTriggerStatus(noLockChangesModel.getLogId(), 3, new Date());
                } catch (Exception e1) {
                    DCJobLogger.log("连接数据库出错: " + e1);
                    log.error("noLockChangesJobHandler error : ", e1);
                }
            }
            return ReturnT.SUCCESS;
        } finally {
            jobInfoDao.updateStatusByJobId(0, load.getJobId());
        }
        return ReturnT.SUCCESS;
    }



}

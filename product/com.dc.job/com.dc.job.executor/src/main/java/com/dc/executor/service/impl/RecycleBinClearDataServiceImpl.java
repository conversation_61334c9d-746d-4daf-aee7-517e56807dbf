package com.dc.executor.service.impl;

import com.dc.executor.service.RecycleBinClearDataService;
import com.dc.repository.mysql.mapper.DataTableMapper;
import com.dc.repository.mysql.mapper.RcBatchMapper;
import com.dc.repository.mysql.mapper.RcSqlMapper;
import com.dc.repository.mysql.mapper.RelationTableMapper;
import com.dc.repository.mysql.model.RelationTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RecycleBinClearDataServiceImpl implements RecycleBinClearDataService {

    @Resource
    private RcSqlMapper rcSqlDao;
    @Resource
    private RcBatchMapper rcBatchDao;
    @Resource
    private RelationTableMapper relationTableDao;
    @Resource
    private DataTableMapper dataTableDao;

    @Override
    public void clearData(String rcRelName, String rcDataName, Integer transactionIndex, Long rcSqlId) {
        List<String> rowIds = new ArrayList<>();

        try {
            Map<String, Object> relationTableMap = new LinkedHashMap<>();
            relationTableMap.put("table_name", rcRelName);
            relationTableMap.put("rc_sql_ids", Collections.singleton(rcSqlId));
            relationTableMap.put("transaction_index", transactionIndex);
            // 获取rel表数据
            List<RelationTable> relationTables = relationTableDao.getRelationTables(relationTableMap);
            // 获取所有涉及的主键值
             rowIds = relationTables.stream().map(RelationTable::getRow_id).collect(Collectors.toList());
            // 删除rel表数据
            relationTableDao.deleteRelationTables(relationTableMap);
            // 查询删除后的rel表数据
            int relationTableCount = relationTableDao.getRelationTableCount(relationTableMap);
            // 删除rel表
            if (relationTableCount == 0) {
                relationTableDao.dropRelationTable(relationTableMap);
            }
        } catch (Exception e) {
            log.error("处理rel表出错！", e);
        }

        try {
            Map<String, Object> dataTableMap = new LinkedHashMap<>();
            dataTableMap.put("table_name", rcDataName);
            dataTableMap.put("row_ids", rowIds);
            dataTableMap.put("transaction_index", transactionIndex);
            // 删除data表数据
            if (!rowIds.isEmpty()) {
                dataTableDao.deleteDataByRowIds(dataTableMap);
            }
            // 查询删除后的data表数据
            int dataTableCount = dataTableDao.getDataTableCount(dataTableMap);
            // 删除data表
            if (dataTableCount == 0) {
                dataTableDao.dropDataTable(dataTableMap);
            }
        } catch (Exception e) {
            log.error("处理data表出错！", e);
        }

        // 删除rcSql表数据
        rcSqlDao.deleteExpiredRcSql(rcSqlId);
    }

    @Override
    public void deleteRcBatch(Set<Long> rcBatchSet) {
        if (rcBatchSet == null || rcBatchSet.isEmpty()) {
            return;
        }
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("ids", rcBatchSet);
        // 删除rcBatch表数据
        rcBatchDao.deleteExpiredRcBatchByIdList(map);
    }
}

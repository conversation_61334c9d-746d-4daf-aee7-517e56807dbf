package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.job.log.XxlJobLogger;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

@Component(value = "JOBSPUD")
public class SyncPaUserDailyJobHandler {

    private static Logger logger = LoggerFactory.getLogger(SyncPaUserDailyJobHandler.class);

    @Resource
    private JobConfig jobConfig;

    private static final String summerSyncPaUserDaily = "/dc-summer/pa/user/sync-user-daily";

    @XxlJob("SyncPaUserDailyJobHandler")
    public ReturnT<String> syncPaUserDailyJobHandler(String param) throws Exception {
        ReturnT<String> result = ReturnT.SUCCESS;
        try {
            String jobUrl = jobConfig.getPath().getDcSummer() + summerSyncPaUserDaily;
            if (StringUtils.isNotEmpty(param)) {
                Map paramMap = new Gson().fromJson(param, Map.class);;
                String startTime = (String) paramMap.get("startTime");
                String endTime = (String) paramMap.get("endTime");
                if (startTime != null && endTime != null) {
                    jobUrl = jobUrl + "?startTime=" + startTime + "&endTime=" + endTime;
                }
            }
            URL url = new URL(jobUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "text/event-stream");
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (StringUtils.isNotBlank(line) && line.contains("error")) {
                        result = ReturnT.FAIL;
                    }
                    XxlJobLogger.log("\n----------- summer log -----------" + line);
                }
            } finally {
                connection.disconnect();
            }
        } catch (Exception e) {
            logger.error("call syncPaUserJobHandler error!", e);
        }
        return result;
    }
}

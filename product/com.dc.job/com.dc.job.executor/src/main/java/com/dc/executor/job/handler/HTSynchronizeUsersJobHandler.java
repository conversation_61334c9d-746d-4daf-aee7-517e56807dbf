package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.utils.http.HttpClientUtils;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class HTSynchronizeUsersJobHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    @XxlJob("HTSynchronizeUsersJobHandler")
    public ReturnT<String> hTSynchronizeUsersJobHandler(String param) throws Exception {
        try {
            log.info("Start HuaTai Users Synchronize!");
            // 华泰需求:同步用户
            HttpClientUtils.doGet(jobConfig.getPath().getDcBackend() + "/" + param, null);
        } catch (Exception e) {
            log.error("call hTSynchronizeUsersJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

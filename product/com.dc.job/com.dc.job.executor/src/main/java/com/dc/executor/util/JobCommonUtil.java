package com.dc.executor.util;

import com.dc.executor.type.RepeatType;
import com.dc.executor.type.TaskInfoStateType;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.execution.JobExportMessage;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.StringEntity;
import org.springframework.http.HttpHeaders;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
public class JobCommonUtil {

    private static final String backendInterfaceAddressGetStrategy = "/api/v1/config/schemas/strategy";

    public static int jobExport(String summerInterfaceAddress, JobExportMessage jobExportMessage, String cookieValue) {
        int taskId = TaskInfoStateType.error.getValue();

        try {
            String summerReturnMessage = HttpClientUtils.doPost(summerInterfaceAddress, new StringEntity(JSON.toJSONString(jobExportMessage), "UTF-8"), Collections.singletonMap(HttpHeaders.COOKIE, cookieValue));
            if (StringUtils.isBlank(summerReturnMessage)) {
                log.info("call interface:" + summerInterfaceAddress + ", bug get nothing!");
            }

            Map<String, Object> map = (Map<String, Object>) JSON.parseObject(summerReturnMessage, Map.class);
            if (map.get("body") instanceof Map) {
                Map<String, Object> body = (Map<String, Object>) map.get("body");
                if (body.get("task_id") != null) {
                    String task_id_String = body.get("task_id").toString();
                    if (StringUtils.isNotBlank(task_id_String)) {
                        taskId = Integer.parseInt(task_id_String);
                    }
                }
            }
        } catch (Exception e) {
            log.error("call jobExport error!", e);
        }

        return taskId;
    }

    public static boolean needSkipBasedOnStrategy(Integer repeatType, String dailyStrategy, String dcBackend) {

        try {
            if (RepeatType.REPEAT_DAILY.getValue().equals(repeatType)
                    && StringUtils.isNotBlank(dailyStrategy)
                    && !"unlimited".equals(dailyStrategy)) {

                // 调用php接口获取策略的具体日期
                String strategyString = HttpClientUtils.doGet(dcBackend + backendInterfaceAddressGetStrategy, null);

                if (StringUtils.isNotBlank(strategyString)) {
                    Map<String, Object> strategyMap = (Map<String, Object>) JSON.parseObject(strategyString, Map.class);

                    if (strategyMap.get("data") instanceof Map) {
                        strategyMap = (Map<String, Object>) strategyMap.get("data");

                        if (strategyMap.get(dailyStrategy) instanceof Map) {
                            strategyMap = (Map<String, Object>) strategyMap.get(dailyStrategy);

                            if (strategyMap.get("dates") != null) {
                                Object dates = strategyMap.get("dates");

                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                Calendar calendar = Calendar.getInstance(Locale.CHINA);
                                String dateStr = sdf.format(calendar.getTime());

                                if (dates instanceof String) {
                                    return !((String) dates).contains(dateStr);
                                } else if (dates instanceof Collection) {
                                    return !((Collection<?>) dates).contains(dateStr);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Verify task frequency, daily strategy error!", e);
        }

        return false;
    }

    public static void sleepSeconds(int seconds) {
        try {
            TimeUnit.SECONDS.sleep(seconds);
        } catch (InterruptedException ignored) {
            Thread.currentThread().interrupt(); // 可选，恢复中断状态
        }
    }
}

package com.dc.executor.service.impl;

import com.dc.repository.mysql.mapper.JobInfoMapper;
import com.dc.executor.service.JobService;
import com.dc.repository.mysql.model.JobInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class JobServiceImpl implements JobService {

    @Resource
    private JobInfoMapper jobInfoDao;


    @Override
    public String add(String job_cron, String app_name, String executor_handler, String param) {
        JobInfo job = jobInfoDao.loadByHandle(executor_handler);
        if (job != null) {
            return null;
        }
        String group_id = jobInfoDao.loadGroupId(app_name);
        JobInfo jobInfo = new JobInfo();
        jobInfo.setJobGroup(Integer.parseInt(group_id));
        jobInfo.setJobCron(job_cron);
        jobInfo.setJobDesc("任务");
        jobInfo.setAuthor("s");
        jobInfo.setExecutorHandler(executor_handler);
        jobInfo.setExecutorParam(param);
        jobInfo.setExecutorRouteStrategy("FIRST");
        jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        jobInfo.setExecutorTimeout(0);
        jobInfo.setExecutorFailRetryCount(0);
        jobInfo.setGlueType("BEAN");
        jobInfo.setGlueRemark("GLUE代码初始化");
        jobInfo.setGlueUpdatetime(new Date());
        jobInfo.setAddTime(new Date());
        jobInfo.setUpdateTime(new Date());
        jobInfo.setTriggerStatus(1);
        jobInfo.setTriggerLastTime(0);
        jobInfo.setTriggerNextTime(0);
        jobInfoDao.save(jobInfo);
        //return String.valueOf(jobInfo.getId());
        return "ADD SUCCESS";
    }

    @Override
    public String update(String job_cron, String executor_handler) {
        JobInfo jobInfo = jobInfoDao.loadByHandle(executor_handler);
        if (jobInfo == null) {
            return "Task does not exist";
        }
        jobInfoDao.update(job_cron, executor_handler);
        return "UPDATE SUCCESS";
    }

    @Override
    public String remove(String executor_handler) {
        JobInfo jobInfo = jobInfoDao.loadByHandle(executor_handler);

        if (jobInfo == null) {
            return "SUCCESS";
        }

        jobInfoDao.delete(executor_handler);
        return "SUCCESS";
    }

    @Override
    public String start( String executor_handler) {
        JobInfo jobInfo = jobInfoDao.loadByHandle(executor_handler);
        if (jobInfo == null) {
            return "Task does not exist";
        }
        jobInfoDao.updateStatus("1", executor_handler);
        return "START";
    }

    @Override
    public String stop( String executor_handler) {
        JobInfo jobInfo = jobInfoDao.loadByHandle(executor_handler);
        if (jobInfo == null) {
            return "Task does not exist";
        }
        jobInfoDao.updateStatus("0", executor_handler);
        return "STOP";
    }

}

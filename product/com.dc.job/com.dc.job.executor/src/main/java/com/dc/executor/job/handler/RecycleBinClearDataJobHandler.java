package com.dc.executor.job.handler;

import com.dc.executor.service.RecycleBinClearDataService;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.RcSqlMapper;
import com.dc.repository.mysql.mapper.SystemParamConfigMapper;
import com.dc.repository.mysql.model.RcSql;
import com.dc.repository.mysql.model.SystemParamConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class RecycleBinClearDataJobHandler extends AbstractJobHandler {

    @Resource
    private RcSqlMapper rcSqlDao;

    @Resource
    protected SystemParamConfigMapper systemParamConfigMapper;

    @Resource
    protected RecycleBinClearDataService recycleBinClearDataService;

    @XxlJob("RecycleBinClearDataJobHandler")
    public ReturnT<String> recycleBinClearDataJobHandler(String param) {
        try {
            log.info("Start clean up operation backup data!");

            int expiredDay = getExpiredDay();
            if (expiredDay <= 0) {
                return ReturnT.SUCCESS;
            }

            List<RcSql> expiredRcSqlList = rcSqlDao.getExpiredRcSqlList(expiredDay);

            Set<Long> rcBatchSet = new HashSet<>();
            for (RcSql rcSql : expiredRcSqlList) {
                Long rcBatchId = rcSql.getRc_batch_id();
                if (rcBatchId != null) {
                    rcBatchSet.add(rcBatchId);
                }
            }
            recycleBinClearDataService.deleteRcBatch(rcBatchSet);

            for (RcSql rcSql : expiredRcSqlList) {
                Long id = rcSql.getId();
                Long rcTableId = rcSql.getRc_table_id();
                String sessionId = rcSql.getSession_id();
                Integer transactionIndex = rcSql.getTransaction_index();

                // 获取rel表名和data表名
                String rcRelName = "rc_rel_" + rcTableId + "_" + sessionId;
                String rcDataName = "rc_" + rcTableId + "_" + sessionId;

                try {
                    recycleBinClearDataService.clearData(rcRelName, rcDataName, transactionIndex, id);
                } catch (Exception e) {
                    log.error("clear recycleBin data error!", e);
                }
            }
        } catch (Exception e) {
            log.error("call recycleBinClearDataJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

    public int getExpiredDay() {
        try {
            String pValue = getBackupDataRetentionFromMysql();
            if (StringUtils.isBlank(pValue)) {
                log.info("无法获取操作备份数据留存天数!");
                return 0;
            } else if ("0".equals(pValue.trim())) {
                log.info("操作备份数据留存天数不限制，无需清理!");
                return 0;
            }

            int backupDataRetention = Integer.parseInt(pValue);
            if (backupDataRetention < 0) {
                log.info("配置操作备份数据留存天数不合法!");
                return 0;
            }

            return backupDataRetention;
        } catch (Exception e ) {
            log.error("验证是否需要清理数据时出错!", e);
        }
        return 0;
    }

    public String getBackupDataRetentionFromMysql() {
        try {
            List<SystemParamConfig> systemParamConfigs = systemParamConfigMapper.getSystemParamConfig();
            for (SystemParamConfig paramConfig : systemParamConfigs) {
                if ("backup_data_retention".equals(paramConfig.getP_key())) {
                    return paramConfig.getP_value();
                }
            }
        } catch (Exception e) {
            log.error("get backup_data_retention from mysql error!", e);
        }
        return "";
    }

}

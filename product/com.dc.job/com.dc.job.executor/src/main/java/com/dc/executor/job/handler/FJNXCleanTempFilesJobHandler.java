package com.dc.executor.job.handler;

import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class FJNXCleanTempFilesJobHandler extends AbstractJobHandler {

    @XxlJob("FJNXCleanTempFilesJobHandler")
    public ReturnT<String> fjnxCleanTempFilesJobHandler(String param) throws Exception {
        try {
            log.info("Start FuJianNongXin Temp Files Clean!");
            // 福建农信需求:定时清理过期文件
            HttpClientUtils.doGet(jobConfig.getPath().getDcBackend() + "/" + param, null);
        } catch (Exception e) {
            log.error("call fjnxCleanTempFilesJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}

package com.dc.executor.job.handler;

import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SpiPostRequestJobHandler extends AbstractJobHandler {

    @XxlJob("SpiPostRequestJobHandler")
    public ReturnT<String> SpiPostRequestJobHandler(String param) throws Exception {
        try {
            log.info("Start request spi!");
            HttpClientUtils.doPost(jobConfig.getPath().getDcSpi() + "/" + param, new StringEntity(""));
        } catch (Exception e) {
            log.error("call spi error!", e);
        }
        return ReturnT.SUCCESS;
    }
}

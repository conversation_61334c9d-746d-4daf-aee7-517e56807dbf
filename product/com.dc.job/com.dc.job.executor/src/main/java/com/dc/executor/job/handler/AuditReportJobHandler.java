package com.dc.executor.job.handler;

import com.dc.config.ApiConfig;
import com.dc.executor.util.JobCommonUtil;
import com.dc.executor.model.AuditReportParam;
import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.enums.SqlExecuteStatusType;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.model.JobLog;
import com.dc.springboot.core.component.JSON;
import com.dc.utils.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class AuditReportJobHandler extends AbstractJobHandler {

    @XxlJob("AuditReportJobHandler")
    public ReturnT<String> auditReportJobHandler(String param) throws Exception {

        try {
            AuditReportParam auditReportParam = JSON.parseObject(param, AuditReportParam.class);

            if (JobCommonUtil.needSkipBasedOnStrategy(auditReportParam.getRepeatType(), auditReportParam.getDailyStrategy(), jobConfig.getPath().getDcBackend())) {
                return ReturnT.SUCCESS;
            }

            this.updateTriggerTime(auditReportParam.getLogId()); // 更新触发时间为真正得执行任务的时间

            JobLog load = jobLogMapper.load(auditReportParam.getLogId()); // 获取调度记录

            this.updateBeginTriggerStatus(load.getJobId(), auditReportParam.getLogId()); // 更新状态为执行中

            String url = jobConfig.getPath().getDcBackend() + ApiConfig.AUDIT_REPORT.getPath();
            log.info("Start post php interface: " + url);

            SqlExecuteStatusType sqlExecuteStatusType = SqlExecuteStatusType.success;
            try {
                String phpReturnMessage = HttpClientUtils.doPost(url, new StringEntity(param, "UTF-8"));
                if (StringUtils.isBlank(phpReturnMessage)) {
                    log.info("call interface:" + url + ", but get nothing!");
                }

                Map<String, Object> map = (Map<String, Object>) JSON.parseObject(phpReturnMessage, Map.class);
                if (map.get("status") instanceof Integer) {
                    Integer status = (Integer) map.get("status");
                    if (status != 0) {
                        sqlExecuteStatusType = SqlExecuteStatusType.fail;
                    } else {
                        // 只有成功(status=0)时，才去更新文件名
                        if (map.get("data") != null) {
                            jobLogMapper.updateFileDownloadLink(auditReportParam.getLogId(), map.get("data").toString());
                        }
                    }
                } else {
                    sqlExecuteStatusType = SqlExecuteStatusType.fail;
                }

                if (map.get("message") != null) {
                    DCJobLogger.log(map.get("message").toString());
                }
            } catch (Exception e) {
                log.error("调用php审计报表接口出错！", e);
                sqlExecuteStatusType = SqlExecuteStatusType.fail;
                DCJobLogger.log("调用php审计报表接口出错！");
            }

            this.updateFinalStatus(sqlExecuteStatusType, auditReportParam.getLogId(), auditReportParam.isRepeat(), load.getJobId()); // 更新最终状态

        } catch (Exception e) {
            log.error("call auditReportJobHandler error!", e);
        }

        return ReturnT.SUCCESS;
    }

}

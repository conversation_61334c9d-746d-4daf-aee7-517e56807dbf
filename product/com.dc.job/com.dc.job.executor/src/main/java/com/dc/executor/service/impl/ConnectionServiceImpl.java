package com.dc.executor.service.impl;

import com.dc.executor.component.JobMapper;
import com.dc.executor.config.JobConfig;
import com.dc.executor.service.PrivateSchemaService;
import com.dc.executor.util.ClientUtils;
import com.dc.executor.util.DCJobLogger;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.mapper.SystemParamConfigMapper;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.mysql.model.Schema;
import com.dc.repository.mysql.model.SystemParamConfig;
import com.dc.repository.redis.client.DataSourceClient;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.client.ProxyInternalClient;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.ExecutorData;
import com.dc.springboot.core.model.database.DataSourceMessage;
import com.dc.springboot.core.model.database.SysSchemaMessage;
import com.dc.springboot.core.model.database.ConnectionMessage;
import com.dc.springboot.core.model.database.SchemaInfo;
import com.dc.type.DatabaseType;
import com.dc.executor.service.ConnectionService;
import com.dc.utils.DateUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 把 所有的 获取 schema sql 放到 info 中，
 * 调用 proxy 接口，获取所有的 schema
 */
@Slf4j
@Service
public class ConnectionServiceImpl implements ConnectionService {

    final Gson gson = new GsonBuilder().create();
    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final long nd = 1000 * 60 * 60 * 24;
    private static final long nh = 1000 * 60 * 60;
    private static final long nm = 1000 * 60;
    private static final long ns = 1000;

    @Resource
    private JobConfig jobConfig;

    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;

    @Resource
    private SchemaMapper schemaMapper;

    @Resource
    private ProxyInternalClient proxyInternalClient;

    @Resource
    private BackendClient backendClient;

    @Resource
    private DataSourceClient dataSourceClient;

    @Resource
    private JobMapper jobMapper;

    @Resource
    private PrivateSchemaService PrivateSchemaService;

    // 定义常量，避免硬编码
    private static final String INSTANCE_SYNC_INTERVAL = "instance_sync_interval";

    @Resource
    protected SystemParamConfigMapper systemParamConfigMapper;

    /**
     * update Schema for every instance
     */
    @Override
    public void updateSchema() {

        SystemParamConfig config = systemParamConfigMapper.getSystemParamConfigByKey(INSTANCE_SYNC_INTERVAL);
        String jsonStr = Optional.ofNullable(config).map(SystemParamConfig::getP_value).orElse(null);

        AtomicInteger timeInterval = new AtomicInteger(0);
        if (Optional.ofNullable(jsonStr).isPresent()) {
            JsonObject json = gson.fromJson(jsonStr, new TypeToken<JsonObject>() {
            }.getType());
            int type = json.get("type").getAsInt();
            if (type == 1) {
                timeInterval.set(json.get("text").getAsInt());
            }
        }

        List<DatabaseConnection> allConnections = this.databaseConnectionMapper.getAllConnections()
                .stream()
                // redis、es不进行schema定时同步
                .filter(instance -> !Arrays.asList(DatabaseType.REDIS.getValue(), DatabaseType.ELASTIC_SEARCH.getValue()).contains(instance.getDb_type()))
                // 根据同步时间过滤实例
                .filter(instance -> {
                    //获取实例的字段 最后一次同步schema时间
                    //获取全局配置同步时间间隔
                    if (timeInterval.get() > 0 && Optional.of(instance).map(DatabaseConnection::getSync_schema_time).isPresent()) {
                        long timestampSeconds = Instant.now().getEpochSecond();
                        long timeDiff = timestampSeconds - instance.getSync_schema_time();
                        long timeSeconds = timeInterval.get() * 60 * 60L;
                        if (timeDiff < timeSeconds) {
                            DCJobLogger.log("同步时间间隔内无需同步" + instance.getInstance_name());
                            log.info("同步时间间隔内无需同步" + instance.getInstance_name());
                            return false;
                        }
                    }
                    return true;
                })
                // 编辑小于 5 分钟，不同步。
                .filter(instance -> {
                    if (instance.getGmt_modified() != null && !instance.getGmt_modified().isEmpty()) {
                        try {
                            Date modifiedDate = sdf.parse(instance.getGmt_modified());
                            Calendar calendar = Calendar.getInstance(Locale.CHINA);
                            Date nowDate = calendar.getTime();
                            boolean canUpdate = canUpdateSchema(modifiedDate, nowDate);
                            if (!canUpdate) {
                                log.info("Instance " + instance.getInstance_name() + " modification time is less than 5 minutes");
                                log.info("modified date: " + sdf.format(modifiedDate));
                                log.info("now date: " + sdf.format(nowDate));
                                DCJobLogger.log("Instance 【" + instance.getInstance_name() + "】 modification time is less than 5 minutes");
                                return false;
                            }
                        } catch (Exception e) {
                            log.error("judge can update schema error!", e);
                            DCJobLogger.log("judge can update schema error! 【" + instance.getInstance_name() + " 】");
                        }
                    }
                    return true;
                })
                .collect(toList());

        SysSchemaMessage message = new SysSchemaMessage();
        message.setDbTypes(allConnections.stream().map(DatabaseConnection::getDb_type).collect(toList()));
        message.setShowDef(1);

        Map<Integer, List<String>> sysSchema = backendClient.getSysSchema(Client.getClient(jobConfig.getPath().getDcBackend()), message);

        List<String> ids = new ArrayList<>();

        Map<Integer, ExecutorData> executorDataMap = backendClient.getExecutorDataMap(Client.getClient(jobConfig.getPath().getDcBackend()));

        for (DatabaseConnection instance : allConnections) {

            try {
                DCJobLogger.log("【" + instance.getInstance_name() + "】开始同步");
                log.info("【" + instance.getInstance_name() + "】开始同步");
                // mysql库中每个instance对应的所有schema
                List<Schema> schemaMysqlList = getSchemaByConnectionUniqueKey(instance.getUnique_key());

                Map<String, String> catalogMap = schemaMysqlList.stream()
                        .filter(schema -> DatabaseType.getCatalogDatabaseIntegerValueList().contains(schema.getDb_type()))
                        .filter(schema -> StringUtils.isBlank(schema.getCatalog_name()))
                        .collect(Collectors.toMap(Schema::getSchema_name, Schema::getUnique_key, (key1, key2) -> key2));

                // 连接数据库，获取实时schema
                List<Schema> schemaDBList = getDbSchema(instance, catalogMap, executorDataMap);

                if (schemaDBList.isEmpty()) {
                    DCJobLogger.log("Instance 【" + instance.getInstance_name() + "】 获取schema数量为0");
                    continue;
                }

                // 校正是否是系统Schema
                schemaDBList.forEach(schema -> {
                    List<String> schemas = sysSchema != null ? sysSchema.get(schema.getDb_type()) : new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(schemas)) {
                        if (StringUtils.isNotBlank(schema.getCatalog_name())) {
                            schema.setIs_sys(schemas.contains(schema.getCatalog_name()) ? 1 : 0);
                        } else {
                            schema.setIs_sys(schemas.contains(schema.getSchema_name()) ? 1 : 0);
                        }
                    }
                });

                // 新增schema
                List<Schema> addList = schemaDBList.stream().filter(item -> !schemaMysqlList.contains(item)).collect(toList());
                insertSchema(instance, addList);

                // 删除schema
                List<Schema> reduceList = schemaMysqlList.stream().filter(item -> !schemaDBList.contains(item)).collect(toList());
                deleteSchema(instance, reduceList);

                if (ObjectUtils.isNotEmpty(reduceList)) {
                    for (Schema reduceSchema : reduceList) {
                        String uniqueKey = instance.getUnique_key() + "^" + reduceSchema.getUnique_key();
                        ids.add(uniqueKey);
                    }
                }

                for (Schema mysqlSchema : schemaMysqlList) {
                    for (Schema dbSchema : schemaDBList) {
                        if (mysqlSchema.equals(dbSchema)) {
                            // 更新mysql库中的Schema节点的tableCount
                            if (!mysqlSchema.getTable_count().equals(dbSchema.getTable_count())) {
                                this.schemaMapper.updateTableCountById(mysqlSchema.getUnique_key(), dbSchema.getTable_count());
                                log.info("Update tableCount for schema: " + mysqlSchema);
                            }
                            // 更新mysql库中的Schema节点的Charset
                            if (!mysqlSchema.getCharset().trim().equals(dbSchema.getCharset().trim())) {
                                this.schemaMapper.updateSchemaCharsetById(mysqlSchema.getUnique_key(), dbSchema.getCharset().trim());
                                log.info("Update Schema Charset for schema: " + mysqlSchema);
                            }
                            // 更新是否是系统Schema
                            if (!mysqlSchema.getIs_sys().equals(dbSchema.getIs_sys())) {
                                this.schemaMapper.updateSchemaIsSysById(mysqlSchema.getUnique_key(), dbSchema.getIs_sys());
                                log.info("Update Schema isSys for schema: " + mysqlSchema);
                            }
                            break;
                        }
                    }
                }
                //修改私有schema 标识
                this.PrivateSchemaService.updatePrivateSchema(instance, schemaMysqlList);

                //更新下次同步schema时间
                instance.setSync_schema_time(new Date().getTime() / 1000);
                instance.setGmt_modified(DateUtil.ymd_hms_sdf_1.format(new Date()));
                databaseConnectionMapper.updateById(instance);
                DCJobLogger.log("【" + instance.getInstance_name() + "】同步结束");
                log.info("【" + instance.getInstance_name() + "】同步结束");
            } catch (Exception e) {
                log.error("instance 【" + instance.getInstance_name() + "】，schema.refresh.failed。");
            }
        }

        if (!ids.isEmpty()) {
            Map<String, List<String>> map = new HashMap<>(16);
            map.put("ids", ids);
            Object obj = backendClient.removeSchema(Client.getClient(jobConfig.getPath().getDcBackend()), map);
            log.info("请求php删除schema结果集：" + JSON.toJSONString(obj));
        }
    }

    private List<Schema> getDbSchema(DatabaseConnection instance, Map<String, String> catalogMap, Map<Integer, ExecutorData> executorDataMap) {
        List<Schema> schemaList = new ArrayList<>();

        try {
            // 获取数据库真实schema
            ConnectionMessage connectionMessage = new ConnectionMessage();
            connectionMessage.setConnectionConfig(jobMapper.toDatabaseConnectionDto(instance).buildConnectionConfig(null, null));
            ExecutorData executorData = executorDataMap.get(instance.getExecutor());
            log.info("调用proxy地址为:" + instance.getExecutor());
            List<SchemaInfo> schemaInfos = proxyInternalClient.schemaInfos(Client.getClient(executorData.getDcIceage()), connectionMessage);

            for (SchemaInfo schemaInfo : schemaInfos) {
                String uniqueKey = UUID.randomUUID().toString().replaceAll("-", "");
                Schema schema = buildSchema(schemaInfo, instance, uniqueKey);
                schema.setDef_dbo_name(DatabaseType.get3FDatabaseIntegerValueList().contains(instance.getDb_type()) ? schemaInfo.getDefDboName() : "");
                schema.setPid("");
                schema.setCatalog_name(null);
                if (schemaInfo.getInnerSchemaInfoList() != null) {
                    List<SchemaInfo> innerSchemaInfoList = schemaInfo.getInnerSchemaInfoList();
                    for (SchemaInfo innerSchemaInfo : innerSchemaInfoList) {
                        String innerUniqueKey = UUID.randomUUID().toString().replaceAll("-", "");
                        Schema innerSchema = buildSchema(innerSchemaInfo, instance, innerUniqueKey);
                        innerSchema.setDef_dbo_name(innerSchemaInfo.getDefDboName());
                        innerSchema.setPid(StringUtils.isNotBlank(catalogMap.get(schemaInfo.getSchemaName())) ? catalogMap.get(schemaInfo.getSchemaName()) : uniqueKey);
                        innerSchema.setCatalog_name(schemaInfo.getSchemaName());
                        schemaList.add(innerSchema);
                    }
                }
                schemaList.add(schema);
            }
        } catch (Exception e) {
            log.error("get db schema error!", e);
        }

        return schemaList;
    }

    public Schema buildSchema(SchemaInfo schemaInfo, DatabaseConnection instance, String uniqueKey) {
        Schema schema = new Schema();
        schema.setDb_type(instance.getDb_type());
        schema.setSchema_name(schemaInfo.getSchemaName());
        schema.setTable_count(schemaInfo.getTableCount());
        schema.setCharset((schemaInfo.getCharset()).toUpperCase());
        schema.setIs_sys(schemaInfo.getIsSystem());
        schema.setConnect_id(instance.getUnique_key());
        schema.setGmt_create(new Date());
        schema.setGmt_modified(new Date());
        schema.setUnique_key(uniqueKey);
        schema.setIs_private(0);
        return schema;
    }

    private void insertSchema(DatabaseConnection instance, List<Schema> addList) {
        if (addList.size() > 0) {
            log.info("Insert schema start: " + addList.size() + " in total");
            try {
                schemaMapper.insertBatchSomeColumn(addList);
            } catch (Exception e) {
                log.error("定时同步schema，插入schema失败！", e);
            }
            this.refreshDatasource(instance.getUnique_key());
        } else {
            log.info("Insert schema : " + addList.size() + " in total");
        }

    }

    private void deleteSchema(DatabaseConnection instance, List<Schema> reduceList) {
        if (reduceList.size() > 0) {
            log.info("Delete schema start: " + reduceList.size() + " in total");
        }
        try {
            if (CollectionUtils.isNotEmpty(reduceList)) {
                schemaMapper.deleteBatchIds(reduceList.stream().map(Schema::getId).collect(toList()));
            }
        } catch (Exception e) {
            log.error("定时同步schema，删除schema失败！", e);
        }
        if (reduceList.size() > 0) {
            this.refreshDatasource(instance.getUnique_key());
        }
    }

    private List<Schema> getSchemaByConnectionUniqueKey(String uniqueKey) {
        Map<String, Object> map = new HashMap<>();
        map.put("connect_id", uniqueKey);
        map.put("is_delete", 0);
        return this.schemaMapper.selectByMap(map);
    }

    private boolean canUpdateSchema(Date modifiedDate, Date nowDate) {
        long diff = nowDate.getTime() - modifiedDate.getTime();
        if (diff <= 0) {
            return true;
        } else {
            long day = diff / nd;
            long hour = diff % nd / nh;
            long min = diff % nd % nh / nm;
//            long sec = diff % nd % nh % nm / ns;
            if (day > 0 || hour > 0 || min >= 5) {
                return true;
            } else {
                return false;
            }
        }

    }

    private void refreshDatasource(String uniqueKey) {
        log.info("Post summer to refresh datasource.");

        DataSourceMessage message = new DataSourceMessage();
        message.setConnectionId(uniqueKey);
        dataSourceClient.refresh(message);
    }

}

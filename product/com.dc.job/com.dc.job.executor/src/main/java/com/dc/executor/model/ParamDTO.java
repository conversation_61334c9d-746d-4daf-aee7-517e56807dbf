package com.dc.executor.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ParamDTO {

    @SerializedName("log_id")
    private long logId;
    @SerializedName("job_id")
    private Integer jobId;
    @SerializedName("connect_id")
    private String connectId; // 实例 unique_key
    private Integer dbType; // 1:oracle;2:mysql
    private Integer operation; // 1:exp;2:imp
    private String schema;
    private String catalog; // pg数据库的catalogName
    private String sourceSchema;
    private String targetSchema;
    private boolean force; // 报错继续执行
    private boolean fullTable = true; // 导出全部表
    private List<String> tables = new ArrayList<>();
    private List<String> views = new ArrayList<>();
    private Integer exportContent; // 导出内容: 1:数据和结构;2:结构
    private boolean isCompress = false; // 压缩
    private boolean isAuthorization = false; // 授权
    private boolean isAgreement = false; // 一致
    private boolean isIndexes = false; // 索引
    private boolean isConstraint = false; // 约束
    private boolean isDirect = false; // 直接
    private boolean isTrigger = false; // 触发器
    private boolean isRoutines = false; // 导出存储过程以及自定义函数
    private boolean isEvents = false; // 导出事件
    private boolean isCreateSchema = false; // 包含Schema创建语句
    private String dumpName;
    private String logName;
    private String dumpPath; // 导入文件路径
    private String charset; // 导入文件编码
    private String dumpDownloadName;

    // postGreSql
    private boolean noOwner; // 不包含所有者
    private boolean noPrivileges; // 不包含权限
    private boolean inserts; // 使用insert代替copy
    private boolean noCreate; // 不包含create database
    private boolean noClean; // 不包含drop database

}

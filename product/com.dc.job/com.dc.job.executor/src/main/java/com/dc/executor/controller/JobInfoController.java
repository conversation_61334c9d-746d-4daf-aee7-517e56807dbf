package com.dc.executor.controller;

import com.dc.executor.service.JobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;


@SuppressWarnings("all")
@RestController
@RequestMapping("/jobinfo")
public class JobInfoController {

    @Autowired
    private JobService jobService;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public String add(String job_cron, String app_name, String job_handler, String param) {
        return jobService.add( job_cron, app_name, job_handler, param);
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ResponseBody
    public String update(String job_cron, String executor_handler) {
        return jobService.update( job_cron, executor_handler);
    }

    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @ResponseBody
    public String remove(String executor_handler) {
        return jobService.remove( executor_handler);
    }

    @RequestMapping(value = "/start", method = RequestMethod.PUT)
    @ResponseBody
    public String start(String executor_handler) {
        return jobService.start( executor_handler);
    }

    @RequestMapping(value = "/stop", method = RequestMethod.PUT)
    @ResponseBody
    public String stop(String executor_handler) {
        return jobService.stop( executor_handler);
    }

}
